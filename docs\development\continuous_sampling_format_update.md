# 连续采样模式输出格式统一化修改

## 修改概述

**修改时间**: 2025-01-11  
**修改文件**: `sysFunction/Timer_APP.c`  
**修改目的**: 统一连续采样模式和单次采集命令的超阈值标识格式

## 问题描述

在原有实现中，连续采样模式和单次采集命令(`command:get_data`)的超阈值标识格式不一致：

### 原有格式差异

**单次采集格式** (正确格式):
```
report:ch0*=25.67,ch1=12.34,ch2*=89.12
```

**连续采样格式** (需要修改):
```
report:2025-01-11 14:30:25 ch0=25.67,ch1=12.34,ch2=89.12 OverLimit
[ch0:20.00][ch2:80.00]
```

## 修改方案

### 目标格式
统一为在超阈值通道名后添加`*`标识：
```
report:2025-01-11 14:30:25 ch0*=25.67,ch1=12.34,ch2*=89.12
```

### 代码修改详情

#### 1. 超限情况输出格式修改
**位置**: `Timer_APP.c` 第171-180行

**修改前**:
```c
// 正常模式：超限时也按统一格式输出，但在数据后添加超限标识
rs485_printf("report:20%02x-%02x-%02x %02x:%02x:%02x ch0=%.2f,ch1=%.2f,ch2=%.2f OverLimit",
       rtc_time.year, rtc_time.month, rtc_time.date,
       rtc_time.hour, rtc_time.minute, rtc_time.second,
       ch0_actual, ch1_actual, ch2_actual);

// 标注具体哪些通道超限
if(ch0_over_limit) rs485_printf("[ch0:%.2f]", ch0_limit);
if(ch1_over_limit) rs485_printf("[ch1:%.2f]", ch1_limit);
if(ch2_over_limit) rs485_printf("[ch2:%.2f]", ch2_limit);
rs485_printf("\r\n");
```

**修改后**:
```c
// 正常模式：超限通道在通道名后加*（与单次采集格式一致）
rs485_printf("report:20%02x-%02x-%02x %02x:%02x:%02x ch0%s=%.2f,ch1%s=%.2f,ch2%s=%.2f\r\n",
       rtc_time.year, rtc_time.month, rtc_time.date,
       rtc_time.hour, rtc_time.minute, rtc_time.second,
       ch0_over_limit ? "*" : "", ch0_actual,
       ch1_over_limit ? "*" : "", ch1_actual,
       ch2_over_limit ? "*" : "", ch2_actual);
```

#### 2. 正常情况输出格式修改
**位置**: `Timer_APP.c` 第200-209行

**修改前**:
```c
// 正常模式：按要求的格式输出三通道数据
rs485_printf("report:20%02x-%02x-%02x %02x:%02x:%02x ch0=%.2f,ch1=%.2f,ch2=%.2f\r\n",
       rtc_time.year, rtc_time.month, rtc_time.date,
       rtc_time.hour, rtc_time.minute, rtc_time.second,
       ch0_actual, ch1_actual, ch2_actual);
```

**修改后**:
```c
// 正常模式：统一格式输出三通道数据（与超限格式保持一致）
rs485_printf("report:20%02x-%02x-%02x %02x:%02x:%02x ch0%s=%.2f,ch1%s=%.2f,ch2%s=%.2f\r\n",
       rtc_time.year, rtc_time.month, rtc_time.date,
       rtc_time.hour, rtc_time.minute, rtc_time.second,
       ch0_over_limit ? "*" : "", ch0_actual,
       ch1_over_limit ? "*" : "", ch1_actual,
       ch2_over_limit ? "*" : "", ch2_actual);
```

## 修改效果

### 修改后的输出示例

**正常情况**:
```
report:2025-01-11 14:30:25 ch0=25.67,ch1=12.34,ch2=89.12
```

**ch0和ch2超限情况**:
```
report:2025-01-11 14:30:25 ch0*=25.67,ch1=12.34,ch2*=89.12
```

**仅ch1超限情况**:
```
report:2025-01-11 14:30:25 ch0=25.67,ch1*=12.34,ch2=89.12
```

## 技术优势

1. **格式统一**: 连续采样和单次采集使用相同的超阈值标识格式
2. **简洁明了**: 直接在通道名后标识，无需额外的标注信息
3. **易于解析**: 上位机软件可以使用统一的解析逻辑
4. **向后兼容**: 不影响正常数据的解析逻辑

## 相关功能保持不变

- 超限数据仍会写入`overlimit/`文件夹
- LED2超限指示功能保持不变
- 加密模式的输出格式保持不变
- SD卡数据存储格式保持不变

## 测试建议

1. **功能测试**: 验证超限和正常情况下的输出格式
2. **边界测试**: 测试单通道、多通道超限的各种组合
3. **兼容性测试**: 确认上位机软件能正确解析新格式
4. **长期测试**: 验证连续采样模式下的稳定性

---

*此修改确保了系统输出格式的一致性，提升了用户体验和系统的专业性。*
