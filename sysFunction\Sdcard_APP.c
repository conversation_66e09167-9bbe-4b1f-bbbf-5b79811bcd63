#include "Sdcard_APP.h"
#include "RTC_APP.h"
#include "Flash_APP.h"
#include "ff.h"
#include "diskio.h"
#include "gd32f4xx.h"
#include "string.h"
#include "stdio.h"
#include "stdlib.h"

FIL fdst;                                        // 文件对象
FATFS fs;                                        // 文件系统对象
UINT bw;                                         // 写入字节数
BYTE filebuffer[128];                            // 写入缓冲区

// 保留原有的记录状态管理
record_state_t record_state = RECORD_STATE_IDLE; // 记录状态
static uint8_t current_record_count = 0;         // 当前文件记录计数
static uint8_t overlimit_record_count = 0;       // 超限文件记录计数
static uint8_t hidedata_record_count = 0;        // 加密数据文件记录计数
static uint8_t log_record_count = 0;             // log数据文件记录计数
uint8_t log_file_created = 0;                   // 全局变量，用于在SD卡初始化时重置
FIL overlimit_file;                              // 超限数据文件对象
FIL hidedata_file;                               // 加密数据文件对象
FIL log_file;                                    // log数据文件对象

// 文件名管理变量
static char current_filename[MAX_FILENAME_LEN] = {0};  // 当前采样文件名
static uint8_t filename_initialized = 0;               // 文件名初始化标志

// 超阈值数据文件名管理变量
static char current_overlimit_filename[MAX_FILENAME_LEN] = {0};  // 当前超阈值文件名
static uint8_t overlimit_filename_initialized = 0;              // 超阈值文件名初始化标志

/**
 * @brief NVIC配置
 */
void nvic_config(void)
{
    nvic_priority_group_set(NVIC_PRIGROUP_PRE1_SUB3); // 设置中断优先级分组
    nvic_irq_enable(SDIO_IRQn, 0, 0);                 // 使能SDIO中断，优先级为0
}

/**
 * @brief 检测TF卡是否插入
 * @return uint8_t: 1表示TF卡已插入，0表示TF卡未插入
 */
uint8_t Check_TF_Card(void)
{
    uint16_t k = 3;
    DSTATUS stat = 0;

    //检测是否存在
    do {
        stat = disk_initialize(0);
    } while((stat != 0) && (--k));
    if(stat == 0)
    {
        return 1; // SD卡存在
    }
    else
    {
        return 0; // SD卡不存在或初始化失败
    }
}





/**
 * @brief 初始化数据记录功能
 * @return uint8_t: 0表示成功，非0表示失败
 */
uint8_t Init_Data_Recording(void)
{
    uint16_t k = 5;
    DSTATUS stat = 0;
    DIR dir;

    // 完全按照例程的初始化序列
    nvic_config();  // 配置中断控制器
    do
    {
        stat = disk_initialize(0);  // 初始化SD卡（设备号0）
    } while((stat != 0) && (--k)); // 如果初始化失败，重试最多k次

    f_mount(0, &fs);  // 挂载SD卡的文件系统（设备号0）

    if(RES_OK == stat)  // 返回挂载结果（FR_OK 表示成功）
    {
        FRESULT res = f_mkdir("0:/sample");
        if(res != FR_OK && res != FR_EXIST)
        {
            res = f_mkdir("sample");
        }
        // 创建overlimit文件夹
        res = f_mkdir("0:/overlimit");
        if(res != FR_OK && res != FR_EXIST)
        {
            res = f_mkdir("overlimit");
        }

        // 创建hideData文件夹
        res = f_mkdir("0:/hideData");
        if(res != FR_OK && res != FR_EXIST)
        {
            res = f_mkdir("hideData");
        }

        // 检查log文件夹是否存在来判断是否第一次插入TF卡
        FRESULT log_check = f_opendir(&dir, "0:/log");
        if(log_check != FR_OK)
        {
            // 第一次插入TF卡，创建log文件夹和log0.txt
            res = f_mkdir("0:/log");
            if(res != FR_OK && res != FR_EXIST)
            {
                res = f_mkdir("log");
            }

            // 创建log0.txt并写入Flash缓存
            Create_Log0_File();
            if(Check_Cached_Log_In_Flash())
            {
                Write_Cached_Log_To_Log0();
            }

            // 设置当前log ID为1（后续新数据写入log1.txt）
            current_log_id = 1;
        }
        else
        {
            // TF卡重新插入，log文件夹已存在
            // 获取下一个可用的log ID
            current_log_id = Get_Next_Log_ID_From_SD();
        }

        // 重置log文件创建标志
        extern uint8_t log_file_created;
        log_file_created = 0;

        record_state = RECORD_STATE_IDLE;
        return 0;
    }
    else
    {
        record_state = RECORD_STATE_ERROR;
        return 1;
    }
}

/**
 * @brief 生成基于时间戳的文件名
 * @param filename: 输出文件名缓冲区
 * @param max_len: 缓冲区最大长度
 */
static void Generate_Filename(char* filename, uint32_t max_len)
{
    rtc_parameter_struct rtc_time;
    rtc_current_time_get(&rtc_time);
    snprintf(filename, max_len, "0:/sample/sampleData20%02x%02x%02x%02x%02x%02x.txt",
             rtc_time.year, rtc_time.month, rtc_time.date,
             rtc_time.hour, rtc_time.minute, rtc_time.second);
}

/**
 * @brief 创建采样数据文件
 * @return uint8_t: 0表示成功，非0表示失败
 */
uint8_t Create_Sample_File(void)
{
    // 检查记录状态
    if(record_state == RECORD_STATE_ERROR)
    {
        return 1;
    }

    // 生成初始文件名并存储到变量
    Generate_Filename(current_filename, sizeof(current_filename));

    // 设置标志
    filename_initialized = 1;

    // 设置记录状态为活跃
    record_state = RECORD_STATE_ACTIVE;

    // 重置记录计数
    current_record_count = 0;
    return 0;
}

/**
 * @brief 写入采样数据到文件
 * @param timestamp: 时间戳字符串
 * @param voltage: 电压值
 * @param ratio: 变比值
 * @return uint8_t: 0表示成功，非0表示失败
 */
uint8_t Write_Sample_Data(const char* timestamp, float voltage, float ratio)
{
    FRESULT result;
    char data_line[DATA_BUFFER_SIZE];

    // 检查记录状态
    if(record_state != RECORD_STATE_ACTIVE)
    {
        return 1;
    }

    // 检查是否需要创建新文件（每10条数据）
    if(current_record_count >= MAX_RECORDS_PER_FILE)
    {
        // 生成新文件名并重置计数
        Generate_Filename(current_filename, sizeof(current_filename));
        current_record_count = 0;
        filename_initialized = 1;
    }

    // 如果文件名未初始化，生成初始文件名
    if(!filename_initialized)
    {
        Generate_Filename(current_filename, sizeof(current_filename));
        filename_initialized = 1;
    }
    snprintf(data_line, sizeof(data_line), "%s %.1fV\r\n", timestamp, voltage);
    strncpy((char*)filebuffer, data_line, sizeof(filebuffer)-1);
    filebuffer[sizeof(filebuffer)-1] = '\0';
    result = f_open(&fdst, current_filename, FA_OPEN_ALWAYS | FA_WRITE);
    if(result == FR_OK)
    {
        result = f_lseek(&fdst, f_size(&fdst));
    }
    else
    {
        rtc_parameter_struct rtc_time;
        rtc_current_time_get(&rtc_time);
        char root_filename[MAX_FILENAME_LEN];
        snprintf(root_filename, sizeof(root_filename), "0:/sampleData20%02x%02x%02x%02x%02x%02x.txt",
                 rtc_time.year, rtc_time.month, rtc_time.date,
                 rtc_time.hour, rtc_time.minute, rtc_time.second);

        result = f_open(&fdst, root_filename, FA_CREATE_ALWAYS | FA_WRITE);
        if(result == FR_OK)
        {
            strncpy(current_filename, root_filename, sizeof(current_filename)-1);
            current_filename[sizeof(current_filename)-1] = '\0';
        }
    }

    if(FR_OK == result)
    {
        // 写入数据到文件
        result = f_write(&fdst, filebuffer, strlen((char*)filebuffer), &bw);

        // 立即关闭文件
        f_close(&fdst);

        if(FR_OK == result)
        {
            // 增加记录计数
            current_record_count++;
            return 0;
        }
        else
        {
            record_state = RECORD_STATE_ERROR;
            return 1;
        }
    }
    else
    {
        record_state = RECORD_STATE_ERROR;
        return 1;
    }
}

/**
 * @brief 关闭采样数据文件
 * @return uint8_t: 0表示成功，非0表示失败
 */
uint8_t Close_Sample_File(void)
{
    if(record_state != RECORD_STATE_ACTIVE)
    {
        return 1;
    }
    // 重置record_state为RECORD_STATE_IDLE
    record_state = RECORD_STATE_IDLE;

    // 重置为0
    current_record_count = 0;

    // 清空current_filename
    memset(current_filename, 0, sizeof(current_filename));

    // 重置标志
    filename_initialized = 0;

    return 0;
}

/**
 * @brief 写入三通道采样数据到SD卡
 * @param timestamp: 时间戳字符串
 * @param ch0_value: 通道0实际值 (电压)
 * @param ch1_value: 通道1实际值 (电流)
 * @param ch2_value: 通道2实际值 (电阻)
 * @param ch0_ratio: 通道0变比
 * @param ch1_ratio: 通道1变比
 * @param ch2_ratio: 通道2变比
 * @return uint8_t: 0表示成功，非0表示失败
 */
uint8_t Write_Sample_Data_MultiChannel(const char* timestamp,
                                      float ch0_value, float ch1_value, float ch2_value,
                                      float ch0_ratio, float ch1_ratio, float ch2_ratio)
{
    FRESULT result;
    char data_line[DATA_BUFFER_SIZE];

    // 检查记录状态
    if(record_state != RECORD_STATE_ACTIVE)
    {
        return 1;
    }

    // 检查是否需要创建新文件（每10条数据）
    if(current_record_count >= MAX_RECORDS_PER_FILE)
    {
        // 生成新文件名并重置计数
        Generate_Filename(current_filename, sizeof(current_filename));
        current_record_count = 0;
        filename_initialized = 1;
    }

    // 如果文件名未初始化，生成初始文件名
    if(!filename_initialized)
    {
        Generate_Filename(current_filename, sizeof(current_filename));
        filename_initialized = 1;
    }

    // 格式化三通道数据行
    snprintf(data_line, sizeof(data_line),
             "%s ch0=%.2fV(%.2f) ch1=%.2fA(%.2f) ch2=%.2fΩ(%.2f)\r\n",
             timestamp, ch0_value, ch0_ratio, ch1_value, ch1_ratio, ch2_value, ch2_ratio);

    // 写入数据（使用与原函数相同的方式）
    strncpy((char*)filebuffer, data_line, sizeof(filebuffer)-1);
    filebuffer[sizeof(filebuffer)-1] = '\0';

    result = f_open(&fdst, current_filename, FA_OPEN_ALWAYS | FA_WRITE);
    if(result == FR_OK)
    {
        result = f_lseek(&fdst, f_size(&fdst));
    }
    else
    {
        rtc_parameter_struct rtc_time;
        rtc_current_time_get(&rtc_time);
        char root_filename[MAX_FILENAME_LEN];
        snprintf(root_filename, sizeof(root_filename), "0:/sampleData20%02x%02x%02x%02x%02x%02x.txt",
                 rtc_time.year, rtc_time.month, rtc_time.date,
                 rtc_time.hour, rtc_time.minute, rtc_time.second);

        result = f_open(&fdst, root_filename, FA_CREATE_ALWAYS | FA_WRITE);
        if(result == FR_OK)
        {
            strncpy(current_filename, root_filename, sizeof(current_filename)-1);
            current_filename[sizeof(current_filename)-1] = '\0';
        }
    }

    if(FR_OK == result)
    {
        UINT data_len = strlen((char*)filebuffer);
        result = f_write(&fdst, filebuffer, data_len, &bw);
        f_sync(&fdst); // 立即同步确保数据写入
        f_close(&fdst);

        if(FR_OK == result)
        {
            current_record_count++;
            return 0;
        }
    }
    return 1;
}

/**
 * @brief 生成超限数据文件名
 * @param filename: 输出文件名缓冲区
 * @param max_len: 缓冲区最大长度
 */
void Generate_OverLimit_Filename(char* filename, uint32_t max_len)
{
    rtc_parameter_struct rtc_time;
    rtc_current_time_get(&rtc_time);

    // 格式：overLimit{datetime}.txt，其中datetime为14位连续数字
    snprintf(filename, max_len, "0:/overlimit/overLimit20%02x%02x%02x%02x%02x%02x.txt",
             rtc_time.year, rtc_time.month, rtc_time.date,
             rtc_time.hour, rtc_time.minute, rtc_time.second);
}



/**
 * @brief 写入超限数据到文件
 * @param timestamp: 时间戳字符串
 * @param voltage: 电压值
 * @param limit: 阈值
 * @return uint8_t: 0表示成功，非0表示失败
 */
uint8_t Write_OverLimit_Data(const char* timestamp, float voltage, float limit)
{
    FRESULT result;
    char data_line[DATA_BUFFER_SIZE];
    if(overlimit_record_count >= MAX_RECORDS_PER_FILE)
    {
        Generate_OverLimit_Filename(current_overlimit_filename, sizeof(current_overlimit_filename));
        overlimit_record_count = 0;
        overlimit_filename_initialized = 1;
    }
    if(!overlimit_filename_initialized)
    {
        Generate_OverLimit_Filename(current_overlimit_filename, sizeof(current_overlimit_filename));
        overlimit_filename_initialized = 1;
    }
    snprintf(data_line, sizeof(data_line), "%s %.1fV limit %.2f\r\n",
             timestamp, voltage, limit);
    strncpy((char*)filebuffer, data_line, sizeof(filebuffer)-1);
    filebuffer[sizeof(filebuffer)-1] = '\0';
    result = f_open(&overlimit_file, current_overlimit_filename, FA_OPEN_ALWAYS | FA_WRITE);

    if(result == FR_OK)
    {
        result = f_lseek(&overlimit_file, f_size(&overlimit_file));
    }
    else
    {
        rtc_parameter_struct rtc_time;
        rtc_current_time_get(&rtc_time);
        char root_filename[MAX_FILENAME_LEN];
        snprintf(root_filename, sizeof(root_filename), "0:/overLimit20%02x%02x%02x%02x%02x%02x.txt",
                 rtc_time.year, rtc_time.month, rtc_time.date,
                 rtc_time.hour, rtc_time.minute, rtc_time.second);

        result = f_open(&overlimit_file, root_filename, FA_CREATE_ALWAYS | FA_WRITE);
        if(result == FR_OK)
        {
            strncpy(current_overlimit_filename, root_filename, sizeof(current_overlimit_filename)-1);
            current_overlimit_filename[sizeof(current_overlimit_filename)-1] = '\0';
        }
    }

    if(FR_OK == result)
    {
        // 写入数据到文件
        result = f_write(&overlimit_file, filebuffer, strlen((char*)filebuffer), &bw);

        // 立即关闭文件
        f_close(&overlimit_file);

        if(FR_OK == result)
        {
            // 增加记录计数
            overlimit_record_count++;
            return 0;
        }
        else
        {
            return 1;
        }
    }
    else
    {
        return 1;
    }
}

/**
 * @brief 关闭超限数据文件
 * @return uint8_t: 0表示成功，非0表示失败
 */
uint8_t Close_OverLimit_File(void)
{
    // 重置为0
    overlimit_record_count = 0;

    // 清空current_overlimit_filename
    memset(current_overlimit_filename, 0, sizeof(current_overlimit_filename));

    // 重置标志
    overlimit_filename_initialized = 0;

    return 0;
}

// Unicode处理函数,启用长文件名支持所需
#if _USE_LFN

// Code page 437 to Unicode conversion table
static const WCHAR unicode_table[] = {
    0x00C7, 0x00FC, 0x00E9, 0x00E2, 0x00E4, 0x00E0, 0x00E5, 0x00E7,
    0x00EA, 0x00EB, 0x00E8, 0x00EF, 0x00EE, 0x00EC, 0x00C4, 0x00C5,
    0x00C9, 0x00E6, 0x00C6, 0x00F4, 0x00F6, 0x00F2, 0x00FB, 0x00F9,
    0x00FF, 0x00D6, 0x00DC, 0x00A2, 0x00A3, 0x00A5, 0x20A7, 0x0192,
    0x00E1, 0x00ED, 0x00F3, 0x00FA, 0x00F1, 0x00D1, 0x00AA, 0x00BA,
    0x00BF, 0x2310, 0x00AC, 0x00BD, 0x00BC, 0x00A1, 0x00AB, 0x00BB,
    0x2591, 0x2592, 0x2593, 0x2502, 0x2524, 0x2561, 0x2562, 0x2556,
    0x2555, 0x2563, 0x2551, 0x2557, 0x255D, 0x255C, 0x255B, 0x2510,
    0x2514, 0x2534, 0x252C, 0x251C, 0x2500, 0x253C, 0x255E, 0x255F,
    0x255A, 0x2554, 0x2569, 0x2566, 0x2560, 0x2550, 0x256C, 0x2567,
    0x2568, 0x2564, 0x2565, 0x2559, 0x2558, 0x2552, 0x2553, 0x256B,
    0x256A, 0x2518, 0x250C, 0x2588, 0x2584, 0x258C, 0x2590, 0x2580,
    0x03B1, 0x00DF, 0x0393, 0x03C0, 0x03A3, 0x03C3, 0x00B5, 0x03C4,
    0x03A6, 0x0398, 0x03A9, 0x03B4, 0x221E, 0x03C6, 0x03B5, 0x2229,
    0x2261, 0x00B1, 0x2265, 0x2264, 0x2320, 0x2321, 0x00F7, 0x2248,
    0x00B0, 0x2219, 0x00B7, 0x221A, 0x207F, 0x00B2, 0x25A0, 0x00A0
};

WCHAR ff_convert(WCHAR src, UINT dir)
{
    WCHAR c;

    if (src < 0x80) 
    {
        c = src;
    }
    else
    {
        if (dir)  
        {
            c = (src >= 0x100) ? 0 : unicode_table[src - 0x80];
        }
        else 
        {
            for (c = 0; c < 0x80; c++)
            {
                if (src == unicode_table[c]) break;
            }
            c = (c + 0x80) & 0xFF;
        }
    }

    return c;
}

WCHAR ff_wtoupper(WCHAR chr)
{
    if (chr >= 'a' && chr <= 'z')
    {
        return chr - 0x20;
    }
    if (chr >= 0x00E0 && chr <= 0x00FE)
    {
        if (chr != 0x00F7)
        {
            return chr - 0x20;
        }
    }
    return chr;
}

#endif // _USE_LFN

/**
 * @brief 生成加密数据文件名
 * @param filename: 输出文件名缓冲区
 * @param max_len: 缓冲区最大长度
 */
void Generate_HideData_Filename(char* filename, uint32_t max_len)
{
    rtc_parameter_struct rtc_time;

    // 获取当前RTC时间
    rtc_current_time_get(&rtc_time);

    // 格式：hideData{datetime}.txt，其中datetime为14位连续数字
    // 例如：hideData20250101003010.txt
    snprintf(filename, max_len, "0:/hideData/hideData20%02x%02x%02x%02x%02x%02x.txt",
             rtc_time.year, rtc_time.month, rtc_time.date,
             rtc_time.hour, rtc_time.minute, rtc_time.second);
}

/**
 * @brief 创建加密数据文件
 * @return uint8_t: 0表示成功，非0表示失败
 */
uint8_t Create_HideData_File(void)
{
    FRESULT result;
    char filename[MAX_FILENAME_LEN];

    // 生成文件名
    Generate_HideData_Filename(filename, sizeof(filename));

    // 确保文件句柄是干净的
    f_close(&hidedata_file);

    result = f_open(&hidedata_file, filename, FA_CREATE_ALWAYS | FA_WRITE);

    if(FR_OK == result)
    {
        hidedata_record_count = 0;
        return 0;
    }
    else
    {
        rtc_parameter_struct rtc_time;
        rtc_current_time_get(&rtc_time);

        char root_filename[MAX_FILENAME_LEN];
        snprintf(root_filename, sizeof(root_filename), "0:/hideData20%02x%02x%02x%02x%02x%02x.txt",
                 rtc_time.year, rtc_time.month, rtc_time.date,
                 rtc_time.hour, rtc_time.minute, rtc_time.second);

        result = f_open(&hidedata_file, root_filename, FA_CREATE_ALWAYS | FA_WRITE);

        if(FR_OK == result)
        {
            hidedata_record_count = 0;
            return 0;
        }
        else
        {
            return 1;
        }
    }
}

/**
 * @brief 写入加密数据到文件
 * @param timestamp: 时间戳字符串
 * @param voltage: 电压值
 * @param hex_data: 加密的HEX字符串
 * @return uint8_t: 0表示成功，非0表示失败
 */
uint8_t Write_HideData(const char* timestamp, float voltage, const char* hex_data)
{
    FRESULT result;
    char data_line[DATA_BUFFER_SIZE];
    if(timestamp == NULL || hex_data == NULL) return 1; // 参数检查
    if(hidedata_record_count == 0 || hidedata_record_count >= MAX_RECORDS_PER_FILE)
    {
        if(hidedata_record_count >= MAX_RECORDS_PER_FILE)
        {
            f_close(&hidedata_file);
        }
        if(Create_HideData_File() != 0) return 1;
    }
    snprintf(data_line, sizeof(data_line), "%s %.1fV\r\nhide: %s\r\n", timestamp, voltage, hex_data);
    result = f_write(&hidedata_file, data_line, strlen(data_line), &bw);

    if(FR_OK == result)
    {
        hidedata_record_count++;
        f_sync(&hidedata_file); // 立即同步确保数据写入
        return 0;
    }
    return 1;
}

/**
 * @brief 关闭加密数据文件
 */
uint8_t Close_HideData_File(void)
{
    f_close(&hidedata_file);
    hidedata_record_count = 0;
    return 0;
}

/**
 * @brief 生成log数据文件名
 * @param filename: 输出文件名缓冲区
 * @param max_len: 缓冲区最大长度
 */
static void Generate_Log_Filename(char* filename, uint32_t max_len)
{
    // 参数检查
    if(filename == NULL || max_len == 0)
    {
        return;
    }
    //在log文件夹中创建log文件
    snprintf(filename, max_len, "0:/log/log%u.txt", current_log_id);
}

/**
 * @brief 创建log数据文件
 * @return uint8_t: 0表示成功，非0表示失败
 */
uint8_t Create_Log_File(void)
{
    FRESULT result;
    char filename[MAX_FILENAME_LEN];
    result = f_mount(0, &fs);
    if(result != FR_OK)
    {
        return 1;
    }
    if(current_log_id == 0)
    {
        current_log_id = 1;
    }
    f_mkdir("0:/log");
    Generate_Log_Filename(filename, sizeof(filename));
    f_close(&log_file);
    result = f_open(&log_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
    if(FR_OK == result)
    {
        return 0;
    }
    else
    {
        snprintf(filename, sizeof(filename), "0:/log%u.txt", current_log_id);
        result = f_open(&log_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
        return (result == FR_OK) ? 0 : 1;
    }
}

/**
 * @brief 写入log数据到文件
 * @param log_msg: 日志消息字符串
 * @return uint8_t: 0表示成功，非0表示失败
 */
uint8_t Write_Log_Data(const char* log_msg)
{
    FRESULT result;
    char data_line[DATA_BUFFER_SIZE];
    rtc_parameter_struct rtc_time;
    if(log_msg == NULL)
    {
        return 1;
    }
    rtc_current_time_get(&rtc_time);
    snprintf(data_line, sizeof(data_line), "20%02x-%02x-%02x %02x:%02x:%02x %s\r\n",
             rtc_time.year, rtc_time.month, rtc_time.date,
             rtc_time.hour, rtc_time.minute, rtc_time.second, log_msg);
    static uint8_t first_system_init_written = 0;

    if(log_file_created == 0)
    {
        if(Create_Log_File() != 0)
        {
            // TF卡不可用，缓存到Flash
            Cache_Log_To_Flash(data_line);
            return 0; // 缓存成功也返回0
        }
        log_file_created = 1; // 标记文件已创建
        if(!first_system_init_written && strcmp(log_msg, "system init") == 0)// 如果第一次写入且是system init，先写入当前的system init
        {
            first_system_init_written = 1;
        }
    }
    strncpy((char*)filebuffer, data_line, sizeof(filebuffer)-1);    // 将数据复制到filebuffer
    filebuffer[sizeof(filebuffer)-1] = '\0';
    UINT data_len = strlen((char*)filebuffer);
    result = f_write(&log_file, filebuffer, data_len, &bw);
    if(FR_OK == result)
    {
        log_record_count++;
        f_sync(&log_file);
        return 0;
    }
    else
    {
        // 写入失败，缓存到Flash
        Cache_Log_To_Flash(data_line);
        return 0;
    }
}



/**
 * @brief 简单的log ID获取：检查log0.txt存在则从1开始递增
 * @return uint32_t: 下一个log ID
 */
uint32_t Get_Next_Log_ID_From_SD(void)
{
    FRESULT res;
    FIL test_file;
    uint32_t log_id = 1; // 从log1.txt开始
    char filename[64];

    res = f_open(&test_file, "0:/log/log0.txt", FA_READ); // 检查log0.txt是否存在
    if(res != FR_OK)
    {
        return 1;
    }
    f_close(&test_file);
    while(log_id < 100)
    {
        snprintf(filename, sizeof(filename), "0:/log/log%u.txt", log_id);
        res = f_open(&test_file, filename, FA_READ);
        if(res != FR_OK)
        {
            return log_id;
        }
        f_close(&test_file);
        log_id++; 
    }
    // 如果所有ID都被占用，返回1
    return 1;
}

/**
 * @brief 在TF卡根目录创建默认的config.ini文件
 * @return uint8_t: 0表示成功，非0表示失败
 */
uint8_t Create_Default_Config_INI(void)
{
    FIL config_file;
    FRESULT result;
    char config_content[] = "[Ratio]\r\nCh0 = 1.00\r\nCh1 = 1.00\r\nCh2 = 1.00\r\n\r\n[Limit]\r\nCh0 = 10.11\r\nCh1 = 10.11\r\nCh2 = 10.11\r\n";
    UINT bw_local;

    // 检查文件是否已存在
    result = f_open(&config_file, "0:/config.ini", FA_READ);
    if(result == FR_OK)
    {
        // 文件已存在，关闭并返回
        f_close(&config_file);
        return 0;
    }

    // 创建新的config.ini文件
    result = f_open(&config_file, "0:/config.ini", FA_CREATE_NEW | FA_WRITE);
    if(result != FR_OK)
    {
        return 1;
    }

    // 写入默认内容
    result = f_write(&config_file, config_content, strlen(config_content), &bw_local);
    if(result != FR_OK)
    {
        f_close(&config_file);
        return 1;
    }

    // 同步并关闭文件
    f_sync(&config_file);
    f_close(&config_file);

    return 0;
}

/**
 * @brief 更新config.ini文件中的配置参数
 * @param ch0_ratio: 通道0变比值
 * @param ch1_ratio: 通道1变比值
 * @param ch2_ratio: 通道2变比值
 * @param ch0_limit: 通道0阈值
 * @param ch1_limit: 通道1阈值
 * @param ch2_limit: 通道2阈值
 * @return uint8_t: 0表示成功，非0表示失败
 */
uint8_t Update_Config_INI(float ch0_ratio, float ch1_ratio, float ch2_ratio,
                         float ch0_limit, float ch1_limit, float ch2_limit)
{
    FIL config_file;
    FRESULT result;
    char config_content[512];
    UINT bw_local;

    // 格式化新的配置内容
    snprintf(config_content, sizeof(config_content),
             "[Ratio]\r\n"
             "Ch0 = %.2f\r\n"
             "Ch1 = %.2f\r\n"
             "Ch2 = %.2f\r\n"
             "\r\n"
             "[Limit]\r\n"
             "Ch0 = %.2f\r\n"
             "Ch1 = %.2f\r\n"
             "Ch2 = %.2f\r",
             ch0_ratio, ch1_ratio, ch2_ratio,
             ch0_limit, ch1_limit, ch2_limit);

    // 创建或覆盖config.ini文件
    result = f_open(&config_file, "0:/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if(result != FR_OK)
    {
        return 1;
    }

    // 写入新内容
    result = f_write(&config_file, config_content, strlen(config_content), &bw_local);
    if(result != FR_OK)
    {
        f_close(&config_file);
        return 1;
    }

    // 同步并关闭文件
    f_sync(&config_file);
    f_close(&config_file);

    return 0;
}

/**
 * @brief 简化版本：只更新变比值到config.ini，阈值保持当前系统设置
 * @param ch0_ratio: 通道0变比值
 * @param ch1_ratio: 通道1变比值
 * @param ch2_ratio: 通道2变比值
 * @return uint8_t: 0表示成功，非0表示失败
 */
uint8_t Update_Config_INI_Ratio_Only(float ch0_ratio, float ch1_ratio, float ch2_ratio)
{
    // 获取当前系统阈值
    float current_limit = Get_Limit();

    // 调用完整版本的更新函数，所有通道使用相同的阈值
    return Update_Config_INI(ch0_ratio, ch1_ratio, ch2_ratio,
                            current_limit, current_limit, current_limit);
}

// 检查log文件夹是否存在
uint8_t Is_Log_Folder_Missing(void)
{
    DIR dir;
    FRESULT res;
    if(f_mount(0, &fs) != FR_OK)
    {
        return 1; // 挂载失败，认为文件夹不存在
    }
    res = f_opendir(&dir, "0:/log");
    f_mount(0, NULL); // 卸载
    return (res == FR_OK) ? 0 : 1; // 0=存在，1=不存在
}

//创建log0.txt文件
uint8_t Create_Log0_File(void)
{
    FRESULT result;
    FIL log0_file;
    result = f_open(&log0_file, "0:/log/log0.txt", FA_CREATE_ALWAYS | FA_WRITE);
    if(result == FR_OK)
    {
        f_sync(&log0_file);
        f_close(&log0_file);
        return 1; // 成功
    }

    return 0; // 失败
}

