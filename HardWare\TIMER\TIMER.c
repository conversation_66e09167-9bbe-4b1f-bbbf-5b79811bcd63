#include "TIMER.h"
#include "Timer_APP.h"

/**
 * @brief 定时器初始化 - 1ms周期定时器用于LED闪烁和ADC采样控制
 */
void Timer_Init(void)
{
    timer_parameter_struct timer_initpara;

    rcu_periph_clock_enable(LED_TIMER_RCU); // 启用TIMER1时钟
    timer_deinit(LED_TIMER);                 // 复位定时器

    // 配置定时器参数 - 精确1ms周期 (系统时钟240MHz, APB1 60MHz, TIMER1时钟120MHz)
    timer_initpara.prescaler         = TIMER_PRESCALER;
    timer_initpara.alignedmode       = TIMER_COUNTER_EDGE;
    timer_initpara.counterdirection  = TIMER_COUNTER_UP;
    timer_initpara.period            = TIMER_PERIOD;
    timer_initpara.clockdivision     = TIMER_CKDIV_DIV1;
    timer_initpara.repetitioncounter = 0;

    timer_init(LED_TIMER, &timer_initpara);
    timer_interrupt_enable(LED_TIMER, TIMER_INT_UP);
    nvic_irq_enable(LED_TIMER_IRQ, TIMER_PRIORITY, 0);
    timer_enable(LED_TIMER);
}


/**
 * @brief TIMER1中断处理函数 - 每1ms执行一次
 */
void TIMER1_IRQHandler(void)
{
    if(SET == timer_interrupt_flag_get(LED_TIMER, TIMER_INT_FLAG_UP))
    {
        // 清除中断标志
        timer_interrupt_flag_clear(LED_TIMER, TIMER_INT_FLAG_UP);

        // 调用应用层的处理函数
        Timer_LED_Handler();  // LED控制处理
        Timer_ADC_Handler();  // ADC采样处理
    }
}
