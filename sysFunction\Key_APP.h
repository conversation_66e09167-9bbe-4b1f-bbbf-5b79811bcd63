#ifndef __KEY_APP_H
#define __KEY_APP_H

#include "HeaderFiles.h"

#define KEY1_VAL 0x01
#define KEY2_VAL 0x02
#define KEY3_VAL 0x04
#define KEY4_VAL 0x08
#define KEY5_VAL 0x10
#define KEY6_VAL 0x20

typedef enum {
    SAMPLE_PERIOD_5S = 5,   // 5秒周期
    SAMPLE_PERIOD_10S = 10, // 10秒周期
    SAMPLE_PERIOD_15S = 15  // 15秒周期
} sample_period_t;

extern sample_period_t current_sample_period;

void Key_Proc(void);
void Handle_Key1_Press(void);
void Handle_Key2_Press(void);
void Handle_Key3_Press(void);
void Handle_Key4_Press(void);
void Handle_Key5_Press(void);
void Handle_Key6_Press(void);
void Set_Sample_Period(sample_period_t period);
sample_period_t Get_Sample_Period(void);
void Update_Current_Display(void); // 更新当前显示模式的OLED显示

#endif

