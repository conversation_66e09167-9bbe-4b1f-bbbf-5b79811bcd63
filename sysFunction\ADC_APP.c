#include "ADC_APP.h"
#include "Sdcard_APP.h"

float current_ratio = 1.0f;
float current_limit = 100.0f;
float ch0_ratio = 1.0;
float ch1_ratio = 1.0;
float ch2_ratio = 1.0;
float ch0_limit = 100.0;
float ch1_limit = 100.0;
float ch2_limit = 100.0;
uint8_t over_limit_state = 0;
adc_state_t adc_state = ADC_STATE_IDLE;
system_state_t system_state = SYSTEM_STATE_IDLE;
adc_data_t latest_data = {0};
uint32_t sample_interval = 5000;
float ch0_data = 0.00f;
float ch1_data = 0.00f;
float ch2_data = 0.00f;

uint8_t intensity_flag = 0;
uint8_t intensity_flag_single = 0;
uint8_t res_flag = 0;
uint8_t res_flag_single = 0;
uint8_t vol_flag = 0;
uint8_t vol_flag_single = 0;
uint8_t read_mode = 2;       //0-电压读取    1-电流读取    2-电阻读取

void Set_Ratio(float ratio)
{
    if(ratio >= 0.0f && ratio <= 100.0f)
    {
        current_ratio = ratio;
    }
}

float Get_Ratio(void)
{
    return current_ratio;
}

void Set_Limit(float limit)
{
    if(limit >= 0.0f && limit <= 200.0f)
    {
        current_limit = limit;
    }
}

float Get_Limit(void)
{
    return current_limit;
}

void Start_Sampling(void)
{
    adc_state = ADC_STATE_SAMPLING;
    system_state = SYSTEM_STATE_SAMPLING;
    Set_ADC_Sampling_State(ADC_SAMPLING_ON);
    Set_ADC_Sample_Interval(sample_interval);
    Create_Sample_File();
    Update_LED_Status();
}

void Stop_Sampling(void)
{
    adc_state = ADC_STATE_STOPPED;
    system_state = SYSTEM_STATE_IDLE;
    Set_ADC_Sampling_State(ADC_SAMPLING_OFF);
    Close_Sample_File();
    Close_OverLimit_File();
    Update_LED_Status();
}

adc_state_t Get_ADC_State(void)
{
    return adc_state;
}

adc_data_t Get_Latest_Data(void)
{
    return latest_data;
}

float ADC_To_Voltage(uint16_t adc_value)
{
    const float vref = 3.3f;
    const uint16_t adc_max = 4095;
    return ((float)adc_value / adc_max) * vref;
}

void Set_System_State(system_state_t state)
{
    system_state = state;
    Update_LED_Status();
}



system_state_t Get_System_State(void)
{
    return system_state;
}

void Update_LED_Status(void)
{
    switch(system_state)
    {
        case SYSTEM_STATE_IDLE:
            Set_LED1_Blink_Mode(LED_BLINK_OFF);
            Set_LED2_State(0);
            break;
        case SYSTEM_STATE_SAMPLING:
            Set_LED1_Blink_Mode(LED_BLINK_ON);
            break;
        case SYSTEM_STATE_CONFIG:
            Set_LED1_Blink_Mode(LED_BLINK_ON);
            Set_LED2_State(0);
            break;
        default:
            Set_LED1_Blink_Mode(LED_BLINK_OFF);
            Set_LED2_State(0);
            break;
    }
}

void Set_Sample_Interval(uint32_t interval_ms)
{
    sample_interval = interval_ms;
    Set_ADC_Sample_Interval(interval_ms);
}

uint32_t Get_Sample_Interval(void)
{
    return sample_interval;
}

void Set_Over_Limit_State(uint8_t state)
{
    over_limit_state = state;
    if(state)
    {
        Set_LED2_State(1);
    }
    else
    {
        Set_LED2_State(0);
    }
}

void ADC_Proc()
{
    if(read_mode == 0)//电压读取
    {
        ch0_data = (GD30AD3344_AD_Read(GD30AD3344_Channel_4 ,GD30AD3344_PGA_6V144) - 0.033f) * 4.88888f;
        if(vol_flag == 1 || vol_flag_single == 1)
        {
            vol_flag_single = 0;
            snprintf(oled2_buffer, sizeof(oled2_buffer), "%.4f", ch0_data);
            OLED_ShowString(0, 16, (unsigned char *)oled2_buffer, 16);
            OLED_Refresh();
        }
    }
    else if(read_mode == 1)//电流读取
    {
        ch1_data = (GD30AD3344_AD_Read(GD30AD3344_Channel_5 ,GD30AD3344_PGA_6V144) - 0.033f) * 10.7801f;
        if(intensity_flag == 1 || intensity_flag_single == 1)
        {
            intensity_flag_single = 0;
            snprintf(oled2_buffer, sizeof(oled2_buffer), "%.4f", ch1_data);
            OLED_ShowString(0, 16, (unsigned char *)oled2_buffer, 16);
            OLED_Refresh();
        }
    }
    else if(read_mode == 2)//电阻读取
    {
        ch2_data = (GD30AD3344_AD_Read(GD30AD3344_Channel_6 ,GD30AD3344_PGA_6V144) - 0.033f) * 6130.76923f;
        if(res_flag == 1 || res_flag_single == 1)
        {
            res_flag_single = 0;
            snprintf(oled2_buffer, sizeof(oled2_buffer), "%.4f ", ch2_data);
            OLED_ShowString(0, 16, (unsigned char *)oled2_buffer, 16);
            OLED_Refresh();
        }
    }
}
