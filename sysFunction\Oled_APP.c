#include "Oled_APP.h"
#include "ADC_APP.h"
#include "RTC_APP.h"

char oled1_buffer[128] = "system idle";  // 第一行初始化为"system idle"
char oled2_buffer[128] = "";  // 第二行显示

// OLED应用层初始化
void OLED_App_Init(void)
{
    // 设置初始显示内容
    strcpy(oled1_buffer, "system idle");
    strcpy(oled2_buffer, ""); // 确保第二行为空
 
    // 立即显示到OLED
    OLED_ShowString(0, 0, (unsigned char *)oled1_buffer, 16);
    OLED_ShowString(0, 16, (unsigned char *)oled2_buffer, 16);
    OLED_Refresh();
}

void Oled_Task()
{
	//Update_OLED_Display();
//	OLED_ShowString(0, 0, (unsigned char *)oled1_buffer, 16);
//	if(Get_System_State() == SYSTEM_STATE_IDLE)
//	{
//		OLED_ShowString(0, 16, (unsigned char *)"                ", 16);
//	}
//	else
//	{
//		OLED_ShowString(0, 16, (unsigned char *)oled2_buffer, 16);
//	}
//	OLED_Refresh();
}

/* 原有OLED显示函数（已注释，改为使用按键控制的显示模式）
void Update_OLED_Display(void)
{
    // 根据系统状态设置显示内容
    system_state_t current_state = Get_System_State();

    // 第一行根据状态设置
    if(current_state == SYSTEM_STATE_IDLE)
    {
        strcpy(oled1_buffer, "system idle");
        strcpy(oled2_buffer, "              ");
    }
    else if(current_state == SYSTEM_STATE_SAMPLING)
    {
        //第一行显示时间
        rtc_parameter_struct rtc_time;
        rtc_current_time_get(&rtc_time);

        // BCD转十进制
        int hour = ((rtc_time.hour >> 4) & 0x0F) * 10 + (rtc_time.hour & 0x0F);
        int minute = ((rtc_time.minute >> 4) & 0x0F) * 10 + (rtc_time.minute & 0x0F);
        int second = ((rtc_time.second >> 4) & 0x0F) * 10 + (rtc_time.second & 0x0F);

        snprintf(oled1_buffer, sizeof(oled1_buffer), "%02d:%02d:%02d   ", hour, minute, second);

        // 第二行显示电压
        adc_data_t latest_data = Get_Latest_Data();
        snprintf(oled2_buffer, sizeof(oled2_buffer), "%.2fV ", latest_data.voltage * current_ratio);
    }
}
*/


