#ifndef __TIMER_H
#define __TIMER_H

#include "HeaderFiles.h"

#define LED_TIMER           TIMER1          //TIMER1控制LED闪烁
#define LED_TIMER_RCU       RCU_TIMER1      //IMER1时钟
#define LED_TIMER_IRQ       TIMER1_IRQn     //TIMER1中断
#define LED_TIMER_HANDLER   TIMER1_IRQHandler //TIMER1中断处理函数

#define TIMER_PRESCALER     119            
#define TIMER_PERIOD        999            
#define TIMER_PRIORITY      2              

void Timer_Init(void);                                  
void TIMER1_IRQHandler(void);                       

#endif
