#ifndef __HEADERFILES_H
#define __HEADERFILES_H

/************************* C语言标准库 *************************/
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <stdint.h>

/************************* GD32库函数 *************************/
#include "gd32f4xx.h"
#include "gd32f4xx_libopt.h"
#include "systick.h"

/************************* Hardware硬件层 *************************/
#include "LED.h"
#include "Key.h"
#include "RTC.h"
#include "TIMER.h"
#include "oled.h"
#include "ADC.h"
#include "sdcard.h"
#include "gd30ad3344.h"

/************************* Protocol协议层 *************************/
#include "usart.h"
#include "SPI_FLASH.h"

/************************* FatFs文件系统 *************************/
#include "ff.h"
#include "diskio.h"

/************************* 逻辑应用层 *************************/
#include "Function.h"
#include "scheduler.h"
#include "Key_APP.h"
#include "Usart_APP.h"
#include "RTC_APP.h"
#include "Flash_APP.h"
#include "Oled_APP.h"
#include "Sdcard_APP.h"
#include "ADC_APP.h"
#include "Timer_APP.h"
#include "CRC_APP.h"

/************************* 全局变量声明 *************************/
extern uint8_t ucled[8];
extern uint32_t uwTick;

#endif


