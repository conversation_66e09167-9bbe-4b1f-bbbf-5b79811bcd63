#include "gd30ad3344.h"

uint8_t gd30_send_array[ARRAYSIZE] = {0};    // SPI0 DMA 发送缓冲区
uint8_t gd30_receive_array[ARRAYSIZE] = {0}; // SPI0 DMA 接收缓冲区

uint8_t spi_gd30ad3344_send_byte_dma(uint8_t byte)
{
    gd30_send_array[0] = byte;
    
    dma_single_data_parameter_struct dma_init_struct;
    
    dma_deinit(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX);
    dma_init_struct.periph_addr         = (uint32_t)&SPI_DATA(SPI_GD30AD3344);
    dma_init_struct.memory0_addr        = (uint32_t)gd30_send_array;
    dma_init_struct.direction           = DMA_MEMORY_TO_PERIPH;
    dma_init_struct.periph_memory_width = DMA_PERIPH_WIDTH_8BIT;
    dma_init_struct.priority            = DMA_PRIORITY_HIGH;
    dma_init_struct.number              = 1; /* 只发送一个字节 */
    dma_init_struct.periph_inc          = DMA_PERIPH_INCREASE_DISABLE;
    dma_init_struct.memory_inc          = DMA_MEMORY_INCREASE_ENABLE;
    dma_init_struct.circular_mode       = DMA_CIRCULAR_MODE_DISABLE;
    dma_single_data_mode_init(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX, &dma_init_struct);
    dma_channel_subperipheral_select(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX, DMA_GD30_SUB);
    
    dma_deinit(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX);
    dma_init_struct.periph_addr         = (uint32_t)&SPI_DATA(SPI_GD30AD3344);
    dma_init_struct.memory0_addr        = (uint32_t)gd30_receive_array;
    dma_init_struct.direction           = DMA_PERIPH_TO_MEMORY;
    dma_init_struct.priority            = DMA_PRIORITY_HIGH;
    dma_single_data_mode_init(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX, &dma_init_struct);
    dma_channel_subperipheral_select(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX, DMA_GD30_SUB);
    
    dma_channel_enable(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX);
    dma_channel_enable(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX);
    
    spi_dma_enable(SPI_GD30AD3344, SPI_DMA_RECEIVE);
    spi_dma_enable(SPI_GD30AD3344, SPI_DMA_TRANSMIT);
    
    while(RESET == dma_flag_get(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX, DMA_FLAG_FTF));
    
    spi_dma_disable(SPI_GD30AD3344, SPI_DMA_RECEIVE);
    spi_dma_disable(SPI_GD30AD3344, SPI_DMA_TRANSMIT);
    dma_channel_disable(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX);
    dma_channel_disable(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX);
    
    dma_flag_clear(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX, DMA_FLAG_FTF);
    dma_flag_clear(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX, DMA_FLAG_FTF);
    
    return gd30_receive_array[0];
}

uint16_t spi_gd30ad3344_send_halfword_dma(uint16_t half_word)
{
    SPI_GD30AD3344_CS_LOW();
    uint16_t rx_data;
    
    gd30_send_array[0] = (uint8_t)(half_word >> 8);
    gd30_send_array[1] = (uint8_t)half_word;
    
    dma_single_data_parameter_struct dma_init_struct;
    
    dma_deinit(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX);
    dma_init_struct.periph_addr         = (uint32_t)&SPI_DATA(SPI_GD30AD3344);
    dma_init_struct.memory0_addr        = (uint32_t)gd30_send_array;
    dma_init_struct.direction           = DMA_MEMORY_TO_PERIPH;
    dma_init_struct.periph_memory_width = DMA_PERIPH_WIDTH_8BIT;
    dma_init_struct.priority            = DMA_PRIORITY_HIGH;
    dma_init_struct.number              = 2; /* 发送2个字节 */
    dma_init_struct.periph_inc          = DMA_PERIPH_INCREASE_DISABLE;
    dma_init_struct.memory_inc          = DMA_MEMORY_INCREASE_ENABLE;
    dma_init_struct.circular_mode       = DMA_CIRCULAR_MODE_DISABLE;
    dma_single_data_mode_init(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX, &dma_init_struct);
    dma_channel_subperipheral_select(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX, DMA_GD30_SUB);
    
    dma_deinit(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX);
    dma_init_struct.periph_addr         = (uint32_t)&SPI_DATA(SPI_GD30AD3344);
    dma_init_struct.memory0_addr        = (uint32_t)gd30_receive_array;
    dma_init_struct.direction           = DMA_PERIPH_TO_MEMORY;
    dma_init_struct.priority            = DMA_PRIORITY_HIGH;
    dma_single_data_mode_init(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX, &dma_init_struct);
    dma_channel_subperipheral_select(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX, DMA_GD30_SUB);
    
    dma_channel_enable(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX);
    dma_channel_enable(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX);
    
    spi_dma_enable(SPI_GD30AD3344, SPI_DMA_RECEIVE);
    spi_dma_enable(SPI_GD30AD3344, SPI_DMA_TRANSMIT);
    
    while(RESET == dma_flag_get(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX, DMA_FLAG_FTF));
    
    spi_dma_disable(SPI_GD30AD3344, SPI_DMA_RECEIVE);
    spi_dma_disable(SPI_GD30AD3344, SPI_DMA_TRANSMIT);
    dma_channel_disable(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX);
    dma_channel_disable(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX);
    
    dma_flag_clear(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX, DMA_FLAG_FTF);
    dma_flag_clear(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX, DMA_FLAG_FTF);
    
    rx_data = (uint16_t)(gd30_receive_array[0] << 8);
    rx_data |= gd30_receive_array[1];
    SPI_GD30AD3344_CS_HIGH();
    return rx_data;
}

void spi_gd30ad3344_transmit_receive_dma(uint8_t *tx_buffer, uint8_t *rx_buffer, uint16_t size)
{
    if (size > ARRAYSIZE) {
        size = ARRAYSIZE;
    }
    
    for (uint16_t i = 0; i < size; i++) {
        gd30_send_array[i] = tx_buffer[i];
    }
    
    dma_single_data_parameter_struct dma_init_struct;
    
    dma_deinit(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX);
    dma_init_struct.periph_addr         = (uint32_t)&SPI_DATA(SPI_GD30AD3344);
    dma_init_struct.memory0_addr        = (uint32_t)gd30_send_array;
    dma_init_struct.direction           = DMA_MEMORY_TO_PERIPH;
    dma_init_struct.periph_memory_width = DMA_PERIPH_WIDTH_8BIT;
    dma_init_struct.priority            = DMA_PRIORITY_HIGH;
    dma_init_struct.number              = size;
    dma_init_struct.periph_inc          = DMA_PERIPH_INCREASE_DISABLE;
    dma_init_struct.memory_inc          = DMA_MEMORY_INCREASE_ENABLE;
    dma_init_struct.circular_mode       = DMA_CIRCULAR_MODE_DISABLE;
    dma_single_data_mode_init(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX, &dma_init_struct);
    dma_channel_subperipheral_select(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX, DMA_GD30_SUB);
    
    dma_deinit(DMA0, DMA_GD30_CHANNEL_RX);
    dma_init_struct.periph_addr         = (uint32_t)&SPI_DATA(SPI_GD30AD3344);
    dma_init_struct.memory0_addr        = (uint32_t)gd30_receive_array;
    dma_init_struct.direction           = DMA_PERIPH_TO_MEMORY;
    dma_init_struct.priority            = DMA_PRIORITY_HIGH;
    dma_single_data_mode_init(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX, &dma_init_struct);
    dma_channel_subperipheral_select(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX, DMA_GD30_SUB);
    
    dma_channel_enable(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX);
    dma_channel_enable(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX);
    
    spi_dma_enable(SPI_GD30AD3344, SPI_DMA_RECEIVE);
    spi_dma_enable(SPI_GD30AD3344, SPI_DMA_TRANSMIT);
    
    while(RESET == dma_flag_get(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX, DMA_FLAG_FTF));
    
    spi_dma_disable(SPI_GD30AD3344, SPI_DMA_RECEIVE);
    spi_dma_disable(SPI_GD30AD3344, SPI_DMA_TRANSMIT);
    dma_channel_disable(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX);
    dma_channel_disable(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX);
    
    dma_flag_clear(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX, DMA_FLAG_FTF);
    dma_flag_clear(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX, DMA_FLAG_FTF);
    
    for (uint16_t i = 0; i < size; i++) 
	{
        rx_buffer[i] = gd30_receive_array[i];
    }
}

void spi_gd30ad3344_wait_for_dma_end(void)
{
    /* 等待 DMA 传输完成 */
    while(RESET == dma_flag_get(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX, DMA_FLAG_FTF));
    
    /* 清除 DMA 标志 */
    dma_flag_clear(DMA_GD30AD3344, DMA_GD30_CHANNEL_RX, DMA_FLAG_FTF);
    dma_flag_clear(DMA_GD30AD3344, DMA_GD30_CHANNEL_TX, DMA_FLAG_FTF);
}


GD30AD3344 GD30AD3344_InitStruct;

void gd30ad3344_init(void)
{
    rcu_periph_clock_enable(GD30_PORT_RCU);
    rcu_periph_clock_enable(GD30_SPI_RCU);
    rcu_periph_clock_enable(GD30_DMA_RCU);
    
    gpio_af_set(GD30_PORT, GPIO_AF_5, GD30_SCK | GD30_MISO | GD30_MOSI);
    gpio_mode_set(GD30_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, GD30_SCK | GD30_MISO | GD30_MOSI);
    gpio_output_options_set(GD30_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GD30_SCK | GD30_MISO | GD30_MOSI);

    gpio_mode_set(GD30_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, GD30_NSS);
    gpio_output_options_set(GD30_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GD30_NSS);
    
    spi_parameter_struct spi_init_struct;
    spi_init_struct.trans_mode           = SPI_TRANSMODE_FULLDUPLEX;
    spi_init_struct.device_mode          = SPI_MASTER;
    spi_init_struct.frame_size           = SPI_FRAMESIZE_8BIT;
    spi_init_struct.clock_polarity_phase = GD30_SPIMODE;
    spi_init_struct.nss                  = SPI_NSS_SOFT;
    spi_init_struct.prescale             = SPI_PSC_8;
    spi_init_struct.endian               = SPI_ENDIAN_MSB;
    spi_init(SPI_GD30AD3344, &spi_init_struct);

    GD30AD3344_InitStruct.SS         = 0;      
    GD30AD3344_InitStruct.MUX        = 4;      
                                               
    GD30AD3344_InitStruct.PGA        = 0;      
                                               
    GD30AD3344_InitStruct.MODE       = 0;      
    GD30AD3344_InitStruct.DR         = 1;      
                                               
    GD30AD3344_InitStruct.RESERVED_1 = 0;      
    GD30AD3344_InitStruct.PULL_UP_EN = 0;      
    GD30AD3344_InitStruct.NOP        = 1;      
    GD30AD3344_InitStruct.RESERVED   = 1;      
    
    spi_enable(SPI_GD30AD3344);
    spi_gd30ad3344_send_halfword_dma(GD30AD3344_InitStruct_Value);
}

float GD30AD3344_PGA_SET(GD30AD3344_PGA_TypeDef PGA)
{
    float PGA_DATA = 0.0;
    switch(PGA)
    {
        case GD30AD3344_PGA_6V144:
            PGA_DATA = 6.144;
            break;
        case GD30AD3344_PGA_4V096:
            PGA_DATA = 4.096;
            break;
        case GD30AD3344_PGA_2V048:
            PGA_DATA = 2.048;
            break;
        case GD30AD3344_PGA_1V024:
            PGA_DATA = 1.024;
            break;
        case GD30AD3344_PGA_0V512:
            PGA_DATA = 0.512;
            break;
        case GD30AD3344_PGA_0V256:
            PGA_DATA = 0.256;
            break;
        case GD30AD3344_PGA_0V064:
            PGA_DATA = 0.064;
            break;
     
    }
    return (float)PGA_DATA;
}


float GD30AD3344_AD_Read(GD30AD3344_Channel_TypeDef CH,GD30AD3344_PGA_TypeDef Ref)          
{
    uint16_t raw_data;
    float result = 0.0;
    GD30AD3344_InitStruct.MUX     =	CH; 
    GD30AD3344_InitStruct.PGA     =	Ref;
    raw_data = spi_gd30ad3344_send_halfword_dma(GD30AD3344_InitStruct_Value);
    result = (float)raw_data * GD30AD3344_PGA_SET(Ref) / 32768;
    return (float)result;
}


