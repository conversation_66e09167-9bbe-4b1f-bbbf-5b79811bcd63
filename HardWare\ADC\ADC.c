#include "ADC.h"

/**
 * @brief ADC初始化
 */
void ADC_Init(void)
{
    // 配置ADC时钟
    adc_clock_config(ADC_ADCCK_PCLK2_DIV4);

    // 配置GPIO为模拟输入
    rcu_periph_clock_enable(RCU_GPIOC);
    gpio_mode_set(ADC_GPIO_PORT, GPIO_MODE_ANALOG, GPIO_PUPD_NONE, ADC_PIN_CH0);

    // 配置ADC0
    rcu_periph_clock_enable(RCU_ADC0);
    adc_deinit();
    adc_resolution_config(ADC0, ADC_RESOLUTION_12BIT);
    adc_data_alignment_config(ADC0, ADC_DATAALIGN_RIGHT);
    adc_channel_length_config(ADC0, ADC_ROUTINE_CHANNEL, 1);
    adc_routine_channel_config(ADC0, 0, ADC_CHANNEL_10, ADC_SAMPLE_TIME); // PC0->ADC0_IN10

    // 使能并校准ADC
    adc_enable(ADC0);
    delay_1ms(1);
    adc_calibration_enable(ADC0);
}

/**
 * @brief 读取ADC转换结果
 * @return uint16_t: ADC转换值 (0-4095)
 */
uint16_t ADC_Read(void)
{
    adc_software_trigger_enable(ADC0, ADC_ROUTINE_CHANNEL); // 启动软件触发转换
    while(!adc_flag_get(ADC0, ADC_FLAG_EOC));               // 等待转换完成

    uint16_t adc_value = adc_routine_data_read(ADC0);       // 读取转换结果
    adc_flag_clear(ADC0, ADC_FLAG_EOC);                     // 清除转换完成标志

    return adc_value;
}


