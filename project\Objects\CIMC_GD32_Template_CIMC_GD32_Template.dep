Dependencies for Project 'CIMC_GD32_Template', Target 'CIMC_GD32_Template': (DO NOT MODIFY !)
CompilerVersion: 5060750::V5.06 update 6 (build 750)::.\ARMCC
F (..\User\gd32f4xx_it.c)(0x684F2AA2)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_it.o --omf_browse .\objects\gd32f4xx_it.crf --depend .\objects\gd32f4xx_it.d)
I (..\User\gd32f4xx_it.h)(0x682C900E)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\User\main.c)(0x68511AC2)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\User\systick.c)(0x61ADC344)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\systick.o --omf_browse .\objects\systick.crf --depend .\objects\systick.d)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
F (..\HardWare\LED\LED.c)(0x6850771A)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\led.o --omf_browse .\objects\led.crf --depend .\objects\led.d)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\HardWare\OLED\OLED.c)(0x685123E4)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\oled.o --omf_browse .\objects\oled.crf --depend .\objects\oled.d)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
I (..\HardWare\OLED\oledfont.h)(0x685122E2)
F (..\HardWare\RTC\RTC.c)(0x68515A7E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\rtc.o --omf_browse .\objects\rtc.crf --depend .\objects\rtc.d)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\HardWare\KEY\Key.c)(0x6898DF1C)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\key.o --omf_browse .\objects\key.crf --depend .\objects\key.d)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\HardWare\SDCARD\sdcard.c)(0x685048DC)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\sdcard.o --omf_browse .\objects\sdcard.crf --depend .\objects\sdcard.d)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stddef.h)(0x5D9B3488)
F (..\HardWare\ADC\ADC.c)(0x68507348)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\adc.o --omf_browse .\objects\adc.crf --depend .\objects\adc.d)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\HardWare\TIMER\TIMER.c)(0x685111C8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\timer.o --omf_browse .\objects\timer.crf --depend .\objects\timer.d)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\HardWare\GD30AD3344\gd30ad3344.c)(0x6898D0EA)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd30ad3344.o --omf_browse .\objects\gd30ad3344.crf --depend .\objects\gd30ad3344.d)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\Protocol\usart.c)(0x689327D4)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\usart.o --omf_browse .\objects\usart.crf --depend .\objects\usart.d)
I (..\Protocol\usart.h)(0x6893230E)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\Protocol\SPI_FLASH.c)(0x6851237A)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\spi_flash.o --omf_browse .\objects\spi_flash.crf --depend .\objects\spi_flash.d)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\sysFunction\ADC_APP.c)(0x689A2996)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\adc_app.o --omf_browse .\objects\adc_app.crf --depend .\objects\adc_app.d)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\sysFunction\Flash_APP.c)(0x689A26A1)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\flash_app.o --omf_browse .\objects\flash_app.crf --depend .\objects\flash_app.d)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\sysFunction\Function.c)(0x689A26AE)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\function.o --omf_browse .\objects\function.crf --depend .\objects\function.d)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\sysFunction\Key_APP.c)(0x689A29C5)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\key_app.o --omf_browse .\objects\key_app.crf --depend .\objects\key_app.d)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\sysFunction\Oled_APP.c)(0x689A2378)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\oled_app.o --omf_browse .\objects\oled_app.crf --depend .\objects\oled_app.d)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\sysFunction\RTC_APP.c)(0x68515250)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\rtc_app.o --omf_browse .\objects\rtc_app.crf --depend .\objects\rtc_app.d)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\sysFunction\scheduler.c)(0x6898CD8E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\scheduler.o --omf_browse .\objects\scheduler.crf --depend .\objects\scheduler.d)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\sysFunction\Sdcard_APP.c)(0x689A0F14)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\sdcard_app.o --omf_browse .\objects\sdcard_app.crf --depend .\objects\sdcard_app.d)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\sysFunction\Timer_APP.c)(0x689A29A9)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\timer_app.o --omf_browse .\objects\timer_app.crf --depend .\objects\timer_app.d)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\sysFunction\Usart_APP.c)(0x689A26BB)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\usart_app.o --omf_browse .\objects\usart_app.crf --depend .\objects\usart_app.d)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
F (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)()
F (..\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c)(0x67D91E04)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\system_gd32f4xx.o --omf_browse .\objects\system_gd32f4xx.crf --depend .\objects\system_gd32f4xx.d)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_adc.o --omf_browse .\objects\gd32f4xx_adc.crf --depend .\objects\gd32f4xx_adc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c)(0x67D917C2)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_can.o --omf_browse .\objects\gd32f4xx_can.crf --depend .\objects\gd32f4xx_can.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_crc.o --omf_browse .\objects\gd32f4xx_crc.crf --depend .\objects\gd32f4xx_crc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_ctc.o --omf_browse .\objects\gd32f4xx_ctc.crf --depend .\objects\gd32f4xx_ctc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dac.o --omf_browse .\objects\gd32f4xx_dac.crf --depend .\objects\gd32f4xx_dac.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dbg.o --omf_browse .\objects\gd32f4xx_dbg.crf --depend .\objects\gd32f4xx_dbg.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dci.o --omf_browse .\objects\gd32f4xx_dci.crf --depend .\objects\gd32f4xx_dci.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dma.o --omf_browse .\objects\gd32f4xx_dma.crf --depend .\objects\gd32f4xx_dma.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_enet.o --omf_browse .\objects\gd32f4xx_enet.crf --depend .\objects\gd32f4xx_enet.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_exmc.o --omf_browse .\objects\gd32f4xx_exmc.crf --depend .\objects\gd32f4xx_exmc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_exti.o --omf_browse .\objects\gd32f4xx_exti.crf --depend .\objects\gd32f4xx_exti.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_fmc.o --omf_browse .\objects\gd32f4xx_fmc.crf --depend .\objects\gd32f4xx_fmc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_fwdgt.o --omf_browse .\objects\gd32f4xx_fwdgt.crf --depend .\objects\gd32f4xx_fwdgt.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_gpio.o --omf_browse .\objects\gd32f4xx_gpio.crf --depend .\objects\gd32f4xx_gpio.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_i2c.o --omf_browse .\objects\gd32f4xx_i2c.crf --depend .\objects\gd32f4xx_i2c.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_ipa.o --omf_browse .\objects\gd32f4xx_ipa.crf --depend .\objects\gd32f4xx_ipa.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_iref.o --omf_browse .\objects\gd32f4xx_iref.crf --depend .\objects\gd32f4xx_iref.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_misc.o --omf_browse .\objects\gd32f4xx_misc.crf --depend .\objects\gd32f4xx_misc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_pmu.o --omf_browse .\objects\gd32f4xx_pmu.crf --depend .\objects\gd32f4xx_pmu.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_rcu.o --omf_browse .\objects\gd32f4xx_rcu.crf --depend .\objects\gd32f4xx_rcu.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_rtc.o --omf_browse .\objects\gd32f4xx_rtc.crf --depend .\objects\gd32f4xx_rtc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_sdio.o --omf_browse .\objects\gd32f4xx_sdio.crf --depend .\objects\gd32f4xx_sdio.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_spi.o --omf_browse .\objects\gd32f4xx_spi.crf --depend .\objects\gd32f4xx_spi.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_syscfg.o --omf_browse .\objects\gd32f4xx_syscfg.crf --depend .\objects\gd32f4xx_syscfg.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_timer.o --omf_browse .\objects\gd32f4xx_timer.crf --depend .\objects\gd32f4xx_timer.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_tli.o --omf_browse .\objects\gd32f4xx_tli.crf --depend .\objects\gd32f4xx_tli.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_trng.o --omf_browse .\objects\gd32f4xx_trng.crf --depend .\objects\gd32f4xx_trng.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_usart.o --omf_browse .\objects\gd32f4xx_usart.crf --depend .\objects\gd32f4xx_usart.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_wwdgt.o --omf_browse .\objects\gd32f4xx_wwdgt.crf --depend .\objects\gd32f4xx_wwdgt.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s)(0x6894BDE8)(--cpu Cortex-M4.fp.sp -g --apcs=interwork --pd "__MICROLIB SETA 1"

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

--pd "__UVISION_VERSION SETA 539" --pd "_RTE_ SETA 1" --pd "GD32F470 SETA 1" --pd "_RTE_ SETA 1"

--list .\listings\startup_gd32f450_470.lst --xref -o .\objects\startup_gd32f450_470.o --depend .\objects\startup_gd32f450_470.d)
F (..\Fatfs\00readme.txt)(0x4E64F382)()
F (..\Fatfs\diskio.h)(0x4D21CB12)()
F (..\Fatfs\ff.h)(0x4E646136)()
F (..\Fatfs\ffconf.h)(0x684F61CA)()
F (..\Fatfs\integer.h)(0x4BD31212)()
F (..\Fatfs\ff.c)(0x4E64E464)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\ff.o --omf_browse .\objects\ff.crf --depend .\objects\ff.d)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\Fatfs\diskio.h)(0x4D21CB12)
F (..\Fatfs\diskio.c)(0x68504A00)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\HardWare -I ..\CMSIS\GD\GD32F4xx\Include -I ..\Protocol -I ..\HardWare\KEY -I ..\HardWare\LED -I ..\HardWare\OLED -I ..\HardWare\RTC -I ..\HardWare\SDCARD -I ..\HardWare\ADC -I ..\Fatfs -I ..\HardWare\TIMER -I ..\sysFunction -I ..\HardWare\GD30AD3344

-I.\RTE\_CIMC_GD32_Template

-ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include

-ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="539" -D_RTE_ -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\diskio.o --omf_browse .\objects\diskio.crf --depend .\objects\diskio.d)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\HardWare\SDCARD\sdcard.h)(0x6811903A)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdint.h)(0x5D9B3488)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\Keil5\ARM\ARMCC\include\stdlib.h)(0x5D9B348A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\HardWare\RTC\RTC.h)(0x684EEA38)
I (..\HeaderFiles\HeaderFiles.h)(0x6898D2D4)
I (D:\Keil5\ARM\ARMCC\include\stdio.h)(0x5D9B348A)
I (D:\Keil5\ARM\ARMCC\include\string.h)(0x5D9B3488)
I (D:\Keil5\ARM\ARMCC\include\stdarg.h)(0x5D9B348A)
I (..\User\systick.h)(0x61ADC344)
I (..\HardWare\LED\LED.h)(0x68506D9E)
I (..\HardWare\KEY\Key.h)(0x6898DEF0)
I (..\HardWare\TIMER\TIMER.h)(0x685132BE)
I (..\HardWare\OLED\oled.h)(0x68511ED4)
I (..\HardWare\ADC\ADC.h)(0x685132A0)
I (..\HardWare\GD30AD3344\gd30ad3344.h)(0x6898CD04)
I (..\Protocol\usart.h)(0x6893230E)
I (..\Protocol\SPI_FLASH.h)(0x684E7754)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\ffconf.h)(0x684F61CA)
I (..\sysFunction\Function.h)(0x6851334E)
I (..\sysFunction\scheduler.h)(0x682C9110)
I (..\sysFunction\Key_APP.h)(0x689A2730)
I (..\sysFunction\Usart_APP.h)(0x689A1B06)
I (..\sysFunction\Sdcard_APP.h)(0x689A0F24)
I (..\sysFunction\RTC_APP.h)(0x685131A2)
I (..\sysFunction\Flash_APP.h)(0x689A1B06)
I (..\sysFunction\Oled_APP.h)(0x689A2411)
I (..\sysFunction\ADC_APP.h)(0x6898DC6E)
I (..\sysFunction\Timer_APP.h)(0x68513646)
I (..\sysFunction\CRC_APP.h)(0x6898D32A)
