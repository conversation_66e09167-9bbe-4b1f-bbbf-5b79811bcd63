#ifndef __GD30AD3344__H
#define __GD30AD3344__H

#include "HeaderFiles.h"

#define ARRAYSIZE             (12)

#define SPI0_PORT              GPIOA
#define SPI0_PORT_RCU          RCU_GPIOA

#define SPI0_NSS               GPIO_PIN_4
#define SPI0_SCK               GPIO_PIN_5
#define SPI0_MISO              GPIO_PIN_6
#define SPI0_MOSI              GPIO_PIN_7

#define SPI_GD30AD3344         SPI0
#define DMA_GD30AD3344         DMA1
#define DMA_GD30_CHANNEL_TX    DMA_CH3
#define DMA_GD30_CHANNEL_RX    DMA_CH2
#define DMA_GD30_SUB           DMA_SUBPERI3

#define SPI_GD30AD3344_CS_LOW()  gpio_bit_reset(SPI0_PORT, SPI0_NSS)
#define SPI_GD30AD3344_CS_HIGH() gpio_bit_set  (SPI0_PORT, SPI0_NSS)

#define GD30_DMA_RCU           RCU_DMA1
#define GD30_SPI_RCU           RCU_SPI0

#define GD30_PORT              GPIOA
#define GD30_PORT_RCU          RCU_GPIOA

#define GD30_NSS               SPI0_NSS
#define GD30_SCK               SPI0_SCK
#define GD30_MISO              SPI0_MISO
#define GD30_MOSI              SPI0_MOSI

#define GD30_SPIMODE           SPI_CK_PL_LOW_PH_2EDGE

typedef struct _GD30AD3344
{
    uint16_t SS         : 1;
    uint16_t MUX        : 3;
    uint16_t PGA        : 3;
    uint16_t MODE       : 1;
    uint16_t DR         : 3;
    uint16_t RESERVED_1 : 1;
    uint16_t PULL_UP_EN : 1;
    uint16_t NOP        : 2;
    uint16_t RESERVED   : 1;
}GD30AD3344;

extern GD30AD3344 GD30AD3344_InitStruct;
 

typedef enum
{
    GD30AD3344_PGA_6V144      = 0,
    GD30AD3344_PGA_4V096      = 1,
    GD30AD3344_PGA_2V048      = 2,
    GD30AD3344_PGA_1V024      = 3,
    GD30AD3344_PGA_0V512      = 4,
    GD30AD3344_PGA_0V256      = 5,
    GD30AD3344_PGA_0V064      = 6,
}
GD30AD3344_PGA_TypeDef;


typedef enum
{
    GD30AD3344_Channel_0      = 0,		//AIN0~AIN1
    GD30AD3344_Channel_1      = 1,		//AIN0~AIN3
    GD30AD3344_Channel_2      = 2,		//AIN1~AIN3
    GD30AD3344_Channel_3      = 3,		//AIN2~AIN3
    GD30AD3344_Channel_4      = 4,		//AIN0~GND
    GD30AD3344_Channel_5      = 5,		//AIN1~GND
    GD30AD3344_Channel_6      = 6,		//AIN2~GND
    GD30AD3344_Channel_7      = 7,		//AIN3~GND
}

GD30AD3344_Channel_TypeDef;

#define GD30AD3344_InitStruct_Value ((uint16_t)((GD30AD3344_InitStruct.SS         <<15)|\
                                                (GD30AD3344_InitStruct.MUX        <<12)|\
                                                (GD30AD3344_InitStruct.PGA        << 9)|\
                                                (GD30AD3344_InitStruct.MODE       << 8)|\
                                                (GD30AD3344_InitStruct.DR         << 5)|\
                                                (GD30AD3344_InitStruct.RESERVED_1 << 4)|\
                                                (GD30AD3344_InitStruct.PULL_UP_EN << 3)|\
                                                (GD30AD3344_InitStruct.NOP        << 1)|\
                                                (GD30AD3344_InitStruct.RESERVED   << 0)))

extern GD30AD3344 GD30AD3344_InitStruct;

float GD30AD3344_AD_Read(GD30AD3344_Channel_TypeDef CH,GD30AD3344_PGA_TypeDef Ref);
float GD30AD3344_correct(GD30AD3344_Channel_TypeDef ch,unsigned char len);
void gd30ad3344_init(void);

#endif
