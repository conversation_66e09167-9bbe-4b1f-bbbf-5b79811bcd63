#ifndef __SDCARD_APP_H
#define __SDCARD_APP_H

#include "HeaderFiles.h"
#include "sdcard.h"
#include "gd32f4xx_rtc.h"

#define SAMPLE_DIR_NAME     "sample"      
#define OVERLIMIT_DIR_NAME  "overlimit"   
#define HIDEDATA_DIR_NAME   "hideData"    
#define LOG_DIR_NAME        "log"         
#define DATA_BUFFER_SIZE    256           
#define MAX_FILENAME_LEN    64            
#define MAX_RECORDS_PER_FILE 10           

typedef enum 
{
    RECORD_STATE_IDLE = 0,    // 空闲状态
    RECORD_STATE_ACTIVE,      // 记录中
    RECORD_STATE_ERROR        // 错误状态
} record_state_t;

extern record_state_t record_state;
extern FIL overlimit_file;
extern FIL hidedata_file;
extern FIL log_file;
extern FATFS fatfs;

void nvic_config(void);
uint8_t Check_TF_Card(void);
uint8_t Init_Data_Recording(void);
uint8_t Create_Sample_File(void);
uint8_t Create_Default_Config_INI(void);
uint8_t Write_Sample_Data(const char* timestamp, float voltage, float ratio);
uint8_t Write_Sample_Data_MultiChannel(const char* timestamp,
                                      float ch0_value, float ch1_value, float ch2_value,
                                      float ch0_ratio, float ch1_ratio, float ch2_ratio);
uint8_t Close_Sample_File(void);
uint8_t Write_OverLimit_Data(const char* timestamp, float voltage, float limit);
uint8_t Close_OverLimit_File(void);
uint8_t Create_HideData_File(void);
uint8_t Write_HideData(const char* timestamp, float voltage, const char* hex_data);
uint8_t Close_HideData_File(void);
uint8_t Create_Log_File(void);
uint8_t Write_Log_Data(const char* log_msg);
uint32_t Get_Next_Log_ID_From_SD(void);
uint8_t Create_Log0_File(void);
uint8_t Update_Config_INI(float ch0_ratio, float ch1_ratio, float ch2_ratio,
                         float ch0_limit, float ch1_limit, float ch2_limit);
uint8_t Update_Config_INI_Ratio_Only(float ch0_ratio, float ch1_ratio, float ch2_ratio);

#endif
