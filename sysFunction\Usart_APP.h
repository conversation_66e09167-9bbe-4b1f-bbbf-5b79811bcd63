#ifndef __USART_APP_H
#define __USART_APP_H

#include "HeaderFiles.h"
#include "Sdcard_APP.h"

// 设备ID定义
#define MYDEVICE_ID 0x0001

void RS485_Task(void);
void Handle_Command_Set_RTC(char* input, uint16_t length);
void Handle_RTC_Now_Command(void);
void Handle_Command_Set_Ratio(char* input, uint16_t length);
void Handle_Get_Device_ID_Command(void);
void Handle_Get_Ratio_Command(void);
void Handle_Get_Data_Command(void);
void Handle_Start_Command(void);
void Handle_Stop_Command(void);
void Handle_Command_Set_Limit(char* input, uint16_t length);
void Handle_Get_Limit_Command(void);
void Handle_Command_Get_Limit(void);

void Generate_Encrypted_Output_With_Values(rtc_parameter_struct* rtc_time, float actual_voltage);
void Generate_Encrypted_Output_With_Values_OverLimit(rtc_parameter_struct* rtc_time, float actual_voltage);


// 输出模式定义
typedef enum {
    OUTPUT_MODE_NORMAL = 0,  // 正常模式
    OUTPUT_MODE_ENCRYPTED    // 加密模式
} output_mode_t;

extern output_mode_t current_output_mode; 


// 三通道配置Flash存储命令处理函数声明
void Handle_Multi_Config_Save_Command(void);
void Handle_Multi_Config_Read_Command(void);


#endif
