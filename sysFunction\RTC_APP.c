#include "RTC_APP.h"

// 将十进制值转换为BCD格式
static uint8_t convert_to_bcd(uint8_t value) 
{
    return ((value / 10) << 4) | (value % 10);
}

void RTC_Proc(void)
{
	rtc_show_time();
}

/**
 * @brief 使用日期时间数组配置RTC
 * @param datetime 日期时间数组，格式为[年, 月, 日, 时, 分, 秒]
 * @return uint8_t 0表示成功，非0表示失败
 */
uint8_t Configure_RTC_With_DateTime(uint16_t* datetime)
{
    rtc_parameter_struct rtc_config;
    if(datetime == NULL)
    {
        return 1; // 参数错误
    }
    extern uint32_t prescaler_a, prescaler_s;
    rcu_periph_clock_enable(RCU_PMU);
    pmu_backup_write_enable();
    
    /* 确保VBAT域和备份LDO启用 */
    PMU_CTL |= PMU_CTL_BKPWEN;    // 使能备份域写访问
    PMU_CS |= (uint32_t)PMU_CS_LVDF;  // 清除LVD标志
    
    /* 配置备份域LDO为开启状态 */
    uint32_t temp_cs = PMU_CS;
    temp_cs &= ~PMU_CS_BLDOON;         // 清除BLDOON位
    temp_cs |= (uint32_t)PMU_BLDOON_ON; // 设置BLDOON位为开启状态
    PMU_CS = temp_cs;
    
    /* 等待LDO准备就绪 */
    while((PMU_CS & PMU_CS_BLDORF) == RESET)
    {
    }
    
    // 确保RTC时钟源配置正确
    extern void rtc_pre_config(void);
    rtc_pre_config();
    
    // 配置RTC参数
    rtc_config.factor_asyn = prescaler_a;
    rtc_config.factor_syn = prescaler_s;
    rtc_config.year = convert_to_bcd(datetime[0] % 100); // 只取年份的后两位并转为BCD
    rtc_config.month = convert_to_bcd(datetime[1]);
    rtc_config.date = convert_to_bcd(datetime[2]);
    rtc_config.hour = convert_to_bcd(datetime[3]);
    rtc_config.minute = convert_to_bcd(datetime[4]);
    rtc_config.second = convert_to_bcd(datetime[5]);
    rtc_config.display_format = RTC_24HOUR;
    rtc_config.am_pm = RTC_AM;
    rtc_config.day_of_week = RTC_MONDAY;
    
    // 初始化RTC
    if(ERROR == rtc_init(&rtc_config))
    {
        return 2; // 配置失败，不输出错误信息保持简洁
    }

    // 保存配置标志
    RTC_BKP0 = 0x32F0; 
    return 0; // 配置成功
}
