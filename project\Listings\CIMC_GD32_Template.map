Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    gd32f4xx_it.o(i.SDIO_IRQHandler) refers to sdcard.o(i.sd_interrupts_process) for sd_interrupts_process
    gd32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.delay_decrement) for delay_decrement
    gd32f4xx_it.o(i.SysTick_Handler) refers to function.o(.data) for uwTick
    main.o(i.main) refers to function.o(i.System_Init) for System_Init
    main.o(i.main) refers to function.o(i.UsrFunction) for UsrFunction
    systick.o(i.delay_1ms) refers to systick.o(.data) for delay
    systick.o(i.delay_decrement) refers to systick.o(.data) for delay
    systick.o(i.systick_config) refers to systick.o(i.NVIC_SetPriority) for NVIC_SetPriority
    systick.o(i.systick_config) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    led.o(i.Led_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    led.o(i.Led_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    led.o(i.Led_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    led.o(i.Led_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.I2C_Start) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.I2C_Stop) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.I2C_WaitAck) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.I2C_WaitAck) refers to oled.o(i.I2C_Stop) for I2C_Stop
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ClearPoint) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ColorTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisplayTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    oled.o(i.OLED_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.OLED_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.OLED_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.OLED_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Refresh) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_ShowChinese) for OLED_ShowChinese
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ClearPoint) for OLED_ClearPoint
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for asc2_1206
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_ClearPoint) for OLED_ClearPoint
    oled.o(i.OLED_ShowChinese) refers to oled.o(.data) for Hzk1
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPicture) refers to oled.o(i.OLED_WR_BP) for OLED_WR_BP
    oled.o(i.OLED_ShowPicture) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WR_BP) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_Start) for I2C_Start
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.Send_Byte) for Send_Byte
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_WaitAck) for I2C_WaitAck
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_Stop) for I2C_Stop
    oled.o(i.Send_Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.Send_Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.Send_Byte) refers to oled.o(i.IIC_delay) for IIC_delay
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rtc.o(i.RTC_Init) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    rtc.o(i.RTC_Init) refers to rtc.o(i.rtc_pre_config) for rtc_pre_config
    rtc.o(i.RTC_Init) refers to rtc.o(i.rtc_setup_default) for rtc_setup_default
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear) for rcu_all_reset_flag_clear
    rtc.o(i.RTC_Init) refers to rtc.o(.data) for RTCSRC_FLAG
    rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_rtc_clock_config) for rcu_rtc_clock_config
    rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rtc.o(i.rtc_pre_config) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    rtc.o(i.rtc_pre_config) refers to rtc.o(.data) for prescaler_s
    rtc.o(i.rtc_setup) refers to gd32f4xx_usart.o(i.usart_interrupt_disable) for usart_interrupt_disable
    rtc.o(i.rtc_setup) refers to printfa.o(i.__0printf) for __2printf
    rtc.o(i.rtc_setup) refers to rtc.o(i.usart_input_threshold) for usart_input_threshold
    rtc.o(i.rtc_setup) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc.o(i.rtc_setup) refers to rtc.o(i.rtc_show_time) for rtc_show_time
    rtc.o(i.rtc_setup) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    rtc.o(i.rtc_setup) refers to rtc.o(.data) for prescaler_a
    rtc.o(i.rtc_setup) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.rtc_setup_default) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rtc.o(i.rtc_setup_default) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    rtc.o(i.rtc_setup_default) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc.o(i.rtc_setup_default) refers to rtc.o(.data) for prescaler_a
    rtc.o(i.rtc_setup_default) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.rtc_show_alarm) refers to gd32f4xx_rtc.o(i.rtc_alarm_get) for rtc_alarm_get
    rtc.o(i.rtc_show_alarm) refers to printfa.o(i.__0printf) for __2printf
    rtc.o(i.rtc_show_alarm) refers to rtc.o(.bss) for rtc_alarm
    rtc.o(i.rtc_show_time) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    rtc.o(i.rtc_show_time) refers to printfa.o(i.__0printf) for __2printf
    rtc.o(i.rtc_show_time) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.usart_input_threshold) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    rtc.o(i.usart_input_threshold) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    rtc.o(i.usart_input_threshold) refers to printfa.o(i.__0printf) for __2printf
    key.o(i.Key_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    key.o(i.Key_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    key.o(i.Key_Read) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.r1_error_check) refers to sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdcard.o(i.r2_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.r3_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.r7_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.rcu_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_block_read) refers to sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_block_read) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdcard.o(i.sd_block_read) refers to sdcard.o(i.dma_receive_config) for dma_receive_config
    sdcard.o(i.sd_block_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdcard.o(i.sd_block_read) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_block_write) refers to sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_block_write) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdcard.o(i.sd_block_write) refers to sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdcard.o(i.sd_block_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdcard.o(i.sd_block_write) refers to sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdcard.o(i.sd_block_write) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_bus_mode_config) refers to sdcard.o(i.sd_bus_width_config) for sd_bus_width_config
    sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdcard.o(i.sd_bus_mode_config) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_bus_width_config) refers to sdcard.o(i.sd_scr_get) for sd_scr_get
    sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_bus_width_config) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_bus_width_config) refers to sdcard.o(.data) for sd_scr
    sdcard.o(i.sd_card_capacity_get) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_card_capacity_get) refers to sdcard.o(.bss) for sd_csd
    sdcard.o(i.sd_card_information_get) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_card_information_get) refers to sdcard.o(.bss) for sd_cid
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_power_state_get) for sdio_power_state_get
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_card_init) refers to sdcard.o(i.r2_error_check) for r2_error_check
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_card_init) refers to sdcard.o(i.r6_error_check) for r6_error_check
    sdcard.o(i.sd_card_init) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_card_init) refers to sdcard.o(.bss) for sd_cid
    sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_card_select_deselect) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_card_state_get) refers to sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdcard.o(i.sd_card_state_get) refers to sdcard.o(.data) for sd_rca
    sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_cardstatus_get) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_cardstatus_get) refers to sdcard.o(.data) for sd_rca
    sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_erase) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_erase) refers to sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdcard.o(i.sd_erase) refers to sdcard.o(.bss) for sd_csd
    sdcard.o(i.sd_erase) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_init) refers to sdcard.o(i.rcu_config) for rcu_config
    sdcard.o(i.sd_init) refers to sdcard.o(i.gpio_config) for gpio_config
    sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_deinit) for sdio_deinit
    sdcard.o(i.sd_init) refers to sdcard.o(i.sd_power_on) for sd_power_on
    sdcard.o(i.sd_init) refers to sdcard.o(i.sd_card_init) for sd_card_init
    sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_get) for sdio_interrupt_flag_get
    sdcard.o(i.sd_interrupts_process) refers to sdcard.o(i.sd_transfer_stop) for sd_transfer_stop
    sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear) for sdio_interrupt_flag_clear
    sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_disable) for sdio_interrupt_disable
    sdcard.o(i.sd_interrupts_process) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_lock_unlock) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_lock_unlock) refers to sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdcard.o(i.sd_lock_unlock) refers to sdcard.o(.bss) for sd_csd
    sdcard.o(i.sd_lock_unlock) refers to sdcard.o(.data) for sd_rca
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_multiblocks_read) refers to sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_multiblocks_read) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdcard.o(i.sd_multiblocks_read) refers to sdcard.o(i.dma_receive_config) for dma_receive_config
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdcard.o(i.sd_multiblocks_read) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_power_off) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_enable) for sdio_clock_enable
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_power_on) refers to sdcard.o(i.cmdsent_error_check) for cmdsent_error_check
    sdcard.o(i.sd_power_on) refers to sdcard.o(i.r7_error_check) for r7_error_check
    sdcard.o(i.sd_power_on) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_power_on) refers to sdcard.o(i.r3_error_check) for r3_error_check
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_power_on) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_scr_get) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_sdstatus_get) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_sdstatus_get) refers to sdcard.o(.data) for sd_rca
    sdcard.o(i.sd_transfer_mode_config) refers to sdcard.o(.data) for transmode
    sdcard.o(i.sd_transfer_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_transfer_stop) refers to sdcard.o(i.r1_error_check) for r1_error_check
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    adc.o(i.ADC_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    adc.o(i.ADC_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_deinit) for adc_deinit
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_resolution_config) for adc_resolution_config
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_channel_length_config) for adc_channel_length_config
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_routine_channel_config) for adc_routine_channel_config
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    adc.o(i.ADC_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    adc.o(i.ADC_Read) refers to gd32f4xx_adc.o(i.adc_software_trigger_enable) for adc_software_trigger_enable
    adc.o(i.ADC_Read) refers to gd32f4xx_adc.o(i.adc_flag_get) for adc_flag_get
    adc.o(i.ADC_Read) refers to gd32f4xx_adc.o(i.adc_routine_data_read) for adc_routine_data_read
    adc.o(i.ADC_Read) refers to gd32f4xx_adc.o(i.adc_flag_clear) for adc_flag_clear
    timer.o(i.TIMER1_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    timer.o(i.TIMER1_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    timer.o(i.TIMER1_IRQHandler) refers to timer_app.o(i.Timer_LED_Handler) for Timer_LED_Handler
    timer.o(i.TIMER1_IRQHandler) refers to timer_app.o(i.Timer_ADC_Handler) for Timer_ADC_Handler
    timer.o(i.Timer_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    timer.o(i.Timer_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    timer.o(i.Timer_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    timer.o(i.Timer_Init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    timer.o(i.Timer_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    timer.o(i.Timer_Init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    gd30ad3344.o(i.GD30AD3344_AD_Read) refers to gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) for spi_gd30ad3344_send_halfword_dma
    gd30ad3344.o(i.GD30AD3344_AD_Read) refers to gd30ad3344.o(i.GD30AD3344_PGA_SET) for GD30AD3344_PGA_SET
    gd30ad3344.o(i.GD30AD3344_AD_Read) refers to gd30ad3344.o(.data) for GD30AD3344_InitStruct
    gd30ad3344.o(i.gd30ad3344_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd30ad3344.o(i.gd30ad3344_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    gd30ad3344.o(i.gd30ad3344_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd30ad3344.o(i.gd30ad3344_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd30ad3344.o(i.gd30ad3344_init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    gd30ad3344.o(i.gd30ad3344_init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    gd30ad3344.o(i.gd30ad3344_init) refers to gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) for spi_gd30ad3344_send_halfword_dma
    gd30ad3344.o(i.gd30ad3344_init) refers to gd30ad3344.o(.data) for GD30AD3344_InitStruct
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd30ad3344.o(.bss) for gd30_send_array
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd30ad3344.o(.bss) for gd30_send_array
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd30ad3344.o(.bss) for gd30_send_array
    gd30ad3344.o(i.spi_gd30ad3344_wait_for_dma_end) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd30ad3344.o(i.spi_gd30ad3344_wait_for_dma_end) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    usart.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    usart.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    usart.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_clear) for usart_interrupt_flag_clear
    usart.o(i.USART0_IRQHandler) refers to usart.o(.data) for uart_rx_idle_flag
    usart.o(i.USART0_IRQHandler) refers to function.o(.data) for uwTick
    usart.o(i.USART0_IRQHandler) refers to usart.o(.bss) for uart_rx_buffer
    usart.o(i.USART1_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    usart.o(i.USART1_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    usart.o(i.USART1_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_clear) for usart_interrupt_flag_clear
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for rs485_rx_idle_flag
    usart.o(i.USART1_IRQHandler) refers to function.o(.data) for uwTick
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for rs485_rx_buffer
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    usart.o(i.rs485_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    usart.o(i.rs485_printf) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    usart.o(i.rs485_printf) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart.o(i.rs485_printf) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    usart.o(i.rs485_printf) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    usart.o(i.rs485_printf) refers to usart.o(.bss) for buffer
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_parity_config) for usart_parity_config
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_word_length_set) for usart_word_length_set
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_stop_bit_set) for usart_stop_bit_set
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_hardware_flow_cts_config) for usart_hardware_flow_cts_config
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_hardware_flow_rts_config) for usart_hardware_flow_rts_config
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usart.o(i.rs485_usart1_config) refers to memseta.o(.text) for __aeabi_memclr
    usart.o(i.rs485_usart1_config) refers to usart.o(.data) for rs485_rx_index
    usart.o(i.rs485_usart1_config) refers to usart.o(.bss) for rs485_rx_buffer
    usart.o(i.usart0_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(i.usart0_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.usart0_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usart.o(i.usart0_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_parity_config) for usart_parity_config
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_word_length_set) for usart_word_length_set
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_stop_bit_set) for usart_stop_bit_set
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_hardware_flow_cts_config) for usart_hardware_flow_cts_config
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_hardware_flow_rts_config) for usart_hardware_flow_rts_config
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    usart.o(i.usart0_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usart.o(i.usart0_config) refers to memseta.o(.text) for __aeabi_memclr
    usart.o(i.usart0_config) refers to usart.o(.data) for uart_rx_index
    usart.o(i.usart0_config) refers to usart.o(.bss) for uart_rx_buffer
    spi_flash.o(i.spi_flash_buffer_erase) refers to memseta.o(.text) for __aeabi_memclr4
    spi_flash.o(i.spi_flash_buffer_erase) refers to spi_flash.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    spi_flash.o(i.spi_flash_buffer_erase) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    spi_flash.o(i.spi_flash_buffer_erase) refers to spi_flash.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    spi_flash.o(i.spi_flash_buffer_read) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_buffer_read) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_buffer_read) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_buffer_write) refers to spi_flash.o(i.spi_flash_page_write) for spi_flash_page_write
    spi_flash.o(i.spi_flash_bulk_erase) refers to spi_flash.o(i.spi_flash_write_enable) for spi_flash_write_enable
    spi_flash.o(i.spi_flash_bulk_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_bulk_erase) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_bulk_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_bulk_erase) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    spi_flash.o(i.spi_flash_page_write) refers to spi_flash.o(i.spi_flash_write_enable) for spi_flash_write_enable
    spi_flash.o(i.spi_flash_page_write) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_page_write) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_page_write) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_page_write) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    spi_flash.o(i.spi_flash_read_byte) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_read_id) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_sector_erase) refers to spi_flash.o(i.spi_flash_write_enable) for spi_flash_write_enable
    spi_flash.o(i.spi_flash_sector_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_sector_erase) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_sector_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_sector_erase) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    spi_flash.o(i.spi_flash_send_byte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    spi_flash.o(i.spi_flash_send_byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    spi_flash.o(i.spi_flash_send_byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    spi_flash.o(i.spi_flash_send_halfword) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    spi_flash.o(i.spi_flash_send_halfword) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    spi_flash.o(i.spi_flash_send_halfword) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    spi_flash.o(i.spi_flash_start_read_sequence) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_start_read_sequence) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_wait_for_write_end) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_wait_for_write_end) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_wait_for_write_end) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_write_enable) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    adc_app.o(i.ADC_Proc) refers to gd30ad3344.o(i.GD30AD3344_AD_Read) for GD30AD3344_AD_Read
    adc_app.o(i.ADC_Proc) refers to key_app.o(i.Update_Current_Display) for Update_Current_Display
    adc_app.o(i.ADC_Proc) refers to adc_app.o(.data) for read_mode
    adc_app.o(i.Get_ADC_State) refers to adc_app.o(.data) for adc_state
    adc_app.o(i.Get_Latest_Data) refers to adc_app.o(.bss) for latest_data
    adc_app.o(i.Get_Limit) refers to adc_app.o(.data) for current_limit
    adc_app.o(i.Get_Ratio) refers to adc_app.o(.data) for current_ratio
    adc_app.o(i.Get_Sample_Interval) refers to adc_app.o(.data) for sample_interval
    adc_app.o(i.Get_System_State) refers to adc_app.o(.data) for system_state
    adc_app.o(i.Set_Limit) refers to adc_app.o(.data) for current_limit
    adc_app.o(i.Set_Over_Limit_State) refers to timer_app.o(i.Set_LED2_State) for Set_LED2_State
    adc_app.o(i.Set_Over_Limit_State) refers to adc_app.o(.data) for over_limit_state
    adc_app.o(i.Set_Ratio) refers to adc_app.o(.data) for current_ratio
    adc_app.o(i.Set_Sample_Interval) refers to timer_app.o(i.Set_ADC_Sample_Interval) for Set_ADC_Sample_Interval
    adc_app.o(i.Set_Sample_Interval) refers to adc_app.o(.data) for sample_interval
    adc_app.o(i.Set_System_State) refers to adc_app.o(i.Update_LED_Status) for Update_LED_Status
    adc_app.o(i.Set_System_State) refers to adc_app.o(.data) for system_state
    adc_app.o(i.Start_Sampling) refers to timer_app.o(i.Set_ADC_Sampling_State) for Set_ADC_Sampling_State
    adc_app.o(i.Start_Sampling) refers to timer_app.o(i.Set_ADC_Sample_Interval) for Set_ADC_Sample_Interval
    adc_app.o(i.Start_Sampling) refers to sdcard_app.o(i.Create_Sample_File) for Create_Sample_File
    adc_app.o(i.Start_Sampling) refers to adc_app.o(i.Update_LED_Status) for Update_LED_Status
    adc_app.o(i.Start_Sampling) refers to adc_app.o(.data) for adc_state
    adc_app.o(i.Stop_Sampling) refers to timer_app.o(i.Set_ADC_Sampling_State) for Set_ADC_Sampling_State
    adc_app.o(i.Stop_Sampling) refers to sdcard_app.o(i.Close_Sample_File) for Close_Sample_File
    adc_app.o(i.Stop_Sampling) refers to sdcard_app.o(i.Close_OverLimit_File) for Close_OverLimit_File
    adc_app.o(i.Stop_Sampling) refers to adc_app.o(i.Update_LED_Status) for Update_LED_Status
    adc_app.o(i.Stop_Sampling) refers to adc_app.o(.data) for adc_state
    adc_app.o(i.Update_LED_Status) refers to timer_app.o(i.Set_LED1_Blink_Mode) for Set_LED1_Blink_Mode
    adc_app.o(i.Update_LED_Status) refers to timer_app.o(i.Set_LED2_State) for Set_LED2_State
    adc_app.o(i.Update_LED_Status) refers to adc_app.o(.data) for system_state
    flash_app.o(i.Cache_Log_To_Flash) refers to strlen.o(.text) for strlen
    flash_app.o(i.Cache_Log_To_Flash) refers to strstr.o(.text) for strstr
    flash_app.o(i.Cache_Log_To_Flash) refers to memseta.o(.text) for __aeabi_memset
    flash_app.o(i.Cache_Log_To_Flash) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    flash_app.o(i.Cache_Log_To_Flash) refers to memcpya.o(.text) for __aeabi_memcpy
    flash_app.o(i.Cache_Log_To_Flash) refers to spi_flash.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    flash_app.o(i.Cache_Log_To_Flash) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    flash_app.o(i.Cache_Log_To_Flash) refers to spi_flash.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    flash_app.o(i.Check_Cached_Log_In_Flash) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    flash_app.o(i.Flash_Init) refers to usart.o(i.rs485_printf) for rs485_printf
    flash_app.o(i.Flash_Init) refers to spi_flash.o(i.spi_flash_init) for spi_flash_init
    flash_app.o(i.Flash_Init) refers to spi_flash.o(i.spi_flash_read_id) for spi_flash_read_id
    flash_app.o(i.Flash_Init) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    flash_app.o(i.Flash_Init) refers to spi_flash.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    flash_app.o(i.Flash_Init) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    flash_app.o(i.Flash_Init) refers to strlen.o(.text) for strlen
    flash_app.o(i.Flash_Init) refers to spi_flash.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    flash_app.o(i.Flash_Init) refers to flash_app.o(i.Read_Config_From_Flash) for Read_Config_From_Flash
    flash_app.o(i.Flash_Init) refers to adc_app.o(i.Set_Ratio) for Set_Ratio
    flash_app.o(i.Flash_Init) refers to adc_app.o(i.Set_Limit) for Set_Limit
    flash_app.o(i.Flash_Init) refers to adc_app.o(i.Set_Sample_Interval) for Set_Sample_Interval
    flash_app.o(i.Flash_Init) refers to flash_app.o(i.Log_Init) for Log_Init
    flash_app.o(i.Flash_Init) refers to adc_app.o(i.Set_System_State) for Set_System_State
    flash_app.o(i.Flash_Init) refers to flash_app.o(.data) for flash_id
    flash_app.o(i.Flash_Init) refers to key_app.o(.data) for current_sample_period
    flash_app.o(i.Log_Init) refers to flash_app.o(.data) for current_log_id
    flash_app.o(i.Read_Config_From_Flash) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    flash_app.o(i.Read_Config_From_Flash) refers to flash_app.o(i.Verify_Config_Data) for Verify_Config_Data
    flash_app.o(i.Read_Multi_Channel_Config_From_Flash) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    flash_app.o(i.Read_Multi_Channel_Config_From_Flash) refers to flash_app.o(i.Verify_Multi_Channel_Config_Data) for Verify_Multi_Channel_Config_Data
    flash_app.o(i.Save_Config_To_Flash) refers to flash_app.o(i.Calculate_Config_Checksum) for Calculate_Config_Checksum
    flash_app.o(i.Save_Config_To_Flash) refers to spi_flash.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    flash_app.o(i.Save_Config_To_Flash) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    flash_app.o(i.Save_Config_To_Flash) refers to spi_flash.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    flash_app.o(i.Save_Multi_Channel_Config_To_Flash) refers to flash_app.o(i.Calculate_Multi_Channel_Config_Checksum) for Calculate_Multi_Channel_Config_Checksum
    flash_app.o(i.Save_Multi_Channel_Config_To_Flash) refers to spi_flash.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    flash_app.o(i.Save_Multi_Channel_Config_To_Flash) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    flash_app.o(i.Save_Multi_Channel_Config_To_Flash) refers to spi_flash.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    flash_app.o(i.Verify_Config_Data) refers to flash_app.o(i.Calculate_Config_Checksum) for Calculate_Config_Checksum
    flash_app.o(i.Verify_Multi_Channel_Config_Data) refers to flash_app.o(i.Calculate_Multi_Channel_Config_Checksum) for Calculate_Multi_Channel_Config_Checksum
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to flash_app.o(i.Check_Cached_Log_In_Flash) for Check_Cached_Log_In_Flash
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to ff.o(i.f_mount) for f_mount
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to ff.o(i.f_mkdir) for f_mkdir
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to ff.o(i.f_open) for f_open
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to ff.o(i.f_lseek) for f_lseek
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to ff.o(i.f_write) for f_write
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to ff.o(i.f_sync) for f_sync
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to ff.o(i.f_close) for f_close
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to spi_flash.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to sdcard_app.o(.bss) for fs
    function.o(i.System_Init) refers to systick.o(i.systick_config) for systick_config
    function.o(i.UsrFunction) refers to oled.o(i.OLED_Init) for OLED_Init
    function.o(i.UsrFunction) refers to oled_app.o(i.OLED_App_Init) for OLED_App_Init
    function.o(i.UsrFunction) refers to key.o(i.Key_Init) for Key_Init
    function.o(i.UsrFunction) refers to led.o(i.Led_Init) for Led_Init
    function.o(i.UsrFunction) refers to usart.o(i.rs485_usart1_config) for rs485_usart1_config
    function.o(i.UsrFunction) refers to gd30ad3344.o(i.gd30ad3344_init) for gd30ad3344_init
    function.o(i.UsrFunction) refers to adc.o(i.ADC_Init) for ADC_Init
    function.o(i.UsrFunction) refers to scheduler.o(i.scheduler_init) for scheduler_init
    function.o(i.UsrFunction) refers to rtc.o(i.RTC_Init) for RTC_Init
    function.o(i.UsrFunction) refers to flash_app.o(i.Flash_Init) for Flash_Init
    function.o(i.UsrFunction) refers to sdcard_app.o(i.Init_Data_Recording) for Init_Data_Recording
    function.o(i.UsrFunction) refers to sdcard_app.o(i.Create_Default_Config_INI) for Create_Default_Config_INI
    function.o(i.UsrFunction) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    function.o(i.UsrFunction) refers to systick.o(i.delay_1ms) for delay_1ms
    function.o(i.UsrFunction) refers to timer.o(i.Timer_Init) for Timer_Init
    function.o(i.UsrFunction) refers to scheduler.o(i.scheduler_run) for scheduler_run
    key_app.o(i.Get_Sample_Period) refers to key_app.o(.data) for current_sample_period
    key_app.o(i.Handle_Key1_Press) refers to key_app.o(i.Update_Current_Display) for Update_Current_Display
    key_app.o(i.Handle_Key1_Press) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    key_app.o(i.Handle_Key1_Press) refers to key_app.o(.data) for current_display_mode
    key_app.o(i.Handle_Key2_Press) refers to key_app.o(i.Update_Current_Display) for Update_Current_Display
    key_app.o(i.Handle_Key2_Press) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    key_app.o(i.Handle_Key2_Press) refers to key_app.o(.data) for current_display_mode
    key_app.o(i.Handle_Key3_Press) refers to key_app.o(i.Update_Current_Display) for Update_Current_Display
    key_app.o(i.Handle_Key3_Press) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    key_app.o(i.Handle_Key3_Press) refers to key_app.o(.data) for current_display_mode
    key_app.o(i.Handle_Key4_Press) refers to key_app.o(i.Update_Current_Display) for Update_Current_Display
    key_app.o(i.Handle_Key4_Press) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    key_app.o(i.Handle_Key4_Press) refers to key_app.o(.data) for current_display_mode
    key_app.o(i.Handle_Key5_Press) refers to key_app.o(i.Update_Current_Display) for Update_Current_Display
    key_app.o(i.Handle_Key5_Press) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    key_app.o(i.Handle_Key5_Press) refers to key_app.o(.data) for current_display_mode
    key_app.o(i.Handle_Key6_Press) refers to key_app.o(i.Update_Current_Display) for Update_Current_Display
    key_app.o(i.Handle_Key6_Press) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    key_app.o(i.Handle_Key6_Press) refers to key_app.o(.data) for current_display_mode
    key_app.o(i.Key_Proc) refers to key.o(i.Key_Read) for Key_Read
    key_app.o(i.Key_Proc) refers to key_app.o(i.Handle_Key1_Press) for Handle_Key1_Press
    key_app.o(i.Key_Proc) refers to key_app.o(i.Handle_Key2_Press) for Handle_Key2_Press
    key_app.o(i.Key_Proc) refers to key_app.o(i.Handle_Key3_Press) for Handle_Key3_Press
    key_app.o(i.Key_Proc) refers to key_app.o(i.Handle_Key4_Press) for Handle_Key4_Press
    key_app.o(i.Key_Proc) refers to key_app.o(i.Handle_Key5_Press) for Handle_Key5_Press
    key_app.o(i.Key_Proc) refers to key_app.o(i.Handle_Key6_Press) for Handle_Key6_Press
    key_app.o(i.Key_Proc) refers to key_app.o(.data) for key_val
    key_app.o(i.Set_Sample_Period) refers to adc_app.o(i.Set_Sample_Interval) for Set_Sample_Interval
    key_app.o(i.Set_Sample_Period) refers to adc_app.o(i.Get_Ratio) for Get_Ratio
    key_app.o(i.Set_Sample_Period) refers to adc_app.o(i.Get_Limit) for Get_Limit
    key_app.o(i.Set_Sample_Period) refers to flash_app.o(i.Save_Config_To_Flash) for Save_Config_To_Flash
    key_app.o(i.Set_Sample_Period) refers to usart.o(i.rs485_printf) for rs485_printf
    key_app.o(i.Set_Sample_Period) refers to key_app.o(.data) for current_sample_period
    key_app.o(i.Update_Current_Display) refers to strcpy.o(.text) for strcpy
    key_app.o(i.Update_Current_Display) refers to f2d.o(.text) for __aeabi_f2d
    key_app.o(i.Update_Current_Display) refers to printfa.o(i.__0snprintf) for __2snprintf
    key_app.o(i.Update_Current_Display) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    key_app.o(i.Update_Current_Display) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    key_app.o(i.Update_Current_Display) refers to key_app.o(.data) for current_display_mode
    key_app.o(i.Update_Current_Display) refers to oled_app.o(.data) for oled1_buffer
    key_app.o(i.Update_Current_Display) refers to adc_app.o(.data) for ch0_data_valid
    key_app.o(i.Update_Current_Display) refers to oled_app.o(.bss) for oled2_buffer
    oled_app.o(i.OLED_App_Init) refers to strcpy.o(.text) for strcpy
    oled_app.o(i.OLED_App_Init) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled_app.o(i.OLED_App_Init) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled_app.o(i.OLED_App_Init) refers to oled_app.o(.data) for oled1_buffer
    oled_app.o(i.OLED_App_Init) refers to oled_app.o(.bss) for oled2_buffer
    rtc_app.o(i.Configure_RTC_With_DateTime) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rtc_app.o(i.Configure_RTC_With_DateTime) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    rtc_app.o(i.Configure_RTC_With_DateTime) refers to rtc.o(i.rtc_pre_config) for rtc_pre_config
    rtc_app.o(i.Configure_RTC_With_DateTime) refers to rtc_app.o(i.convert_to_bcd) for convert_to_bcd
    rtc_app.o(i.Configure_RTC_With_DateTime) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc_app.o(i.Configure_RTC_With_DateTime) refers to rtc.o(.data) for prescaler_a
    rtc_app.o(i.RTC_Proc) refers to rtc.o(i.rtc_show_time) for rtc_show_time
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for task_num
    scheduler.o(i.scheduler_run) refers to function.o(.data) for uwTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for scheduler_task
    scheduler.o(.data) refers to key_app.o(i.Key_Proc) for Key_Proc
    scheduler.o(.data) refers to usart_app.o(i.RS485_Task) for RS485_Task
    scheduler.o(.data) refers to adc_app.o(i.ADC_Proc) for ADC_Proc
    sdcard_app.o(i.Check_TF_Card) refers to diskio.o(i.disk_initialize) for disk_initialize
    sdcard_app.o(i.Close_HideData_File) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Close_HideData_File) refers to sdcard_app.o(.bss) for hidedata_file
    sdcard_app.o(i.Close_HideData_File) refers to sdcard_app.o(.data) for hidedata_record_count
    sdcard_app.o(i.Close_OverLimit_File) refers to memseta.o(.text) for __aeabi_memclr4
    sdcard_app.o(i.Close_OverLimit_File) refers to sdcard_app.o(.data) for overlimit_record_count
    sdcard_app.o(i.Close_OverLimit_File) refers to sdcard_app.o(.bss) for current_overlimit_filename
    sdcard_app.o(i.Close_Sample_File) refers to memseta.o(.text) for __aeabi_memclr4
    sdcard_app.o(i.Close_Sample_File) refers to sdcard_app.o(.data) for record_state
    sdcard_app.o(i.Close_Sample_File) refers to sdcard_app.o(.bss) for current_filename
    sdcard_app.o(i.Create_Default_Config_INI) refers to memcpya.o(.text) for __aeabi_memcpy4
    sdcard_app.o(i.Create_Default_Config_INI) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Create_Default_Config_INI) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Create_Default_Config_INI) refers to strlen.o(.text) for strlen
    sdcard_app.o(i.Create_Default_Config_INI) refers to ff.o(i.f_write) for f_write
    sdcard_app.o(i.Create_Default_Config_INI) refers to ff.o(i.f_sync) for f_sync
    sdcard_app.o(i.Create_Default_Config_INI) refers to sdcard_app.o(.conststring) for .conststring
    sdcard_app.o(i.Create_HideData_File) refers to sdcard_app.o(i.Generate_HideData_Filename) for Generate_HideData_Filename
    sdcard_app.o(i.Create_HideData_File) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Create_HideData_File) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Create_HideData_File) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Create_HideData_File) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Create_HideData_File) refers to sdcard_app.o(.bss) for hidedata_file
    sdcard_app.o(i.Create_HideData_File) refers to sdcard_app.o(.data) for hidedata_record_count
    sdcard_app.o(i.Create_Log0_File) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Create_Log0_File) refers to ff.o(i.f_sync) for f_sync
    sdcard_app.o(i.Create_Log0_File) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Create_Log_File) refers to ff.o(i.f_mount) for f_mount
    sdcard_app.o(i.Create_Log_File) refers to ff.o(i.f_mkdir) for f_mkdir
    sdcard_app.o(i.Create_Log_File) refers to sdcard_app.o(i.Generate_Log_Filename) for Generate_Log_Filename
    sdcard_app.o(i.Create_Log_File) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Create_Log_File) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Create_Log_File) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Create_Log_File) refers to sdcard_app.o(.bss) for fs
    sdcard_app.o(i.Create_Log_File) refers to flash_app.o(.data) for current_log_id
    sdcard_app.o(i.Create_Sample_File) refers to sdcard_app.o(i.Generate_Filename) for Generate_Filename
    sdcard_app.o(i.Create_Sample_File) refers to sdcard_app.o(.data) for record_state
    sdcard_app.o(i.Create_Sample_File) refers to sdcard_app.o(.bss) for current_filename
    sdcard_app.o(i.Generate_Filename) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Generate_Filename) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Generate_HideData_Filename) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Generate_HideData_Filename) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Generate_Log_Filename) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Generate_Log_Filename) refers to flash_app.o(.data) for current_log_id
    sdcard_app.o(i.Generate_OverLimit_Filename) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Generate_OverLimit_Filename) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Get_Next_Log_ID_From_SD) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Get_Next_Log_ID_From_SD) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Get_Next_Log_ID_From_SD) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Init_Data_Recording) refers to sdcard_app.o(i.nvic_config) for nvic_config
    sdcard_app.o(i.Init_Data_Recording) refers to diskio.o(i.disk_initialize) for disk_initialize
    sdcard_app.o(i.Init_Data_Recording) refers to ff.o(i.f_mount) for f_mount
    sdcard_app.o(i.Init_Data_Recording) refers to ff.o(i.f_mkdir) for f_mkdir
    sdcard_app.o(i.Init_Data_Recording) refers to ff.o(i.f_opendir) for f_opendir
    sdcard_app.o(i.Init_Data_Recording) refers to sdcard_app.o(i.Create_Log0_File) for Create_Log0_File
    sdcard_app.o(i.Init_Data_Recording) refers to flash_app.o(i.Check_Cached_Log_In_Flash) for Check_Cached_Log_In_Flash
    sdcard_app.o(i.Init_Data_Recording) refers to flash_app.o(i.Write_Cached_Log_To_Log0) for Write_Cached_Log_To_Log0
    sdcard_app.o(i.Init_Data_Recording) refers to sdcard_app.o(i.Get_Next_Log_ID_From_SD) for Get_Next_Log_ID_From_SD
    sdcard_app.o(i.Init_Data_Recording) refers to sdcard_app.o(.bss) for fs
    sdcard_app.o(i.Init_Data_Recording) refers to flash_app.o(.data) for current_log_id
    sdcard_app.o(i.Init_Data_Recording) refers to sdcard_app.o(.data) for log_file_created
    sdcard_app.o(i.Is_Log_Folder_Missing) refers to ff.o(i.f_mount) for f_mount
    sdcard_app.o(i.Is_Log_Folder_Missing) refers to ff.o(i.f_opendir) for f_opendir
    sdcard_app.o(i.Is_Log_Folder_Missing) refers to sdcard_app.o(.bss) for fs
    sdcard_app.o(i.Update_Config_INI) refers to f2d.o(.text) for __aeabi_f2d
    sdcard_app.o(i.Update_Config_INI) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Update_Config_INI) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Update_Config_INI) refers to strlen.o(.text) for strlen
    sdcard_app.o(i.Update_Config_INI) refers to ff.o(i.f_write) for f_write
    sdcard_app.o(i.Update_Config_INI) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Update_Config_INI) refers to ff.o(i.f_sync) for f_sync
    sdcard_app.o(i.Update_Config_INI) refers to sdcard_app.o(.conststring) for .conststring
    sdcard_app.o(i.Update_Config_INI_Ratio_Only) refers to adc_app.o(i.Get_Limit) for Get_Limit
    sdcard_app.o(i.Update_Config_INI_Ratio_Only) refers to sdcard_app.o(i.Update_Config_INI) for Update_Config_INI
    sdcard_app.o(i.Write_HideData) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Write_HideData) refers to sdcard_app.o(i.Create_HideData_File) for Create_HideData_File
    sdcard_app.o(i.Write_HideData) refers to f2d.o(.text) for __aeabi_f2d
    sdcard_app.o(i.Write_HideData) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Write_HideData) refers to strlen.o(.text) for strlen
    sdcard_app.o(i.Write_HideData) refers to ff.o(i.f_write) for f_write
    sdcard_app.o(i.Write_HideData) refers to ff.o(i.f_sync) for f_sync
    sdcard_app.o(i.Write_HideData) refers to sdcard_app.o(.data) for hidedata_record_count
    sdcard_app.o(i.Write_HideData) refers to sdcard_app.o(.bss) for hidedata_file
    sdcard_app.o(i.Write_Log_Data) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Write_Log_Data) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Write_Log_Data) refers to sdcard_app.o(i.Create_Log_File) for Create_Log_File
    sdcard_app.o(i.Write_Log_Data) refers to flash_app.o(i.Cache_Log_To_Flash) for Cache_Log_To_Flash
    sdcard_app.o(i.Write_Log_Data) refers to strcmp.o(.text) for strcmp
    sdcard_app.o(i.Write_Log_Data) refers to strncpy.o(.text) for strncpy
    sdcard_app.o(i.Write_Log_Data) refers to strlen.o(.text) for strlen
    sdcard_app.o(i.Write_Log_Data) refers to ff.o(i.f_write) for f_write
    sdcard_app.o(i.Write_Log_Data) refers to ff.o(i.f_sync) for f_sync
    sdcard_app.o(i.Write_Log_Data) refers to sdcard_app.o(.data) for log_file_created
    sdcard_app.o(i.Write_Log_Data) refers to sdcard_app.o(.bss) for filebuffer
    sdcard_app.o(i.Write_OverLimit_Data) refers to sdcard_app.o(i.Generate_OverLimit_Filename) for Generate_OverLimit_Filename
    sdcard_app.o(i.Write_OverLimit_Data) refers to f2d.o(.text) for __aeabi_f2d
    sdcard_app.o(i.Write_OverLimit_Data) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Write_OverLimit_Data) refers to strncpy.o(.text) for strncpy
    sdcard_app.o(i.Write_OverLimit_Data) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Write_OverLimit_Data) refers to ff.o(i.f_lseek) for f_lseek
    sdcard_app.o(i.Write_OverLimit_Data) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Write_OverLimit_Data) refers to strlen.o(.text) for strlen
    sdcard_app.o(i.Write_OverLimit_Data) refers to ff.o(i.f_write) for f_write
    sdcard_app.o(i.Write_OverLimit_Data) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Write_OverLimit_Data) refers to sdcard_app.o(.data) for overlimit_record_count
    sdcard_app.o(i.Write_OverLimit_Data) refers to sdcard_app.o(.bss) for current_overlimit_filename
    sdcard_app.o(i.Write_Sample_Data) refers to sdcard_app.o(i.Generate_Filename) for Generate_Filename
    sdcard_app.o(i.Write_Sample_Data) refers to f2d.o(.text) for __aeabi_f2d
    sdcard_app.o(i.Write_Sample_Data) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Write_Sample_Data) refers to strncpy.o(.text) for strncpy
    sdcard_app.o(i.Write_Sample_Data) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Write_Sample_Data) refers to ff.o(i.f_lseek) for f_lseek
    sdcard_app.o(i.Write_Sample_Data) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Write_Sample_Data) refers to strlen.o(.text) for strlen
    sdcard_app.o(i.Write_Sample_Data) refers to ff.o(i.f_write) for f_write
    sdcard_app.o(i.Write_Sample_Data) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Write_Sample_Data) refers to sdcard_app.o(.data) for record_state
    sdcard_app.o(i.Write_Sample_Data) refers to sdcard_app.o(.bss) for current_filename
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to sdcard_app.o(i.Generate_Filename) for Generate_Filename
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to f2d.o(.text) for __aeabi_f2d
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to strncpy.o(.text) for strncpy
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to ff.o(i.f_lseek) for f_lseek
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to strlen.o(.text) for strlen
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to ff.o(i.f_write) for f_write
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to ff.o(i.f_sync) for f_sync
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to sdcard_app.o(.data) for record_state
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to sdcard_app.o(.bss) for current_filename
    sdcard_app.o(i.ff_convert) refers to sdcard_app.o(.constdata) for unicode_table
    sdcard_app.o(i.nvic_config) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    sdcard_app.o(i.nvic_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    timer_app.o(i.Set_ADC_Sample_Interval) refers to timer_app.o(.data) for adc_target_count
    timer_app.o(i.Set_ADC_Sampling_State) refers to timer_app.o(.data) for adc_sampling_state
    timer_app.o(i.Set_LED1_Blink_Mode) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    timer_app.o(i.Set_LED1_Blink_Mode) refers to timer_app.o(.data) for led1_blink_mode
    timer_app.o(i.Set_LED2_State) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    timer_app.o(i.Set_LED2_State) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    timer_app.o(i.Timer_ADC_Handler) refers to gd30ad3344.o(i.GD30AD3344_AD_Read) for GD30AD3344_AD_Read
    timer_app.o(i.Timer_ADC_Handler) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    timer_app.o(i.Timer_ADC_Handler) refers to usart_app.o(i.Generate_Encrypted_Output_With_Values_OverLimit) for Generate_Encrypted_Output_With_Values_OverLimit
    timer_app.o(i.Timer_ADC_Handler) refers to f2d.o(.text) for __aeabi_f2d
    timer_app.o(i.Timer_ADC_Handler) refers to usart.o(i.rs485_printf) for rs485_printf
    timer_app.o(i.Timer_ADC_Handler) refers to adc_app.o(i.Set_Over_Limit_State) for Set_Over_Limit_State
    timer_app.o(i.Timer_ADC_Handler) refers to printfa.o(i.__0snprintf) for __2snprintf
    timer_app.o(i.Timer_ADC_Handler) refers to sdcard_app.o(i.Write_OverLimit_Data) for Write_OverLimit_Data
    timer_app.o(i.Timer_ADC_Handler) refers to usart_app.o(i.Generate_Encrypted_Output_With_Values) for Generate_Encrypted_Output_With_Values
    timer_app.o(i.Timer_ADC_Handler) refers to sdcard_app.o(i.Write_Sample_Data_MultiChannel) for Write_Sample_Data_MultiChannel
    timer_app.o(i.Timer_ADC_Handler) refers to key_app.o(i.Update_Current_Display) for Update_Current_Display
    timer_app.o(i.Timer_ADC_Handler) refers to timer_app.o(.data) for adc_sampling_state
    timer_app.o(i.Timer_ADC_Handler) refers to adc_app.o(.data) for read_mode
    timer_app.o(i.Timer_ADC_Handler) refers to adc_app.o(.bss) for latest_data
    timer_app.o(i.Timer_ADC_Handler) refers to function.o(.data) for uwTick
    timer_app.o(i.Timer_ADC_Handler) refers to usart_app.o(.data) for current_output_mode
    timer_app.o(i.Timer_ADC_Handler) refers to timer_app.o(.conststring) for .conststring
    timer_app.o(i.Timer_LED_Handler) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    timer_app.o(i.Timer_LED_Handler) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    timer_app.o(i.Timer_LED_Handler) refers to timer_app.o(.data) for led1_blink_mode
    usart_app.o(i.Convert_RTC_To_Unix_Timestamp) refers to usart_app.o(i.bcd_to_decimal) for bcd_to_decimal
    usart_app.o(i.Convert_RTC_To_Unix_Timestamp) refers to usart_app.o(i.is_leap_year) for is_leap_year
    usart_app.o(i.Convert_RTC_To_Unix_Timestamp) refers to usart_app.o(i.get_days_in_month) for get_days_in_month
    usart_app.o(i.Generate_Encrypted_Output_With_Values) refers to usart_app.o(i.Convert_RTC_To_Unix_Timestamp) for Convert_RTC_To_Unix_Timestamp
    usart_app.o(i.Generate_Encrypted_Output_With_Values) refers to usart_app.o(i.Encode_Voltage_To_Hex) for Encode_Voltage_To_Hex
    usart_app.o(i.Generate_Encrypted_Output_With_Values) refers to usart_app.o(i.Convert_Uint32_To_Hex_String) for Convert_Uint32_To_Hex_String
    usart_app.o(i.Generate_Encrypted_Output_With_Values) refers to printfa.o(i.__0snprintf) for __2snprintf
    usart_app.o(i.Generate_Encrypted_Output_With_Values) refers to printfa.o(i.__0printf) for __2printf
    usart_app.o(i.Generate_Encrypted_Output_With_Values) refers to sdcard_app.o(i.Write_HideData) for Write_HideData
    usart_app.o(i.Generate_Encrypted_Output_With_Values_OverLimit) refers to usart_app.o(i.Convert_RTC_To_Unix_Timestamp) for Convert_RTC_To_Unix_Timestamp
    usart_app.o(i.Generate_Encrypted_Output_With_Values_OverLimit) refers to usart_app.o(i.Encode_Voltage_To_Hex) for Encode_Voltage_To_Hex
    usart_app.o(i.Generate_Encrypted_Output_With_Values_OverLimit) refers to usart_app.o(i.Convert_Uint32_To_Hex_String) for Convert_Uint32_To_Hex_String
    usart_app.o(i.Generate_Encrypted_Output_With_Values_OverLimit) refers to printfa.o(i.__0printf) for __2printf
    usart_app.o(i.Get_Device_ID) refers to usart_app.o(.data) for MYDEVICE_ID
    usart_app.o(i.Handle_Command_Get_Limit) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Command_Get_Limit) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Command_Get_Limit) refers to adc_app.o(.data) for ch2_limit
    usart_app.o(i.Handle_Command_Set_Limit) refers to strstr.o(.text) for strstr
    usart_app.o(i.Handle_Command_Set_Limit) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    usart_app.o(i.Handle_Command_Set_Limit) refers to d2f.o(.text) for __aeabi_d2f
    usart_app.o(i.Handle_Command_Set_Limit) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Command_Set_Limit) refers to adc_app.o(i.Set_System_State) for Set_System_State
    usart_app.o(i.Handle_Command_Set_Limit) refers to sdcard_app.o(i.Update_Config_INI) for Update_Config_INI
    usart_app.o(i.Handle_Command_Set_Limit) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Command_Set_Limit) refers to printfa.o(i.__0snprintf) for __2snprintf
    usart_app.o(i.Handle_Command_Set_Limit) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Command_Set_Limit) refers to adc_app.o(.data) for ch0_limit
    usart_app.o(i.Handle_Command_Set_RTC) refers to strncpy.o(.text) for strncpy
    usart_app.o(i.Handle_Command_Set_RTC) refers to strcpy.o(.text) for strcpy
    usart_app.o(i.Handle_Command_Set_RTC) refers to atoi.o(.text) for atoi
    usart_app.o(i.Handle_Command_Set_RTC) refers to rtc_app.o(i.Configure_RTC_With_DateTime) for Configure_RTC_With_DateTime
    usart_app.o(i.Handle_Command_Set_RTC) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Command_Set_RTC) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    usart_app.o(i.Handle_Command_Set_RTC) refers to printfa.o(i.__0snprintf) for __2snprintf
    usart_app.o(i.Handle_Command_Set_RTC) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Command_Set_Ratio) refers to strstr.o(.text) for strstr
    usart_app.o(i.Handle_Command_Set_Ratio) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    usart_app.o(i.Handle_Command_Set_Ratio) refers to d2f.o(.text) for __aeabi_d2f
    usart_app.o(i.Handle_Command_Set_Ratio) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Command_Set_Ratio) refers to adc_app.o(i.Set_System_State) for Set_System_State
    usart_app.o(i.Handle_Command_Set_Ratio) refers to sdcard_app.o(i.Update_Config_INI) for Update_Config_INI
    usart_app.o(i.Handle_Command_Set_Ratio) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Command_Set_Ratio) refers to printfa.o(i.__0snprintf) for __2snprintf
    usart_app.o(i.Handle_Command_Set_Ratio) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Command_Set_Ratio) refers to adc_app.o(.data) for ch0_ratio
    usart_app.o(i.Handle_Conf_Command) refers to ff.o(i.f_open) for f_open
    usart_app.o(i.Handle_Conf_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Conf_Command) refers to ff.o(i.f_read) for f_read
    usart_app.o(i.Handle_Conf_Command) refers to ff.o(i.f_close) for f_close
    usart_app.o(i.Handle_Conf_Command) refers to strtok.o(.text) for strtok
    usart_app.o(i.Handle_Conf_Command) refers to strlen.o(.text) for strlen
    usart_app.o(i.Handle_Conf_Command) refers to strstr.o(.text) for strstr
    usart_app.o(i.Handle_Conf_Command) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    usart_app.o(i.Handle_Conf_Command) refers to d2f.o(.text) for __aeabi_d2f
    usart_app.o(i.Handle_Conf_Command) refers to adc_app.o(i.Set_Ratio) for Set_Ratio
    usart_app.o(i.Handle_Conf_Command) refers to adc_app.o(i.Set_Limit) for Set_Limit
    usart_app.o(i.Handle_Conf_Command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Config_Read_Command) refers to adc_app.o(i.Set_System_State) for Set_System_State
    usart_app.o(i.Handle_Config_Read_Command) refers to flash_app.o(i.Read_Config_From_Flash) for Read_Config_From_Flash
    usart_app.o(i.Handle_Config_Read_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Config_Read_Command) refers to adc_app.o(i.Set_Ratio) for Set_Ratio
    usart_app.o(i.Handle_Config_Read_Command) refers to adc_app.o(i.Set_Limit) for Set_Limit
    usart_app.o(i.Handle_Config_Read_Command) refers to adc_app.o(i.Set_Sample_Interval) for Set_Sample_Interval
    usart_app.o(i.Handle_Config_Read_Command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Config_Read_Command) refers to adc_app.o(i.Get_Ratio) for Get_Ratio
    usart_app.o(i.Handle_Config_Read_Command) refers to adc_app.o(i.Get_Limit) for Get_Limit
    usart_app.o(i.Handle_Config_Save_Command) refers to adc_app.o(i.Set_System_State) for Set_System_State
    usart_app.o(i.Handle_Config_Save_Command) refers to adc_app.o(i.Get_Ratio) for Get_Ratio
    usart_app.o(i.Handle_Config_Save_Command) refers to adc_app.o(i.Get_Limit) for Get_Limit
    usart_app.o(i.Handle_Config_Save_Command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Config_Save_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Config_Save_Command) refers to adc_app.o(i.Get_Sample_Interval) for Get_Sample_Interval
    usart_app.o(i.Handle_Config_Save_Command) refers to flash_app.o(i.Save_Config_To_Flash) for Save_Config_To_Flash
    usart_app.o(i.Handle_Get_Data_Command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Get_Data_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Get_Data_Command) refers to adc_app.o(.data) for read_mode
    usart_app.o(i.Handle_Get_Device_ID_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Get_Device_ID_Command) refers to usart_app.o(.data) for MYDEVICE_ID
    usart_app.o(i.Handle_Get_Ratio_Command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Get_Ratio_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Get_Ratio_Command) refers to printfa.o(i.__0snprintf) for __2snprintf
    usart_app.o(i.Handle_Get_Ratio_Command) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Get_Ratio_Command) refers to adc_app.o(.data) for ch2_ratio
    usart_app.o(i.Handle_Hide_Command) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Hide_Command) refers to usart_app.o(.data) for current_output_mode
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to adc_app.o(i.Set_System_State) for Set_System_State
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to flash_app.o(i.Read_Multi_Channel_Config_From_Flash) for Read_Multi_Channel_Config_From_Flash
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to flash_app.o(i.Read_Config_From_Flash) for Read_Config_From_Flash
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to adc_app.o(i.Set_Sample_Interval) for Set_Sample_Interval
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to adc_app.o(i.Get_Sample_Interval) for Get_Sample_Interval
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to adc_app.o(.data) for ch0_ratio
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to adc_app.o(i.Set_System_State) for Set_System_State
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to adc_app.o(i.Get_Sample_Interval) for Get_Sample_Interval
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to flash_app.o(i.Save_Multi_Channel_Config_To_Flash) for Save_Multi_Channel_Config_To_Flash
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to adc_app.o(i.Get_Limit) for Get_Limit
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to adc_app.o(i.Get_Ratio) for Get_Ratio
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to flash_app.o(i.Save_Config_To_Flash) for Save_Config_To_Flash
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to adc_app.o(.data) for ch0_limit
    usart_app.o(i.Handle_RTC_Now_Command) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    usart_app.o(i.Handle_RTC_Now_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Start_Command) refers to adc_app.o(i.Start_Sampling) for Start_Sampling
    usart_app.o(i.Handle_Stop_Command) refers to adc_app.o(i.Stop_Sampling) for Stop_Sampling
    usart_app.o(i.Handle_Stop_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Stop_Command) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Test_Command) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Test_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Test_Command) refers to sdcard_app.o(i.Check_TF_Card) for Check_TF_Card
    usart_app.o(i.Handle_Test_Command) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    usart_app.o(i.Handle_Test_Command) refers to printfa.o(i.__0snprintf) for __2snprintf
    usart_app.o(i.Handle_Test_Command) refers to flash_app.o(i.Cache_Log_To_Flash) for Cache_Log_To_Flash
    usart_app.o(i.Handle_Test_Command) refers to sdcard.o(i.sd_card_capacity_get) for sd_card_capacity_get
    usart_app.o(i.Handle_Test_Command) refers to flash_app.o(.data) for flash_id
    usart_app.o(i.Handle_Unhide_Command) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Unhide_Command) refers to sdcard_app.o(i.Close_HideData_File) for Close_HideData_File
    usart_app.o(i.Handle_Unhide_Command) refers to usart_app.o(.data) for current_output_mode
    usart_app.o(i.RS485_Task) refers to strncmp.o(.text) for strncmp
    usart_app.o(i.RS485_Task) refers to strcmp.o(.text) for strcmp
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Get_Device_ID_Command) for Handle_Get_Device_ID_Command
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Command_Set_RTC) for Handle_Command_Set_RTC
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_RTC_Now_Command) for Handle_RTC_Now_Command
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Command_Set_Ratio) for Handle_Command_Set_Ratio
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Get_Ratio_Command) for Handle_Get_Ratio_Command
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Get_Data_Command) for Handle_Get_Data_Command
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Command_Set_Limit) for Handle_Command_Set_Limit
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Command_Get_Limit) for Handle_Command_Get_Limit
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Start_Command) for Handle_Start_Command
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Stop_Command) for Handle_Stop_Command
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Multi_Config_Save_Command) for Handle_Multi_Config_Save_Command
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Multi_Config_Read_Command) for Handle_Multi_Config_Read_Command
    usart_app.o(i.RS485_Task) refers to memseta.o(.text) for __aeabi_memclr
    usart_app.o(i.RS485_Task) refers to usart.o(.data) for rs485_rx_index
    usart_app.o(i.RS485_Task) refers to function.o(.data) for uwTick
    usart_app.o(i.RS485_Task) refers to usart.o(.bss) for rs485_rx_buffer
    usart_app.o(i.RS485_Task) refers to usart_app.o(.data) for ratio_config_state
    usart_app.o(i.Set_Device_ID) refers to usart_app.o(.data) for MYDEVICE_ID
    usart_app.o(i.get_days_in_month) refers to usart_app.o(i.is_leap_year) for is_leap_year
    usart_app.o(i.get_days_in_month) refers to usart_app.o(.constdata) for days_in_month
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_240m_25m_hxtal) for system_clock_240m_25m_hxtal
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to timer.o(i.TIMER1_IRQHandler) for TIMER1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to usart.o(i.USART0_IRQHandler) for USART0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SDIO_IRQHandler) for SDIO_IRQHandler
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    ff.o(i.check_fs) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_status) for disk_status
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.chk_mounted) refers to ff.o(i.check_fs) for check_fs
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.chk_mounted) refers to ff.o(.data) for FatFs
    ff.o(i.cmp_lfn) refers to sdcard_app.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.cmp_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.create_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.create_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.create_name) refers to sdcard_app.o(i.ff_convert) for ff_convert
    ff.o(i.create_name) refers to ff.o(i.chk_chr) for chk_chr
    ff.o(i.create_name) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.create_name) refers to sdcard_app.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.dir_find) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_find) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_find) refers to ff.o(i.cmp_lfn) for cmp_lfn
    ff.o(i.dir_find) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_find) refers to ff.o(i.mem_cmp) for mem_cmp
    ff.o(i.dir_find) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_next) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_next) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.dir_next) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_next) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_next) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.dir_read) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_read) refers to ff.o(i.pick_lfn) for pick_lfn
    ff.o(i.dir_read) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_read) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.dir_register) refers to ff.o(i.gen_numname) for gen_numname
    ff.o(i.dir_register) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.dir_register) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_register) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_register) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_register) refers to ff.o(i.fit_lfn) for fit_lfn
    ff.o(i.dir_register) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_remove) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_remove) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_remove) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_sdi) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_sdi) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_chmod) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_chmod) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_chmod) refers to ff.o(i.sync) for sync
    ff.o(i.f_chmod) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_close) refers to ff.o(i.f_sync) for f_sync
    ff.o(i.f_getfree) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_getfree) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_getfree) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_lseek) refers to ff.o(i.validate) for validate
    ff.o(i.f_lseek) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_lseek) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_lseek) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_lseek) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_lseek) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_mkdir) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkdir) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_mkdir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_mkdir) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_mkdir) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_mkdir) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkdir) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkdir) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_mkdir) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_mkdir) refers to ff.o(i.sync) for sync
    ff.o(i.f_mkdir) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_mount) refers to ff.o(.data) for FatFs
    ff.o(i.f_open) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_open) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_open) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_open) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_open) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_open) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_open) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_opendir) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_opendir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_opendir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_opendir) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_read) refers to ff.o(i.validate) for validate
    ff.o(i.f_read) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_read) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_read) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_read) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_read) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_readdir) refers to ff.o(i.validate) for validate
    ff.o(i.f_readdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_readdir) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_readdir) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_readdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.f_readdir) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_rename) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_rename) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_rename) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_rename) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_rename) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_rename) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_rename) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_rename) refers to ff.o(i.sync) for sync
    ff.o(i.f_rename) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_stat) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_stat) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_stat) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_stat) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_sync) refers to ff.o(i.validate) for validate
    ff.o(i.f_sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_sync) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_sync) refers to ff.o(i.sync) for sync
    ff.o(i.f_truncate) refers to ff.o(i.validate) for validate
    ff.o(i.f_truncate) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_truncate) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_truncate) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.f_unlink) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_unlink) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_unlink) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_unlink) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_unlink) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_unlink) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_unlink) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_unlink) refers to ff.o(i.sync) for sync
    ff.o(i.f_unlink) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_utime) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_utime) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_utime) refers to ff.o(i.sync) for sync
    ff.o(i.f_utime) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_write) refers to ff.o(i.validate) for validate
    ff.o(i.f_write) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_write) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_write) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_write) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_write) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.fit_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.follow_path) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.follow_path) refers to ff.o(i.create_name) for create_name
    ff.o(i.follow_path) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.gen_numname) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.get_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.get_fileinfo) refers to sdcard_app.o(i.ff_convert) for ff_convert
    ff.o(i.move_window) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.move_window) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.pick_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.put_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.remove_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.remove_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.sync) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.sync) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.validate) refers to diskio.o(i.disk_status) for disk_status
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_init) for sd_init
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_card_information_get) for sd_card_information_get
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_card_select_deselect) for sd_card_select_deselect
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_cardstatus_get) for sd_cardstatus_get
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_bus_mode_config) for sd_bus_mode_config
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_transfer_mode_config) for sd_transfer_mode_config
    diskio.o(i.disk_read) refers to sdcard.o(i.sd_block_read) for sd_block_read
    diskio.o(i.disk_read) refers to sdcard.o(i.sd_multiblocks_read) for sd_multiblocks_read
    diskio.o(i.disk_write) refers to sdcard.o(i.sd_block_write) for sd_block_write
    diskio.o(i.disk_write) refers to sdcard.o(i.sd_multiblocks_write) for sd_multiblocks_write
    diskio.o(i.get_fattime) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    atof.o(i.__hardfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__hardfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.__softfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__softfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers to errno.o(i.__set_errno) for __set_errno
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    strtok.o(.text) refers to strtok.o(.data) for .data
    strtok_r.o(.text) refers to strtok_r.o(.data) for .data
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtod.o(.text) refers to scanf_fp.o(.text) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace_o.o(.text) for isspace
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(i.OLED_ColorTurn), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_Off), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_On), (28 bytes).
    Removing oled.o(i.OLED_DisplayTurn), (44 bytes).
    Removing oled.o(i.OLED_DrawCircle), (152 bytes).
    Removing oled.o(i.OLED_DrawLine), (146 bytes).
    Removing oled.o(i.OLED_Pow), (22 bytes).
    Removing oled.o(i.OLED_ScrollDisplay), (164 bytes).
    Removing oled.o(i.OLED_ShowChinese), (264 bytes).
    Removing oled.o(i.OLED_ShowNum), (116 bytes).
    Removing oled.o(i.OLED_ShowPicture), (78 bytes).
    Removing oled.o(i.OLED_WR_BP), (46 bytes).
    Removing oled.o(.data), (5688 bytes).
    Removing rtc.o(.rev16_text), (4 bytes).
    Removing rtc.o(.revsh_text), (4 bytes).
    Removing rtc.o(i.rtc_setup), (668 bytes).
    Removing rtc.o(i.rtc_show_alarm), (64 bytes).
    Removing rtc.o(i.rtc_show_time), (88 bytes).
    Removing rtc.o(i.usart_input_threshold), (252 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing sdcard.o(.rev16_text), (4 bytes).
    Removing sdcard.o(.revsh_text), (4 bytes).
    Removing sdcard.o(i.sd_card_capacity_get), (168 bytes).
    Removing sdcard.o(i.sd_erase), (324 bytes).
    Removing sdcard.o(i.sd_lock_unlock), (488 bytes).
    Removing sdcard.o(i.sd_power_off), (14 bytes).
    Removing sdcard.o(i.sd_sdstatus_get), (428 bytes).
    Removing sdcard.o(i.sd_transfer_state_get), (20 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(i.ADC_Read), (48 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing gd30ad3344.o(.rev16_text), (4 bytes).
    Removing gd30ad3344.o(.revsh_text), (4 bytes).
    Removing gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma), (252 bytes).
    Removing gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma), (296 bytes).
    Removing gd30ad3344.o(i.spi_gd30ad3344_wait_for_dma_end), (44 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(i.usart0_config), (228 bytes).
    Removing spi_flash.o(.rev16_text), (4 bytes).
    Removing spi_flash.o(.revsh_text), (4 bytes).
    Removing spi_flash.o(i.spi_flash_buffer_erase), (332 bytes).
    Removing spi_flash.o(i.spi_flash_bulk_erase), (44 bytes).
    Removing spi_flash.o(i.spi_flash_read_byte), (10 bytes).
    Removing spi_flash.o(i.spi_flash_send_halfword), (52 bytes).
    Removing spi_flash.o(i.spi_flash_start_read_sequence), (48 bytes).
    Removing adc_app.o(.rev16_text), (4 bytes).
    Removing adc_app.o(.revsh_text), (4 bytes).
    Removing adc_app.o(i.ADC_To_Voltage), (48 bytes).
    Removing adc_app.o(i.Get_ADC_State), (12 bytes).
    Removing adc_app.o(i.Get_Latest_Data), (16 bytes).
    Removing adc_app.o(i.Get_System_State), (12 bytes).
    Removing flash_app.o(.rev16_text), (4 bytes).
    Removing flash_app.o(.revsh_text), (4 bytes).
    Removing function.o(.rev16_text), (4 bytes).
    Removing function.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(i.Get_Sample_Period), (12 bytes).
    Removing key_app.o(i.Set_Sample_Period), (104 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(i.Oled_Task), (2 bytes).
    Removing rtc_app.o(.rev16_text), (4 bytes).
    Removing rtc_app.o(.revsh_text), (4 bytes).
    Removing rtc_app.o(i.RTC_Proc), (8 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(i.test_task), (2 bytes).
    Removing sdcard_app.o(.rev16_text), (4 bytes).
    Removing sdcard_app.o(.revsh_text), (4 bytes).
    Removing sdcard_app.o(i.Check_TF_Card), (36 bytes).
    Removing sdcard_app.o(i.Close_HideData_File), (24 bytes).
    Removing sdcard_app.o(i.Is_Log_Folder_Missing), (60 bytes).
    Removing sdcard_app.o(i.Update_Config_INI_Ratio_Only), (60 bytes).
    Removing sdcard_app.o(i.Write_Sample_Data), (392 bytes).
    Removing timer_app.o(.rev16_text), (4 bytes).
    Removing timer_app.o(.revsh_text), (4 bytes).
    Removing usart_app.o(.rev16_text), (4 bytes).
    Removing usart_app.o(.revsh_text), (4 bytes).
    Removing usart_app.o(i.Get_Device_ID), (12 bytes).
    Removing usart_app.o(i.Handle_Conf_Command), (464 bytes).
    Removing usart_app.o(i.Handle_Config_Read_Command), (260 bytes).
    Removing usart_app.o(i.Handle_Config_Save_Command), (204 bytes).
    Removing usart_app.o(i.Handle_Hide_Command), (32 bytes).
    Removing usart_app.o(i.Handle_Test_Command), (524 bytes).
    Removing usart_app.o(i.Handle_Unhide_Command), (36 bytes).
    Removing usart_app.o(i.Set_Device_ID), (12 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (272 bytes).
    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (96 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (34 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_config), (52 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_source_config), (48 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (124 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (46 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (58 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_data_read), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_software_trigger_enable), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_special_function_config), (90 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_mode_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_routine_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (50 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_sequence_channel_enable), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (56 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (52 bytes).
    Removing gd32f4xx_can.o(i.can_error_get), (12 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (32 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (272 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (30 bytes).
    Removing gd32f4xx_can.o(i.can_init), (290 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_get), (116 bytes).
    Removing gd32f4xx_can.o(i.can_message_receive), (228 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (336 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_receive_message_length_get), (26 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (164 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (80 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (10 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (124 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (48 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (168 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (36 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (16 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (48 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (12 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_deinit), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (38 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (22 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_source_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_mode_config), (40 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (20 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (20 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (36 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (52 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_get), (516 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_config), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (40 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (126 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_para_struct_init), (34 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (66 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_get), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (28 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (108 bytes).
    Removing gd32f4xx_enet.o(i.enet_default_init), (148 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_delay), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_chain_init), (200 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_enable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_receive), (248 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_transmit), (204 bytes).
    Removing gd32f4xx_enet.o(i.enet_init), (868 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (356 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_enable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_set), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_config), (216 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_write_read), (156 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (50 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (280 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (256 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (340 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (444 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (56 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt), (10 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_enable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_drop), (172 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_size_get), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_software_reset), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_transmit_checksum_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_txfifo_flush), (52 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (20 bytes).
    Removing gd32f4xx_enet.o(.bss), (15460 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_enet.o(.data), (20 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (72 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (172 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_init), (228 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (40 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (106 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (188 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (284 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_command_para_init), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (66 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (64 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (28 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (188 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (80 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_halfword_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (72 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_page_erase), (124 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_ready_wait), (32 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_sector_erase), (96 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_state_get), (76 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_word_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_double_bank_select), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (96 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (84 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (76 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (40 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (52 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (72 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (72 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_prescaler_value_config), (60 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_reload_value_config), (64 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_toggle), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_write), (10 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (206 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ack_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_clock_config), (228 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_deinit), (88 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_clear), (40 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_get), (30 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_master_addressing), (20 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_mode_addr_config), (28 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_start_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stop_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (316 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (36 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (20 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (28 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f4xx_misc.o(i.nvic_vector_table_set), (24 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_misc.o(i.systick_clksource_set), (40 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_deepsleepmode), (244 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (108 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ck48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout0_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (140 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_stab_wait), (348 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (132 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (44 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (72 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_get), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (128 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (68 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (116 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (204 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_clear), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (200 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (92 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (76 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (20 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (48 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (292 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_set), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_deinit), (172 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_format_error_clear), (6 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_read_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_write_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (28 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (172 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (24 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_config), (492 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_mode_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config), (38 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_shadow_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_output_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_struct_para_init), (22 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (188 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (152 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (48 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (228 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(i.trng_deinit), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_flag_get), (24 bytes).
    Removing gd32f4xx_trng.o(i.trng_get_true_random_data), (12 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (28 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_receive_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_clear), (52 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_disable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (104 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (34 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (28 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (16 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (12 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).
    Removing startup_gd32f450_470.o(HEAP), (2048 bytes).
    Removing ff.o(i.dir_read), (190 bytes).
    Removing ff.o(i.dir_remove), (96 bytes).
    Removing ff.o(i.f_chmod), (92 bytes).
    Removing ff.o(i.f_getfree), (276 bytes).
    Removing ff.o(i.f_read), (462 bytes).
    Removing ff.o(i.f_readdir), (96 bytes).
    Removing ff.o(i.f_rename), (300 bytes).
    Removing ff.o(i.f_stat), (68 bytes).
    Removing ff.o(i.f_truncate), (156 bytes).
    Removing ff.o(i.f_unlink), (188 bytes).
    Removing ff.o(i.f_utime), (96 bytes).
    Removing ff.o(i.get_fileinfo), (316 bytes).
    Removing ff.o(i.pick_lfn), (116 bytes).
    Removing diskio.o(.rev16_text), (4 bytes).
    Removing diskio.o(.revsh_text), (4 bytes).

933 unused section(s) (total 67612 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/string/strncpy.c        0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok_r.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s 0x00000000   Number         0  startup_gd32f450_470.o ABSOLUTE
    ..\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\Fatfs\diskio.c                        0x00000000   Number         0  diskio.o ABSOLUTE
    ..\Fatfs\ff.c                            0x00000000   Number         0  ff.o ABSOLUTE
    ..\HardWare\ADC\ADC.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\HardWare\GD30AD3344\gd30ad3344.c      0x00000000   Number         0  gd30ad3344.o ABSOLUTE
    ..\HardWare\KEY\Key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HardWare\LED\LED.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HardWare\OLED\OLED.c                  0x00000000   Number         0  oled.o ABSOLUTE
    ..\HardWare\RTC\RTC.c                    0x00000000   Number         0  rtc.o ABSOLUTE
    ..\HardWare\SDCARD\sdcard.c              0x00000000   Number         0  sdcard.o ABSOLUTE
    ..\HardWare\TIMER\TIMER.c                0x00000000   Number         0  timer.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\Protocol\SPI_FLASH.c                  0x00000000   Number         0  spi_flash.o ABSOLUTE
    ..\Protocol\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\User\gd32f4xx_it.c                    0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\User\systick.c                        0x00000000   Number         0  systick.o ABSOLUTE
    ..\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\\Fatfs\\diskio.c                      0x00000000   Number         0  diskio.o ABSOLUTE
    ..\\HardWare\\ADC\\ADC.c                 0x00000000   Number         0  adc.o ABSOLUTE
    ..\\HardWare\\GD30AD3344\\gd30ad3344.c   0x00000000   Number         0  gd30ad3344.o ABSOLUTE
    ..\\HardWare\\KEY\\Key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HardWare\\LED\\LED.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\HardWare\\OLED\\OLED.c               0x00000000   Number         0  oled.o ABSOLUTE
    ..\\HardWare\\RTC\\RTC.c                 0x00000000   Number         0  rtc.o ABSOLUTE
    ..\\HardWare\\SDCARD\\sdcard.c           0x00000000   Number         0  sdcard.o ABSOLUTE
    ..\\HardWare\\TIMER\\TIMER.c             0x00000000   Number         0  timer.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\\Protocol\\SPI_FLASH.c                0x00000000   Number         0  spi_flash.o ABSOLUTE
    ..\\Protocol\\usart.c                    0x00000000   Number         0  usart.o ABSOLUTE
    ..\\User\\gd32f4xx_it.c                  0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\\User\\main.c                         0x00000000   Number         0  main.o ABSOLUTE
    ..\\User\\systick.c                      0x00000000   Number         0  systick.o ABSOLUTE
    ..\\sysFunction\\ADC_APP.c               0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\\sysFunction\\Flash_APP.c             0x00000000   Number         0  flash_app.o ABSOLUTE
    ..\\sysFunction\\Function.c              0x00000000   Number         0  function.o ABSOLUTE
    ..\\sysFunction\\Key_APP.c               0x00000000   Number         0  key_app.o ABSOLUTE
    ..\\sysFunction\\Oled_APP.c              0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\\sysFunction\\RTC_APP.c               0x00000000   Number         0  rtc_app.o ABSOLUTE
    ..\\sysFunction\\Sdcard_APP.c            0x00000000   Number         0  sdcard_app.o ABSOLUTE
    ..\\sysFunction\\Timer_APP.c             0x00000000   Number         0  timer_app.o ABSOLUTE
    ..\\sysFunction\\Usart_APP.c             0x00000000   Number         0  usart_app.o ABSOLUTE
    ..\\sysFunction\\scheduler.c             0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\sysFunction\ADC_APP.c                 0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\sysFunction\Flash_APP.c               0x00000000   Number         0  flash_app.o ABSOLUTE
    ..\sysFunction\Function.c                0x00000000   Number         0  function.o ABSOLUTE
    ..\sysFunction\Key_APP.c                 0x00000000   Number         0  key_app.o ABSOLUTE
    ..\sysFunction\Oled_APP.c                0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\sysFunction\RTC_APP.c                 0x00000000   Number         0  rtc_app.o ABSOLUTE
    ..\sysFunction\Sdcard_APP.c              0x00000000   Number         0  sdcard_app.o ABSOLUTE
    ..\sysFunction\Timer_APP.c               0x00000000   Number         0  timer_app.o ABSOLUTE
    ..\sysFunction\Usart_APP.c               0x00000000   Number         0  usart_app.o ABSOLUTE
    ..\sysFunction\scheduler.c               0x00000000   Number         0  scheduler.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_gd32f450_470.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080001bc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080001bc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080001bc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001bc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001c0   Section       36  startup_gd32f450_470.o(.text)
    $v0                                      0x080001c0   Number         0  startup_gd32f450_470.o(.text)
    .text                                    0x080001e4   Section        0  memcpya.o(.text)
    .text                                    0x08000208   Section        0  memseta.o(.text)
    .text                                    0x0800022c   Section        0  strstr.o(.text)
    .text                                    0x08000250   Section        0  strncpy.o(.text)
    .text                                    0x08000268   Section        0  strlen.o(.text)
    .text                                    0x08000276   Section        0  strcmp.o(.text)
    .text                                    0x08000292   Section        0  strcpy.o(.text)
    .text                                    0x080002a4   Section        0  strncmp.o(.text)
    .text                                    0x080002c2   Section        0  atoi.o(.text)
    .text                                    0x080002dc   Section        0  f2d.o(.text)
    .text                                    0x08000302   Section        0  d2f.o(.text)
    .text                                    0x0800033a   Section        0  uidiv.o(.text)
    .text                                    0x08000366   Section        0  uldiv.o(.text)
    .text                                    0x080003c8   Section        0  strtod.o(.text)
    _local_sscanf                            0x080003c9   Thumb Code    54  strtod.o(.text)
    .text                                    0x08000464   Section        0  strtol.o(.text)
    .text                                    0x080004d4   Section        0  iusefp.o(.text)
    .text                                    0x080004d4   Section        0  fepilogue.o(.text)
    .text                                    0x08000542   Section        0  dadd.o(.text)
    .text                                    0x08000690   Section        0  dmul.o(.text)
    .text                                    0x08000774   Section        0  ddiv.o(.text)
    .text                                    0x08000852   Section        0  dfixul.o(.text)
    .text                                    0x08000884   Section       48  cdrcmple.o(.text)
    .text                                    0x080008b4   Section       36  init.o(.text)
    .text                                    0x080008d8   Section        0  llshl.o(.text)
    .text                                    0x080008f6   Section        0  llushr.o(.text)
    .text                                    0x08000916   Section        0  llsshr.o(.text)
    .text                                    0x0800093c   Section        0  ctype_o.o(.text)
    .text                                    0x08000944   Section        0  isspace_o.o(.text)
    .text                                    0x08000958   Section        0  scanf_fp.o(.text)
    _fp_value                                0x08000959   Thumb Code   296  scanf_fp.o(.text)
    .text                                    0x08000cb8   Section        0  _sgetc.o(.text)
    .text                                    0x08000cf8   Section        0  _strtoul.o(.text)
    .text                                    0x08000d96   Section        0  depilogue.o(.text)
    .text                                    0x08000e50   Section        0  _chval.o(.text)
    .text                                    0x08000e6c   Section        0  dfltul.o(.text)
    .text                                    0x08000e84   Section        0  __dczerorl2.o(.text)
    i.ADC_Init                               0x08000edc   Section        0  adc.o(i.ADC_Init)
    i.ADC_Proc                               0x08000f48   Section        0  adc_app.o(i.ADC_Proc)
    i.BusFault_Handler                       0x08001064   Section        0  gd32f4xx_it.o(i.BusFault_Handler)
    i.Cache_Log_To_Flash                     0x08001068   Section        0  flash_app.o(i.Cache_Log_To_Flash)
    i.Calculate_Config_Checksum              0x08001154   Section        0  flash_app.o(i.Calculate_Config_Checksum)
    i.Calculate_Multi_Channel_Config_Checksum 0x0800116e   Section        0  flash_app.o(i.Calculate_Multi_Channel_Config_Checksum)
    i.Check_Cached_Log_In_Flash              0x08001188   Section        0  flash_app.o(i.Check_Cached_Log_In_Flash)
    i.Close_OverLimit_File                   0x080011ac   Section        0  sdcard_app.o(i.Close_OverLimit_File)
    i.Close_Sample_File                      0x080011d0   Section        0  sdcard_app.o(i.Close_Sample_File)
    i.Configure_RTC_With_DateTime            0x0800120c   Section        0  rtc_app.o(i.Configure_RTC_With_DateTime)
    i.Convert_RTC_To_Unix_Timestamp          0x080012f0   Section        0  usart_app.o(i.Convert_RTC_To_Unix_Timestamp)
    i.Convert_Uint32_To_Hex_String           0x080013e0   Section        0  usart_app.o(i.Convert_Uint32_To_Hex_String)
    i.Create_Default_Config_INI              0x0800141c   Section        0  sdcard_app.o(i.Create_Default_Config_INI)
    i.Create_HideData_File                   0x080014a0   Section        0  sdcard_app.o(i.Create_HideData_File)
    i.Create_Log0_File                       0x0800154c   Section        0  sdcard_app.o(i.Create_Log0_File)
    i.Create_Log_File                        0x08001588   Section        0  sdcard_app.o(i.Create_Log_File)
    i.Create_Sample_File                     0x08001618   Section        0  sdcard_app.o(i.Create_Sample_File)
    i.DebugMon_Handler                       0x08001654   Section        0  gd32f4xx_it.o(i.DebugMon_Handler)
    i.Encode_Voltage_To_Hex                  0x08001658   Section        0  usart_app.o(i.Encode_Voltage_To_Hex)
    i.Flash_Init                             0x080016b0   Section        0  flash_app.o(i.Flash_Init)
    i.GD30AD3344_AD_Read                     0x0800182c   Section        0  gd30ad3344.o(i.GD30AD3344_AD_Read)
    i.GD30AD3344_PGA_SET                     0x080018f0   Section        0  gd30ad3344.o(i.GD30AD3344_PGA_SET)
    i.Generate_Encrypted_Output_With_Values  0x08001974   Section        0  usart_app.o(i.Generate_Encrypted_Output_With_Values)
    i.Generate_Encrypted_Output_With_Values_OverLimit 0x08001a28   Section        0  usart_app.o(i.Generate_Encrypted_Output_With_Values_OverLimit)
    i.Generate_Filename                      0x08001a78   Section        0  sdcard_app.o(i.Generate_Filename)
    Generate_Filename                        0x08001a79   Thumb Code    62  sdcard_app.o(i.Generate_Filename)
    i.Generate_HideData_Filename             0x08001aec   Section        0  sdcard_app.o(i.Generate_HideData_Filename)
    i.Generate_Log_Filename                  0x08001b60   Section        0  sdcard_app.o(i.Generate_Log_Filename)
    Generate_Log_Filename                    0x08001b61   Thumb Code    30  sdcard_app.o(i.Generate_Log_Filename)
    i.Generate_OverLimit_Filename            0x08001b98   Section        0  sdcard_app.o(i.Generate_OverLimit_Filename)
    i.Get_Limit                              0x08001c10   Section        0  adc_app.o(i.Get_Limit)
    i.Get_Next_Log_ID_From_SD                0x08001c1c   Section        0  sdcard_app.o(i.Get_Next_Log_ID_From_SD)
    i.Get_Ratio                              0x08001c94   Section        0  adc_app.o(i.Get_Ratio)
    i.Get_Sample_Interval                    0x08001ca0   Section        0  adc_app.o(i.Get_Sample_Interval)
    i.Handle_Command_Get_Limit               0x08001cac   Section        0  usart_app.o(i.Handle_Command_Get_Limit)
    i.Handle_Command_Set_Limit               0x08001d34   Section        0  usart_app.o(i.Handle_Command_Set_Limit)
    i.Handle_Command_Set_RTC                 0x08001f44   Section        0  usart_app.o(i.Handle_Command_Set_RTC)
    i.Handle_Command_Set_Ratio               0x080020e8   Section        0  usart_app.o(i.Handle_Command_Set_Ratio)
    i.Handle_Get_Data_Command                0x08002308   Section        0  usart_app.o(i.Handle_Get_Data_Command)
    i.Handle_Get_Device_ID_Command           0x08002490   Section        0  usart_app.o(i.Handle_Get_Device_ID_Command)
    i.Handle_Get_Ratio_Command               0x080024c0   Section        0  usart_app.o(i.Handle_Get_Ratio_Command)
    i.Handle_Key1_Press                      0x080025bc   Section        0  key_app.o(i.Handle_Key1_Press)
    i.Handle_Key2_Press                      0x080025fc   Section        0  key_app.o(i.Handle_Key2_Press)
    i.Handle_Key3_Press                      0x0800263c   Section        0  key_app.o(i.Handle_Key3_Press)
    i.Handle_Key4_Press                      0x0800267c   Section        0  key_app.o(i.Handle_Key4_Press)
    i.Handle_Key5_Press                      0x080026c0   Section        0  key_app.o(i.Handle_Key5_Press)
    i.Handle_Key6_Press                      0x08002704   Section        0  key_app.o(i.Handle_Key6_Press)
    i.Handle_Multi_Config_Read_Command       0x08002748   Section        0  usart_app.o(i.Handle_Multi_Config_Read_Command)
    i.Handle_Multi_Config_Save_Command       0x08002a7c   Section        0  usart_app.o(i.Handle_Multi_Config_Save_Command)
    i.Handle_RTC_Now_Command                 0x08002c9c   Section        0  usart_app.o(i.Handle_RTC_Now_Command)
    i.Handle_Start_Command                   0x08002d08   Section        0  usart_app.o(i.Handle_Start_Command)
    i.Handle_Stop_Command                    0x08002d10   Section        0  usart_app.o(i.Handle_Stop_Command)
    i.HardFault_Handler                      0x08002d48   Section        0  gd32f4xx_it.o(i.HardFault_Handler)
    i.I2C_Start                              0x08002d4c   Section        0  oled.o(i.I2C_Start)
    i.I2C_Stop                               0x08002da4   Section        0  oled.o(i.I2C_Stop)
    i.I2C_WaitAck                            0x08002dec   Section        0  oled.o(i.I2C_WaitAck)
    i.IIC_delay                              0x08002e64   Section        0  oled.o(i.IIC_delay)
    i.Init_Data_Recording                    0x08002e74   Section        0  sdcard_app.o(i.Init_Data_Recording)
    i.Key_Init                               0x08002fa0   Section        0  key.o(i.Key_Init)
    i.Key_Proc                               0x08002fc0   Section        0  key_app.o(i.Key_Proc)
    i.Key_Read                               0x08003060   Section        0  key.o(i.Key_Read)
    i.Led_Init                               0x080030cc   Section        0  led.o(i.Led_Init)
    i.Log_Init                               0x08003104   Section        0  flash_app.o(i.Log_Init)
    i.MemManage_Handler                      0x08003114   Section        0  gd32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08003118   Section        0  gd32f4xx_it.o(i.NMI_Handler)
    i.NVIC_SetPriority                       0x0800311c   Section        0  systick.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x0800311d   Thumb Code    32  systick.o(i.NVIC_SetPriority)
    i.OLED_App_Init                          0x08003144   Section        0  oled_app.o(i.OLED_App_Init)
    i.OLED_Clear                             0x0800318c   Section        0  oled.o(i.OLED_Clear)
    i.OLED_ClearPoint                        0x080031bc   Section        0  oled.o(i.OLED_ClearPoint)
    i.OLED_DrawPoint                         0x0800321c   Section        0  oled.o(i.OLED_DrawPoint)
    i.OLED_Init                              0x08003258   Section        0  oled.o(i.OLED_Init)
    i.OLED_Refresh                           0x080033a0   Section        0  oled.o(i.OLED_Refresh)
    i.OLED_ShowChar                          0x080033ec   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowString                        0x080034e8   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WR_Byte                           0x0800352a   Section        0  oled.o(i.OLED_WR_Byte)
    i.PendSV_Handler                         0x08003562   Section        0  gd32f4xx_it.o(i.PendSV_Handler)
    i.RS485_Task                             0x08003564   Section        0  usart_app.o(i.RS485_Task)
    i.RTC_Init                               0x080038c4   Section        0  rtc.o(i.RTC_Init)
    i.Read_Config_From_Flash                 0x08003940   Section        0  flash_app.o(i.Read_Config_From_Flash)
    i.Read_Multi_Channel_Config_From_Flash   0x08003986   Section        0  flash_app.o(i.Read_Multi_Channel_Config_From_Flash)
    i.SDIO_IRQHandler                        0x080039fe   Section        0  gd32f4xx_it.o(i.SDIO_IRQHandler)
    i.SVC_Handler                            0x08003a06   Section        0  gd32f4xx_it.o(i.SVC_Handler)
    i.Save_Config_To_Flash                   0x08003a08   Section        0  flash_app.o(i.Save_Config_To_Flash)
    i.Save_Multi_Channel_Config_To_Flash     0x08003aac   Section        0  flash_app.o(i.Save_Multi_Channel_Config_To_Flash)
    i.Send_Byte                              0x08003bc8   Section        0  oled.o(i.Send_Byte)
    i.Set_ADC_Sample_Interval                0x08003c24   Section        0  timer_app.o(i.Set_ADC_Sample_Interval)
    i.Set_ADC_Sampling_State                 0x08003c38   Section        0  timer_app.o(i.Set_ADC_Sampling_State)
    i.Set_LED1_Blink_Mode                    0x08003c4c   Section        0  timer_app.o(i.Set_LED1_Blink_Mode)
    i.Set_LED2_State                         0x08003c7c   Section        0  timer_app.o(i.Set_LED2_State)
    i.Set_Limit                              0x08003ca0   Section        0  adc_app.o(i.Set_Limit)
    i.Set_Over_Limit_State                   0x08003cc8   Section        0  adc_app.o(i.Set_Over_Limit_State)
    i.Set_Ratio                              0x08003ce8   Section        0  adc_app.o(i.Set_Ratio)
    i.Set_Sample_Interval                    0x08003d10   Section        0  adc_app.o(i.Set_Sample_Interval)
    i.Set_System_State                       0x08003d24   Section        0  adc_app.o(i.Set_System_State)
    i.Start_Sampling                         0x08003d38   Section        0  adc_app.o(i.Start_Sampling)
    i.Stop_Sampling                          0x08003d68   Section        0  adc_app.o(i.Stop_Sampling)
    i.SysTick_Handler                        0x08003d90   Section        0  gd32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08003da8   Section        0  system_gd32f4xx.o(i.SystemInit)
    i.System_Init                            0x08003e7c   Section        0  function.o(i.System_Init)
    i.TIMER1_IRQHandler                      0x08003e84   Section        0  timer.o(i.TIMER1_IRQHandler)
    i.Timer_ADC_Handler                      0x08003ea4   Section        0  timer_app.o(i.Timer_ADC_Handler)
    i.Timer_Init                             0x080042fc   Section        0  timer.o(i.Timer_Init)
    i.Timer_LED_Handler                      0x08004354   Section        0  timer_app.o(i.Timer_LED_Handler)
    i.USART0_IRQHandler                      0x080043c0   Section        0  usart.o(i.USART0_IRQHandler)
    i.USART1_IRQHandler                      0x08004470   Section        0  usart.o(i.USART1_IRQHandler)
    i.Update_Config_INI                      0x08004520   Section        0  sdcard_app.o(i.Update_Config_INI)
    i.Update_Current_Display                 0x08004614   Section        0  key_app.o(i.Update_Current_Display)
    i.Update_LED_Status                      0x08004878   Section        0  adc_app.o(i.Update_LED_Status)
    i.UsageFault_Handler                     0x080048c4   Section        0  gd32f4xx_it.o(i.UsageFault_Handler)
    i.UsrFunction                            0x080048c8   Section        0  function.o(i.UsrFunction)
    i.Verify_Config_Data                     0x08004920   Section        0  flash_app.o(i.Verify_Config_Data)
    i.Verify_Multi_Channel_Config_Data       0x080049b0   Section        0  flash_app.o(i.Verify_Multi_Channel_Config_Data)
    i.Write_Cached_Log_To_Log0               0x08004abc   Section        0  flash_app.o(i.Write_Cached_Log_To_Log0)
    i.Write_HideData                         0x08004b84   Section        0  sdcard_app.o(i.Write_HideData)
    i.Write_Log_Data                         0x08004c3c   Section        0  sdcard_app.o(i.Write_Log_Data)
    i.Write_OverLimit_Data                   0x08004d48   Section        0  sdcard_app.o(i.Write_OverLimit_Data)
    i.Write_Sample_Data_MultiChannel         0x08004ed0   Section        0  sdcard_app.o(i.Write_Sample_Data_MultiChannel)
    i.__0printf                              0x080050e0   Section        0  printfa.o(i.__0printf)
    i.__0snprintf                            0x08005100   Section        0  printfa.o(i.__0snprintf)
    i.__0vsnprintf                           0x08005130   Section        0  printfa.o(i.__0vsnprintf)
    i.__aeabi_errno_addr                     0x0800515c   Section        0  errno.o(i.__aeabi_errno_addr)
    i.__hardfp_atof                          0x08005168   Section        0  atof.o(i.__hardfp_atof)
    i.__read_errno                           0x080051a0   Section        0  errno.o(i.__read_errno)
    i.__scatterload_copy                     0x080051ac   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080051ba   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080051bc   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x080051cc   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x080051d8   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080051d9   Thumb Code   366  printfa.o(i._fp_digits)
    i._is_digit                              0x0800535c   Section        0  scanf_fp.o(i._is_digit)
    i._printf_core                           0x0800536c   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x0800536d   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08005a48   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08005a49   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08005a6c   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08005a6d   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08005a9a   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08005a9b   Thumb Code    22  printfa.o(i._snputc)
    i.adc_calibration_enable                 0x08005ab0   Section        0  gd32f4xx_adc.o(i.adc_calibration_enable)
    i.adc_channel_length_config              0x08005ada   Section        0  gd32f4xx_adc.o(i.adc_channel_length_config)
    i.adc_clock_config                       0x08005b2c   Section        0  gd32f4xx_adc.o(i.adc_clock_config)
    i.adc_data_alignment_config              0x08005b50   Section        0  gd32f4xx_adc.o(i.adc_data_alignment_config)
    i.adc_deinit                             0x08005b66   Section        0  gd32f4xx_adc.o(i.adc_deinit)
    i.adc_enable                             0x08005b7a   Section        0  gd32f4xx_adc.o(i.adc_enable)
    i.adc_resolution_config                  0x08005b8c   Section        0  gd32f4xx_adc.o(i.adc_resolution_config)
    i.adc_routine_channel_config             0x08005b9c   Section        0  gd32f4xx_adc.o(i.adc_routine_channel_config)
    i.bcd_to_decimal                         0x08005c48   Section        0  usart_app.o(i.bcd_to_decimal)
    bcd_to_decimal                           0x08005c49   Thumb Code    20  usart_app.o(i.bcd_to_decimal)
    i.check_fs                               0x08005c5c   Section        0  ff.o(i.check_fs)
    check_fs                                 0x08005c5d   Thumb Code   138  ff.o(i.check_fs)
    i.chk_chr                                0x08005cec   Section        0  ff.o(i.chk_chr)
    chk_chr                                  0x08005ced   Thumb Code    20  ff.o(i.chk_chr)
    i.chk_mounted                            0x08005d00   Section        0  ff.o(i.chk_mounted)
    chk_mounted                              0x08005d01   Thumb Code   898  ff.o(i.chk_mounted)
    i.clust2sect                             0x08006094   Section        0  ff.o(i.clust2sect)
    i.cmdsent_error_check                    0x080060b0   Section        0  sdcard.o(i.cmdsent_error_check)
    cmdsent_error_check                      0x080060b1   Thumb Code    40  sdcard.o(i.cmdsent_error_check)
    i.cmp_lfn                                0x080060e0   Section        0  ff.o(i.cmp_lfn)
    cmp_lfn                                  0x080060e1   Thumb Code   138  ff.o(i.cmp_lfn)
    i.convert_to_bcd                         0x08006170   Section        0  rtc_app.o(i.convert_to_bcd)
    convert_to_bcd                           0x08006171   Thumb Code    26  rtc_app.o(i.convert_to_bcd)
    i.create_chain                           0x0800618a   Section        0  ff.o(i.create_chain)
    create_chain                             0x0800618b   Thumb Code   202  ff.o(i.create_chain)
    i.create_name                            0x08006254   Section        0  ff.o(i.create_name)
    create_name                              0x08006255   Thumb Code   616  ff.o(i.create_name)
    i.delay_1ms                              0x080064d0   Section        0  systick.o(i.delay_1ms)
    i.delay_decrement                        0x080064e4   Section        0  systick.o(i.delay_decrement)
    i.dir_find                               0x080064fc   Section        0  ff.o(i.dir_find)
    dir_find                                 0x080064fd   Thumb Code   222  ff.o(i.dir_find)
    i.dir_next                               0x080065da   Section        0  ff.o(i.dir_next)
    dir_next                                 0x080065db   Thumb Code   280  ff.o(i.dir_next)
    i.dir_register                           0x080066f2   Section        0  ff.o(i.dir_register)
    dir_register                             0x080066f3   Thumb Code   396  ff.o(i.dir_register)
    i.dir_sdi                                0x0800687e   Section        0  ff.o(i.dir_sdi)
    dir_sdi                                  0x0800687f   Thumb Code   156  ff.o(i.dir_sdi)
    i.disk_initialize                        0x0800691a   Section        0  diskio.o(i.disk_initialize)
    i.disk_ioctl                             0x080069a0   Section        0  diskio.o(i.disk_ioctl)
    i.disk_read                              0x080069a6   Section        0  diskio.o(i.disk_read)
    i.disk_status                            0x080069f6   Section        0  diskio.o(i.disk_status)
    i.disk_write                             0x08006a02   Section        0  diskio.o(i.disk_write)
    i.dma_channel_disable                    0x08006a52   Section        0  gd32f4xx_dma.o(i.dma_channel_disable)
    i.dma_channel_enable                     0x08006a72   Section        0  gd32f4xx_dma.o(i.dma_channel_enable)
    i.dma_channel_subperipheral_select       0x08006a92   Section        0  gd32f4xx_dma.o(i.dma_channel_subperipheral_select)
    i.dma_deinit                             0x08006ab8   Section        0  gd32f4xx_dma.o(i.dma_deinit)
    i.dma_flag_clear                         0x08006b5e   Section        0  gd32f4xx_dma.o(i.dma_flag_clear)
    i.dma_flag_get                           0x08006b9c   Section        0  gd32f4xx_dma.o(i.dma_flag_get)
    i.dma_flow_controller_config             0x08006be8   Section        0  gd32f4xx_dma.o(i.dma_flow_controller_config)
    i.dma_multi_data_mode_init               0x08006c28   Section        0  gd32f4xx_dma.o(i.dma_multi_data_mode_init)
    i.dma_receive_config                     0x08006d8c   Section        0  sdcard.o(i.dma_receive_config)
    dma_receive_config                       0x08006d8d   Thumb Code   170  sdcard.o(i.dma_receive_config)
    i.dma_single_data_mode_init              0x08006e40   Section        0  gd32f4xx_dma.o(i.dma_single_data_mode_init)
    i.dma_transfer_config                    0x08006f98   Section        0  sdcard.o(i.dma_transfer_config)
    dma_transfer_config                      0x08006f99   Thumb Code   172  sdcard.o(i.dma_transfer_config)
    i.f_close                                0x0800704c   Section        0  ff.o(i.f_close)
    i.f_lseek                                0x08007062   Section        0  ff.o(i.f_lseek)
    i.f_mkdir                                0x08007214   Section        0  ff.o(i.f_mkdir)
    i.f_mount                                0x0800739c   Section        0  ff.o(i.f_mount)
    i.f_open                                 0x080073c8   Section        0  ff.o(i.f_open)
    i.f_opendir                              0x0800753c   Section        0  ff.o(i.f_opendir)
    i.f_sync                                 0x080075b4   Section        0  ff.o(i.f_sync)
    i.f_write                                0x0800766c   Section        0  ff.o(i.f_write)
    i.ff_convert                             0x0800787c   Section        0  sdcard_app.o(i.ff_convert)
    i.ff_wtoupper                            0x080078c8   Section        0  sdcard_app.o(i.ff_wtoupper)
    i.fit_lfn                                0x080078f4   Section        0  ff.o(i.fit_lfn)
    fit_lfn                                  0x080078f5   Thumb Code   122  ff.o(i.fit_lfn)
    i.follow_path                            0x08007974   Section        0  ff.o(i.follow_path)
    follow_path                              0x08007975   Thumb Code   158  ff.o(i.follow_path)
    i.fputc                                  0x08007a14   Section        0  usart.o(i.fputc)
    i.gd30ad3344_init                        0x08007a38   Section        0  gd30ad3344.o(i.gd30ad3344_init)
    i.gen_numname                            0x08007b9c   Section        0  ff.o(i.gen_numname)
    i.get_days_in_month                      0x08007c68   Section        0  usart_app.o(i.get_days_in_month)
    get_days_in_month                        0x08007c69   Thumb Code    30  usart_app.o(i.get_days_in_month)
    i.get_fat                                0x08007c8c   Section        0  ff.o(i.get_fat)
    i.get_fattime                            0x08007d70   Section        0  diskio.o(i.get_fattime)
    i.gpio_af_set                            0x08007db2   Section        0  gd32f4xx_gpio.o(i.gpio_af_set)
    i.gpio_bit_reset                         0x08007e10   Section        0  gd32f4xx_gpio.o(i.gpio_bit_reset)
    i.gpio_bit_set                           0x08007e14   Section        0  gd32f4xx_gpio.o(i.gpio_bit_set)
    i.gpio_config                            0x08007e18   Section        0  sdcard.o(i.gpio_config)
    gpio_config                              0x08007e19   Thumb Code   106  sdcard.o(i.gpio_config)
    i.gpio_input_bit_get                     0x08007e8c   Section        0  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    i.gpio_mode_set                          0x08007e9c   Section        0  gd32f4xx_gpio.o(i.gpio_mode_set)
    i.gpio_output_options_set                0x08007eea   Section        0  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i.is_leap_year                           0x08007f2c   Section        0  usart_app.o(i.is_leap_year)
    is_leap_year                             0x08007f2d   Thumb Code    52  usart_app.o(i.is_leap_year)
    i.main                                   0x08007f60   Section        0  main.o(i.main)
    i.mem_cmp                                0x08007f6e   Section        0  ff.o(i.mem_cmp)
    mem_cmp                                  0x08007f6f   Thumb Code    38  ff.o(i.mem_cmp)
    i.mem_cpy                                0x08007f94   Section        0  ff.o(i.mem_cpy)
    mem_cpy                                  0x08007f95   Thumb Code    26  ff.o(i.mem_cpy)
    i.mem_set                                0x08007fae   Section        0  ff.o(i.mem_set)
    mem_set                                  0x08007faf   Thumb Code    20  ff.o(i.mem_set)
    i.move_window                            0x08007fc2   Section        0  ff.o(i.move_window)
    move_window                              0x08007fc3   Thumb Code   114  ff.o(i.move_window)
    i.nvic_config                            0x08008034   Section        0  sdcard_app.o(i.nvic_config)
    i.nvic_irq_enable                        0x0800804c   Section        0  gd32f4xx_misc.o(i.nvic_irq_enable)
    i.nvic_priority_group_set                0x08008110   Section        0  gd32f4xx_misc.o(i.nvic_priority_group_set)
    i.pmu_backup_write_enable                0x08008124   Section        0  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    i.put_fat                                0x08008138   Section        0  ff.o(i.put_fat)
    i.r1_error_check                         0x08008270   Section        0  sdcard.o(i.r1_error_check)
    r1_error_check                           0x08008271   Thumb Code   120  sdcard.o(i.r1_error_check)
    i.r1_error_type_check                    0x080082f4   Section        0  sdcard.o(i.r1_error_type_check)
    r1_error_type_check                      0x080082f5   Thumb Code   174  sdcard.o(i.r1_error_type_check)
    i.r2_error_check                         0x080083a4   Section        0  sdcard.o(i.r2_error_check)
    r2_error_check                           0x080083a5   Thumb Code    70  sdcard.o(i.r2_error_check)
    i.r3_error_check                         0x080083f4   Section        0  sdcard.o(i.r3_error_check)
    r3_error_check                           0x080083f5   Thumb Code    52  sdcard.o(i.r3_error_check)
    i.r6_error_check                         0x08008430   Section        0  sdcard.o(i.r6_error_check)
    r6_error_check                           0x08008431   Thumb Code   158  sdcard.o(i.r6_error_check)
    i.r7_error_check                         0x080084d8   Section        0  sdcard.o(i.r7_error_check)
    r7_error_check                           0x080084d9   Thumb Code    74  sdcard.o(i.r7_error_check)
    i.rcu_all_reset_flag_clear               0x08008528   Section        0  gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear)
    i.rcu_clock_freq_get                     0x0800853c   Section        0  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    i.rcu_config                             0x08008660   Section        0  sdcard.o(i.rcu_config)
    rcu_config                               0x08008661   Thumb Code    36  sdcard.o(i.rcu_config)
    i.rcu_flag_get                           0x08008684   Section        0  gd32f4xx_rcu.o(i.rcu_flag_get)
    i.rcu_osci_on                            0x080086a8   Section        0  gd32f4xx_rcu.o(i.rcu_osci_on)
    i.rcu_periph_clock_enable                0x080086cc   Section        0  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    i.rcu_periph_reset_disable               0x080086f0   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    i.rcu_periph_reset_enable                0x08008714   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    i.rcu_rtc_clock_config                   0x08008738   Section        0  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    i.remove_chain                           0x08008750   Section        0  ff.o(i.remove_chain)
    remove_chain                             0x08008751   Thumb Code   104  ff.o(i.remove_chain)
    i.rs485_printf                           0x080087b8   Section        0  usart.o(i.rs485_printf)
    i.rs485_usart1_config                    0x0800883c   Section        0  usart.o(i.rs485_usart1_config)
    i.rtc_current_time_get                   0x0800893c   Section        0  gd32f4xx_rtc.o(i.rtc_current_time_get)
    i.rtc_init                               0x080089a0   Section        0  gd32f4xx_rtc.o(i.rtc_init)
    i.rtc_init_mode_enter                    0x08008a7c   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    i.rtc_init_mode_exit                     0x08008ac4   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    i.rtc_pre_config                         0x08008ad8   Section        0  rtc.o(i.rtc_pre_config)
    i.rtc_register_sync_wait                 0x08008b34   Section        0  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    i.rtc_setup_default                      0x08008b94   Section        0  rtc.o(i.rtc_setup_default)
    i.scheduler_init                         0x08008bf0   Section        0  scheduler.o(i.scheduler_init)
    i.scheduler_run                          0x08008bfc   Section        0  scheduler.o(i.scheduler_run)
    i.sd_block_read                          0x08008c54   Section        0  sdcard.o(i.sd_block_read)
    i.sd_block_write                         0x08008e94   Section        0  sdcard.o(i.sd_block_write)
    i.sd_bus_mode_config                     0x080091cc   Section        0  sdcard.o(i.sd_bus_mode_config)
    i.sd_bus_width_config                    0x08009260   Section        0  sdcard.o(i.sd_bus_width_config)
    sd_bus_width_config                      0x08009261   Thumb Code   242  sdcard.o(i.sd_bus_width_config)
    i.sd_card_information_get                0x0800935c   Section        0  sdcard.o(i.sd_card_information_get)
    i.sd_card_init                           0x0800961c   Section        0  sdcard.o(i.sd_card_init)
    i.sd_card_select_deselect                0x08009738   Section        0  sdcard.o(i.sd_card_select_deselect)
    i.sd_card_state_get                      0x08009760   Section        0  sdcard.o(i.sd_card_state_get)
    sd_card_state_get                        0x08009761   Thumb Code   166  sdcard.o(i.sd_card_state_get)
    i.sd_cardstatus_get                      0x08009818   Section        0  sdcard.o(i.sd_cardstatus_get)
    i.sd_datablocksize_get                   0x08009860   Section        0  sdcard.o(i.sd_datablocksize_get)
    sd_datablocksize_get                     0x08009861   Thumb Code    24  sdcard.o(i.sd_datablocksize_get)
    i.sd_init                                0x08009878   Section        0  sdcard.o(i.sd_init)
    i.sd_interrupts_process                  0x080098c0   Section        0  sdcard.o(i.sd_interrupts_process)
    i.sd_multiblocks_read                    0x080099f0   Section        0  sdcard.o(i.sd_multiblocks_read)
    i.sd_multiblocks_write                   0x08009cb4   Section        0  sdcard.o(i.sd_multiblocks_write)
    i.sd_power_on                            0x0800a064   Section        0  sdcard.o(i.sd_power_on)
    i.sd_scr_get                             0x0800a190   Section        0  sdcard.o(i.sd_scr_get)
    sd_scr_get                               0x0800a191   Thumb Code   366  sdcard.o(i.sd_scr_get)
    i.sd_transfer_mode_config                0x0800a308   Section        0  sdcard.o(i.sd_transfer_mode_config)
    i.sd_transfer_stop                       0x0800a320   Section        0  sdcard.o(i.sd_transfer_stop)
    i.sdio_bus_mode_set                      0x0800a344   Section        0  gd32f4xx_sdio.o(i.sdio_bus_mode_set)
    i.sdio_clock_config                      0x0800a360   Section        0  gd32f4xx_sdio.o(i.sdio_clock_config)
    i.sdio_clock_enable                      0x0800a394   Section        0  gd32f4xx_sdio.o(i.sdio_clock_enable)
    i.sdio_command_index_get                 0x0800a3a8   Section        0  gd32f4xx_sdio.o(i.sdio_command_index_get)
    i.sdio_command_response_config           0x0800a3b4   Section        0  gd32f4xx_sdio.o(i.sdio_command_response_config)
    i.sdio_csm_enable                        0x0800a3ec   Section        0  gd32f4xx_sdio.o(i.sdio_csm_enable)
    i.sdio_data_config                       0x0800a400   Section        0  gd32f4xx_sdio.o(i.sdio_data_config)
    i.sdio_data_read                         0x0800a43c   Section        0  gd32f4xx_sdio.o(i.sdio_data_read)
    i.sdio_data_transfer_config              0x0800a448   Section        0  gd32f4xx_sdio.o(i.sdio_data_transfer_config)
    i.sdio_data_write                        0x0800a464   Section        0  gd32f4xx_sdio.o(i.sdio_data_write)
    i.sdio_deinit                            0x0800a470   Section        0  gd32f4xx_sdio.o(i.sdio_deinit)
    i.sdio_dma_disable                       0x0800a484   Section        0  gd32f4xx_sdio.o(i.sdio_dma_disable)
    i.sdio_dma_enable                        0x0800a498   Section        0  gd32f4xx_sdio.o(i.sdio_dma_enable)
    i.sdio_dsm_disable                       0x0800a4ac   Section        0  gd32f4xx_sdio.o(i.sdio_dsm_disable)
    i.sdio_dsm_enable                        0x0800a4c0   Section        0  gd32f4xx_sdio.o(i.sdio_dsm_enable)
    i.sdio_flag_clear                        0x0800a4d4   Section        0  gd32f4xx_sdio.o(i.sdio_flag_clear)
    i.sdio_flag_get                          0x0800a4e0   Section        0  gd32f4xx_sdio.o(i.sdio_flag_get)
    i.sdio_hardware_clock_disable            0x0800a4f4   Section        0  gd32f4xx_sdio.o(i.sdio_hardware_clock_disable)
    i.sdio_interrupt_disable                 0x0800a508   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_disable)
    i.sdio_interrupt_enable                  0x0800a518   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_enable)
    i.sdio_interrupt_flag_clear              0x0800a528   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear)
    i.sdio_interrupt_flag_get                0x0800a534   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_flag_get)
    i.sdio_power_state_get                   0x0800a548   Section        0  gd32f4xx_sdio.o(i.sdio_power_state_get)
    i.sdio_power_state_set                   0x0800a554   Section        0  gd32f4xx_sdio.o(i.sdio_power_state_set)
    i.sdio_response_get                      0x0800a560   Section        0  gd32f4xx_sdio.o(i.sdio_response_get)
    i.sdio_wait_type_set                     0x0800a59c   Section        0  gd32f4xx_sdio.o(i.sdio_wait_type_set)
    i.spi_dma_disable                        0x0800a5b8   Section        0  gd32f4xx_spi.o(i.spi_dma_disable)
    i.spi_dma_enable                         0x0800a5ce   Section        0  gd32f4xx_spi.o(i.spi_dma_enable)
    i.spi_enable                             0x0800a5e4   Section        0  gd32f4xx_spi.o(i.spi_enable)
    i.spi_flash_buffer_read                  0x0800a5f0   Section        0  spi_flash.o(i.spi_flash_buffer_read)
    i.spi_flash_buffer_write                 0x0800a644   Section        0  spi_flash.o(i.spi_flash_buffer_write)
    i.spi_flash_init                         0x0800a708   Section        0  spi_flash.o(i.spi_flash_init)
    i.spi_flash_page_write                   0x0800a7a4   Section        0  spi_flash.o(i.spi_flash_page_write)
    i.spi_flash_read_id                      0x0800a800   Section        0  spi_flash.o(i.spi_flash_read_id)
    i.spi_flash_sector_erase                 0x0800a854   Section        0  spi_flash.o(i.spi_flash_sector_erase)
    i.spi_flash_send_byte                    0x0800a898   Section        0  spi_flash.o(i.spi_flash_send_byte)
    i.spi_flash_wait_for_write_end           0x0800a8d0   Section        0  spi_flash.o(i.spi_flash_wait_for_write_end)
    i.spi_flash_write_enable                 0x0800a908   Section        0  spi_flash.o(i.spi_flash_write_enable)
    i.spi_gd30ad3344_send_halfword_dma       0x0800a92c   Section        0  gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma)
    i.spi_i2s_data_receive                   0x0800aa4c   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    i.spi_i2s_data_transmit                  0x0800aa54   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    i.spi_i2s_flag_get                       0x0800aa58   Section        0  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    i.spi_init                               0x0800aa68   Section        0  gd32f4xx_spi.o(i.spi_init)
    i.sum_sfn                                0x0800aa9a   Section        0  ff.o(i.sum_sfn)
    sum_sfn                                  0x0800aa9b   Thumb Code    32  ff.o(i.sum_sfn)
    i.sync                                   0x0800aaba   Section        0  ff.o(i.sync)
    sync                                     0x0800aabb   Thumb Code   202  ff.o(i.sync)
    i.system_clock_240m_25m_hxtal            0x0800ab84   Section        0  system_gd32f4xx.o(i.system_clock_240m_25m_hxtal)
    system_clock_240m_25m_hxtal              0x0800ab85   Thumb Code   250  system_gd32f4xx.o(i.system_clock_240m_25m_hxtal)
    i.system_clock_config                    0x0800ac8c   Section        0  system_gd32f4xx.o(i.system_clock_config)
    system_clock_config                      0x0800ac8d   Thumb Code     8  system_gd32f4xx.o(i.system_clock_config)
    i.systick_config                         0x0800ac94   Section        0  systick.o(i.systick_config)
    i.timer_deinit                           0x0800ace4   Section        0  gd32f4xx_timer.o(i.timer_deinit)
    i.timer_enable                           0x0800ae68   Section        0  gd32f4xx_timer.o(i.timer_enable)
    i.timer_init                             0x0800ae74   Section        0  gd32f4xx_timer.o(i.timer_init)
    i.timer_interrupt_enable                 0x0800af0c   Section        0  gd32f4xx_timer.o(i.timer_interrupt_enable)
    i.timer_interrupt_flag_clear             0x0800af14   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_clear)
    i.timer_interrupt_flag_get               0x0800af1a   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    i.usart_baudrate_set                     0x0800af34   Section        0  gd32f4xx_usart.o(i.usart_baudrate_set)
    i.usart_data_receive                     0x0800b01c   Section        0  gd32f4xx_usart.o(i.usart_data_receive)
    i.usart_data_transmit                    0x0800b026   Section        0  gd32f4xx_usart.o(i.usart_data_transmit)
    i.usart_deinit                           0x0800b030   Section        0  gd32f4xx_usart.o(i.usart_deinit)
    i.usart_enable                           0x0800b10c   Section        0  gd32f4xx_usart.o(i.usart_enable)
    i.usart_flag_get                         0x0800b116   Section        0  gd32f4xx_usart.o(i.usart_flag_get)
    i.usart_hardware_flow_cts_config         0x0800b134   Section        0  gd32f4xx_usart.o(i.usart_hardware_flow_cts_config)
    i.usart_hardware_flow_rts_config         0x0800b148   Section        0  gd32f4xx_usart.o(i.usart_hardware_flow_rts_config)
    i.usart_interrupt_enable                 0x0800b15c   Section        0  gd32f4xx_usart.o(i.usart_interrupt_enable)
    i.usart_interrupt_flag_clear             0x0800b176   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_clear)
    i.usart_interrupt_flag_get               0x0800b190   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    i.usart_parity_config                    0x0800b1c8   Section        0  gd32f4xx_usart.o(i.usart_parity_config)
    i.usart_receive_config                   0x0800b1d8   Section        0  gd32f4xx_usart.o(i.usart_receive_config)
    i.usart_stop_bit_set                     0x0800b1e8   Section        0  gd32f4xx_usart.o(i.usart_stop_bit_set)
    i.usart_transmit_config                  0x0800b1f8   Section        0  gd32f4xx_usart.o(i.usart_transmit_config)
    i.usart_word_length_set                  0x0800b208   Section        0  gd32f4xx_usart.o(i.usart_word_length_set)
    i.validate                               0x0800b218   Section        0  ff.o(i.validate)
    validate                                 0x0800b219   Thumb Code    42  ff.o(i.validate)
    .constdata                               0x0800b242   Section     8152  oled.o(.constdata)
    .constdata                               0x0800d21a   Section      256  sdcard_app.o(.constdata)
    unicode_table                            0x0800d21a   Data         256  sdcard_app.o(.constdata)
    .constdata                               0x0800d31a   Section       12  usart_app.o(.constdata)
    days_in_month                            0x0800d31a   Data          12  usart_app.o(.constdata)
    .constdata                               0x0800d326   Section       13  ff.o(.constdata)
    LfnOfs                                   0x0800d326   Data          13  ff.o(.constdata)
    .constdata                               0x0800d333   Section      129  ctype_o.o(.constdata)
    .constdata                               0x0800d3b4   Section        4  ctype_o.o(.constdata)
    table                                    0x0800d3b4   Data           4  ctype_o.o(.constdata)
    .conststring                             0x0800d3b8   Section      188  sdcard_app.o(.conststring)
    .conststring                             0x0800d474   Section       74  timer_app.o(.conststring)
    .data                                    0x20000000   Section        4  systick.o(.data)
    delay                                    0x20000000   Data           4  systick.o(.data)
    .data                                    0x20000004   Section       12  rtc.o(.data)
    .data                                    0x20000010   Section       36  sdcard.o(.data)
    cardtype                                 0x20000018   Data           1  sdcard.o(.data)
    sd_rca                                   0x2000001a   Data           2  sdcard.o(.data)
    transmode                                0x2000001c   Data           4  sdcard.o(.data)
    totalnumber_bytes                        0x20000020   Data           4  sdcard.o(.data)
    stopcondition                            0x20000024   Data           4  sdcard.o(.data)
    transerror                               0x20000028   Data           1  sdcard.o(.data)
    transend                                 0x2000002c   Data           4  sdcard.o(.data)
    number_bytes                             0x20000030   Data           4  sdcard.o(.data)
    .data                                    0x20000034   Section        2  gd30ad3344.o(.data)
    .data                                    0x20000038   Section       14  usart.o(.data)
    .data                                    0x20000048   Section       62  adc_app.o(.data)
    .data                                    0x20000088   Section       32  flash_app.o(.data)
    .data                                    0x200000a8   Section       16  function.o(.data)
    .data                                    0x200000b8   Section        6  key_app.o(.data)
    .data                                    0x200000be   Section      128  oled_app.o(.data)
    .data                                    0x20000140   Section       40  scheduler.o(.data)
    scheduler_task                           0x20000144   Data          36  scheduler.o(.data)
    .data                                    0x20000168   Section       13  sdcard_app.o(.data)
    current_record_count                     0x2000016d   Data           1  sdcard_app.o(.data)
    overlimit_record_count                   0x2000016e   Data           1  sdcard_app.o(.data)
    hidedata_record_count                    0x2000016f   Data           1  sdcard_app.o(.data)
    log_record_count                         0x20000170   Data           1  sdcard_app.o(.data)
    filename_initialized                     0x20000172   Data           1  sdcard_app.o(.data)
    overlimit_filename_initialized           0x20000173   Data           1  sdcard_app.o(.data)
    first_system_init_written                0x20000174   Data           1  sdcard_app.o(.data)
    .data                                    0x20000178   Section       24  timer_app.o(.data)
    led1_blink_mode                          0x20000178   Data           1  timer_app.o(.data)
    led1_counter                             0x2000017c   Data           4  timer_app.o(.data)
    led1_current_state                       0x20000180   Data           1  timer_app.o(.data)
    adc_sampling_state                       0x20000181   Data           1  timer_app.o(.data)
    adc_counter                              0x20000184   Data           4  timer_app.o(.data)
    adc_target_count                         0x20000188   Data           4  timer_app.o(.data)
    sample_count                             0x2000018c   Data           4  timer_app.o(.data)
    .data                                    0x20000190   Section        5  usart_app.o(.data)
    ratio_config_state                       0x20000192   Data           1  usart_app.o(.data)
    limit_config_state                       0x20000193   Data           1  usart_app.o(.data)
    .data                                    0x20000198   Section        4  system_gd32f4xx.o(.data)
    .data                                    0x2000019c   Section        6  ff.o(.data)
    FatFs                                    0x2000019c   Data           4  ff.o(.data)
    Fsid                                     0x200001a0   Data           2  ff.o(.data)
    .data                                    0x200001a4   Section        4  stdout.o(.data)
    .data                                    0x200001a8   Section        4  errno.o(.data)
    _errno                                   0x200001a8   Data           4  errno.o(.data)
    .bss                                     0x200001ac   Section      576  oled.o(.bss)
    .bss                                     0x200003ec   Section       36  rtc.o(.bss)
    .bss                                     0x20000410   Section       32  sdcard.o(.bss)
    sd_csd                                   0x20000410   Data          16  sdcard.o(.bss)
    sd_cid                                   0x20000420   Data          16  sdcard.o(.bss)
    .bss                                     0x20000430   Section       24  gd30ad3344.o(.bss)
    .bss                                     0x20000448   Section      512  usart.o(.bss)
    buffer                                   0x20000548   Data         256  usart.o(.bss)
    .bss                                     0x20000648   Section       12  adc_app.o(.bss)
    .bss                                     0x20000654   Section      128  oled_app.o(.bss)
    .bss                                     0x200006d4   Section     3008  sdcard_app.o(.bss)
    current_filename                         0x20001214   Data          64  sdcard_app.o(.bss)
    current_overlimit_filename               0x20001254   Data          64  sdcard_app.o(.bss)
    .bss                                     0x20001294   Section      512  ff.o(.bss)
    LfnBuf                                   0x20001294   Data         512  ff.o(.bss)
    STACK                                    0x20001498   Section     2048  startup_gd32f450_470.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450_470.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f450_470.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_gd32f450_470.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080001bd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080001bd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x080001c1   Thumb Code     8  startup_gd32f450_470.o(.text)
    ADC_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_EWMC_IRQHandler                     0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX1_IRQHandler                      0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_EWMC_IRQHandler                     0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX1_IRQHandler                      0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DCI_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel0_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel1_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel2_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel3_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel4_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel5_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel6_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel7_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel0_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_WKUP_IRQHandler                     0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXMC_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI0_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI10_15_IRQHandler                     0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI1_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI2_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI3_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI4_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI5_9_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    FMC_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    FPU_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    IPA_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    LVD_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    RCU_CTC_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_Alarm_IRQHandler                     0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_WKUP_IRQHandler                      0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI0_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI1_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI2_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI3_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI4_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI5_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TAMPER_STAMP_IRQHandler                  0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_BRK_TIMER8_IRQHandler             0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_Channel_IRQHandler                0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_UP_TIMER9_IRQHandler              0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER2_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER3_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER4_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER5_DAC_IRQHandler                    0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER6_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_Channel_IRQHandler                0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_ER_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TRNG_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART3_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART4_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART6_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART7_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART2_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART5_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_WKUP_IRQHandler                    0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_In_IRQHandler                  0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_WKUP_IRQHandler                    0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    WWDGT_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    __aeabi_memcpy                           0x080001e5   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x080001e5   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x080001e5   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000209   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000209   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000209   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000217   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000217   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000217   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800021b   Thumb Code    18  memseta.o(.text)
    strstr                                   0x0800022d   Thumb Code    36  strstr.o(.text)
    strncpy                                  0x08000251   Thumb Code    24  strncpy.o(.text)
    strlen                                   0x08000269   Thumb Code    14  strlen.o(.text)
    strcmp                                   0x08000277   Thumb Code    28  strcmp.o(.text)
    strcpy                                   0x08000293   Thumb Code    18  strcpy.o(.text)
    strncmp                                  0x080002a5   Thumb Code    30  strncmp.o(.text)
    atoi                                     0x080002c3   Thumb Code    26  atoi.o(.text)
    __aeabi_f2d                              0x080002dd   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x08000303   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x0800033b   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x0800033b   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x08000367   Thumb Code    98  uldiv.o(.text)
    __strtod_int                             0x080003ff   Thumb Code    90  strtod.o(.text)
    strtol                                   0x08000465   Thumb Code   112  strtol.o(.text)
    __I$use$fp                               0x080004d5   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x080004d5   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x080004e7   Thumb Code    92  fepilogue.o(.text)
    __aeabi_dadd                             0x08000543   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000685   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x0800068b   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08000691   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000775   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x08000853   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000885   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x080008b5   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080008b5   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x080008d9   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080008d9   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080008f7   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080008f7   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08000917   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000917   Thumb Code     0  llsshr.o(.text)
    __rt_ctype_table                         0x0800093d   Thumb Code     4  ctype_o.o(.text)
    isspace                                  0x08000945   Thumb Code    18  isspace_o.o(.text)
    _scanf_real                              0x08000a81   Thumb Code     0  scanf_fp.o(.text)
    _scanf_really_real                       0x08000a81   Thumb Code   556  scanf_fp.o(.text)
    _sgetc                                   0x08000cb9   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000cd7   Thumb Code    34  _sgetc.o(.text)
    _strtoul                                 0x08000cf9   Thumb Code   158  _strtoul.o(.text)
    _double_round                            0x08000d97   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000db5   Thumb Code   156  depilogue.o(.text)
    _chval                                   0x08000e51   Thumb Code    28  _chval.o(.text)
    __aeabi_ul2d                             0x08000e6d   Thumb Code    24  dfltul.o(.text)
    __decompress                             0x08000e85   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x08000e85   Thumb Code    86  __dczerorl2.o(.text)
    ADC_Init                                 0x08000edd   Thumb Code   100  adc.o(i.ADC_Init)
    ADC_Proc                                 0x08000f49   Thumb Code   216  adc_app.o(i.ADC_Proc)
    BusFault_Handler                         0x08001065   Thumb Code     4  gd32f4xx_it.o(i.BusFault_Handler)
    Cache_Log_To_Flash                       0x08001069   Thumb Code   218  flash_app.o(i.Cache_Log_To_Flash)
    Calculate_Config_Checksum                0x08001155   Thumb Code    26  flash_app.o(i.Calculate_Config_Checksum)
    Calculate_Multi_Channel_Config_Checksum  0x0800116f   Thumb Code    26  flash_app.o(i.Calculate_Multi_Channel_Config_Checksum)
    Check_Cached_Log_In_Flash                0x08001189   Thumb Code    30  flash_app.o(i.Check_Cached_Log_In_Flash)
    Close_OverLimit_File                     0x080011ad   Thumb Code    24  sdcard_app.o(i.Close_OverLimit_File)
    Close_Sample_File                        0x080011d1   Thumb Code    42  sdcard_app.o(i.Close_Sample_File)
    Configure_RTC_With_DateTime              0x0800120d   Thumb Code   210  rtc_app.o(i.Configure_RTC_With_DateTime)
    Convert_RTC_To_Unix_Timestamp            0x080012f1   Thumb Code   234  usart_app.o(i.Convert_RTC_To_Unix_Timestamp)
    Convert_Uint32_To_Hex_String             0x080013e1   Thumb Code    60  usart_app.o(i.Convert_Uint32_To_Hex_String)
    Create_Default_Config_INI                0x0800141d   Thumb Code   112  sdcard_app.o(i.Create_Default_Config_INI)
    Create_HideData_File                     0x080014a1   Thumb Code   118  sdcard_app.o(i.Create_HideData_File)
    Create_Log0_File                         0x0800154d   Thumb Code    44  sdcard_app.o(i.Create_Log0_File)
    Create_Log_File                          0x08001589   Thumb Code   108  sdcard_app.o(i.Create_Log_File)
    Create_Sample_File                       0x08001619   Thumb Code    42  sdcard_app.o(i.Create_Sample_File)
    DebugMon_Handler                         0x08001655   Thumb Code     2  gd32f4xx_it.o(i.DebugMon_Handler)
    Encode_Voltage_To_Hex                    0x08001659   Thumb Code    80  usart_app.o(i.Encode_Voltage_To_Hex)
    Flash_Init                               0x080016b1   Thumb Code   254  flash_app.o(i.Flash_Init)
    GD30AD3344_AD_Read                       0x0800182d   Thumb Code   182  gd30ad3344.o(i.GD30AD3344_AD_Read)
    GD30AD3344_PGA_SET                       0x080018f1   Thumb Code    98  gd30ad3344.o(i.GD30AD3344_PGA_SET)
    Generate_Encrypted_Output_With_Values    0x08001975   Thumb Code   130  usart_app.o(i.Generate_Encrypted_Output_With_Values)
    Generate_Encrypted_Output_With_Values_OverLimit 0x08001a29   Thumb Code    72  usart_app.o(i.Generate_Encrypted_Output_With_Values_OverLimit)
    Generate_HideData_Filename               0x08001aed   Thumb Code    62  sdcard_app.o(i.Generate_HideData_Filename)
    Generate_OverLimit_Filename              0x08001b99   Thumb Code    62  sdcard_app.o(i.Generate_OverLimit_Filename)
    Get_Limit                                0x08001c11   Thumb Code     8  adc_app.o(i.Get_Limit)
    Get_Next_Log_ID_From_SD                  0x08001c1d   Thumb Code    84  sdcard_app.o(i.Get_Next_Log_ID_From_SD)
    Get_Ratio                                0x08001c95   Thumb Code     8  adc_app.o(i.Get_Ratio)
    Get_Sample_Interval                      0x08001ca1   Thumb Code     6  adc_app.o(i.Get_Sample_Interval)
    Handle_Command_Get_Limit                 0x08001cad   Thumb Code    70  usart_app.o(i.Handle_Command_Get_Limit)
    Handle_Command_Set_Limit                 0x08001d35   Thumb Code   376  usart_app.o(i.Handle_Command_Set_Limit)
    Handle_Command_Set_RTC                   0x08001f45   Thumb Code   350  usart_app.o(i.Handle_Command_Set_RTC)
    Handle_Command_Set_Ratio                 0x080020e9   Thumb Code   388  usart_app.o(i.Handle_Command_Set_Ratio)
    Handle_Get_Data_Command                  0x08002309   Thumb Code   286  usart_app.o(i.Handle_Get_Data_Command)
    Handle_Get_Device_ID_Command             0x08002491   Thumb Code    14  usart_app.o(i.Handle_Get_Device_ID_Command)
    Handle_Get_Ratio_Command                 0x080024c1   Thumb Code   134  usart_app.o(i.Handle_Get_Ratio_Command)
    Handle_Key1_Press                        0x080025bd   Thumb Code    20  key_app.o(i.Handle_Key1_Press)
    Handle_Key2_Press                        0x080025fd   Thumb Code    20  key_app.o(i.Handle_Key2_Press)
    Handle_Key3_Press                        0x0800263d   Thumb Code    20  key_app.o(i.Handle_Key3_Press)
    Handle_Key4_Press                        0x0800267d   Thumb Code    20  key_app.o(i.Handle_Key4_Press)
    Handle_Key5_Press                        0x080026c1   Thumb Code    20  key_app.o(i.Handle_Key5_Press)
    Handle_Key6_Press                        0x08002705   Thumb Code    20  key_app.o(i.Handle_Key6_Press)
    Handle_Multi_Config_Read_Command         0x08002749   Thumb Code   488  usart_app.o(i.Handle_Multi_Config_Read_Command)
    Handle_Multi_Config_Save_Command         0x08002a7d   Thumb Code   256  usart_app.o(i.Handle_Multi_Config_Save_Command)
    Handle_RTC_Now_Command                   0x08002c9d   Thumb Code    50  usart_app.o(i.Handle_RTC_Now_Command)
    Handle_Start_Command                     0x08002d09   Thumb Code     8  usart_app.o(i.Handle_Start_Command)
    Handle_Stop_Command                      0x08002d11   Thumb Code    20  usart_app.o(i.Handle_Stop_Command)
    HardFault_Handler                        0x08002d49   Thumb Code     4  gd32f4xx_it.o(i.HardFault_Handler)
    I2C_Start                                0x08002d4d   Thumb Code    84  oled.o(i.I2C_Start)
    I2C_Stop                                 0x08002da5   Thumb Code    66  oled.o(i.I2C_Stop)
    I2C_WaitAck                              0x08002ded   Thumb Code   116  oled.o(i.I2C_WaitAck)
    IIC_delay                                0x08002e65   Thumb Code    16  oled.o(i.IIC_delay)
    Init_Data_Recording                      0x08002e75   Thumb Code   198  sdcard_app.o(i.Init_Data_Recording)
    Key_Init                                 0x08002fa1   Thumb Code    26  key.o(i.Key_Init)
    Key_Proc                                 0x08002fc1   Thumb Code   144  key_app.o(i.Key_Proc)
    Key_Read                                 0x08003061   Thumb Code   104  key.o(i.Key_Read)
    Led_Init                                 0x080030cd   Thumb Code    50  led.o(i.Led_Init)
    Log_Init                                 0x08003105   Thumb Code    10  flash_app.o(i.Log_Init)
    MemManage_Handler                        0x08003115   Thumb Code     4  gd32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08003119   Thumb Code     2  gd32f4xx_it.o(i.NMI_Handler)
    OLED_App_Init                            0x08003145   Thumb Code    48  oled_app.o(i.OLED_App_Init)
    OLED_Clear                               0x0800318d   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_ClearPoint                          0x080031bd   Thumb Code    90  oled.o(i.OLED_ClearPoint)
    OLED_DrawPoint                           0x0800321d   Thumb Code    54  oled.o(i.OLED_DrawPoint)
    OLED_Init                                0x08003259   Thumb Code   324  oled.o(i.OLED_Init)
    OLED_Refresh                             0x080033a1   Thumb Code    70  oled.o(i.OLED_Refresh)
    OLED_ShowChar                            0x080033ed   Thumb Code   240  oled.o(i.OLED_ShowChar)
    OLED_ShowString                          0x080034e9   Thumb Code    66  oled.o(i.OLED_ShowString)
    OLED_WR_Byte                             0x0800352b   Thumb Code    56  oled.o(i.OLED_WR_Byte)
    PendSV_Handler                           0x08003563   Thumb Code     2  gd32f4xx_it.o(i.PendSV_Handler)
    RS485_Task                               0x08003565   Thumb Code   530  usart_app.o(i.RS485_Task)
    RTC_Init                                 0x080038c5   Thumb Code   108  rtc.o(i.RTC_Init)
    Read_Config_From_Flash                   0x08003941   Thumb Code    70  flash_app.o(i.Read_Config_From_Flash)
    Read_Multi_Channel_Config_From_Flash     0x08003987   Thumb Code   120  flash_app.o(i.Read_Multi_Channel_Config_From_Flash)
    SDIO_IRQHandler                          0x080039ff   Thumb Code     8  gd32f4xx_it.o(i.SDIO_IRQHandler)
    SVC_Handler                              0x08003a07   Thumb Code     2  gd32f4xx_it.o(i.SVC_Handler)
    Save_Config_To_Flash                     0x08003a09   Thumb Code   152  flash_app.o(i.Save_Config_To_Flash)
    Save_Multi_Channel_Config_To_Flash       0x08003aad   Thumb Code   268  flash_app.o(i.Save_Multi_Channel_Config_To_Flash)
    Send_Byte                                0x08003bc9   Thumb Code    88  oled.o(i.Send_Byte)
    Set_ADC_Sample_Interval                  0x08003c25   Thumb Code    12  timer_app.o(i.Set_ADC_Sample_Interval)
    Set_ADC_Sampling_State                   0x08003c39   Thumb Code    12  timer_app.o(i.Set_ADC_Sampling_State)
    Set_LED1_Blink_Mode                      0x08003c4d   Thumb Code    32  timer_app.o(i.Set_LED1_Blink_Mode)
    Set_LED2_State                           0x08003c7d   Thumb Code    30  timer_app.o(i.Set_LED2_State)
    Set_Limit                                0x08003ca1   Thumb Code    32  adc_app.o(i.Set_Limit)
    Set_Over_Limit_State                     0x08003cc9   Thumb Code    26  adc_app.o(i.Set_Over_Limit_State)
    Set_Ratio                                0x08003ce9   Thumb Code    32  adc_app.o(i.Set_Ratio)
    Set_Sample_Interval                      0x08003d11   Thumb Code    16  adc_app.o(i.Set_Sample_Interval)
    Set_System_State                         0x08003d25   Thumb Code    14  adc_app.o(i.Set_System_State)
    Start_Sampling                           0x08003d39   Thumb Code    34  adc_app.o(i.Start_Sampling)
    Stop_Sampling                            0x08003d69   Thumb Code    32  adc_app.o(i.Stop_Sampling)
    SysTick_Handler                          0x08003d91   Thumb Code    18  gd32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08003da9   Thumb Code   194  system_gd32f4xx.o(i.SystemInit)
    System_Init                              0x08003e7d   Thumb Code     8  function.o(i.System_Init)
    TIMER1_IRQHandler                        0x08003e85   Thumb Code    32  timer.o(i.TIMER1_IRQHandler)
    Timer_ADC_Handler                        0x08003ea5   Thumb Code   968  timer_app.o(i.Timer_ADC_Handler)
    Timer_Init                               0x080042fd   Thumb Code    88  timer.o(i.Timer_Init)
    Timer_LED_Handler                        0x08004355   Thumb Code    92  timer_app.o(i.Timer_LED_Handler)
    USART0_IRQHandler                        0x080043c1   Thumb Code   130  usart.o(i.USART0_IRQHandler)
    USART1_IRQHandler                        0x08004471   Thumb Code   130  usart.o(i.USART1_IRQHandler)
    Update_Config_INI                        0x08004521   Thumb Code   222  sdcard_app.o(i.Update_Config_INI)
    Update_Current_Display                   0x08004615   Thumb Code   438  key_app.o(i.Update_Current_Display)
    Update_LED_Status                        0x08004879   Thumb Code    72  adc_app.o(i.Update_LED_Status)
    UsageFault_Handler                       0x080048c5   Thumb Code     4  gd32f4xx_it.o(i.UsageFault_Handler)
    UsrFunction                              0x080048c9   Thumb Code    74  function.o(i.UsrFunction)
    Verify_Config_Data                       0x08004921   Thumb Code   132  flash_app.o(i.Verify_Config_Data)
    Verify_Multi_Channel_Config_Data         0x080049b1   Thumb Code   250  flash_app.o(i.Verify_Multi_Channel_Config_Data)
    Write_Cached_Log_To_Log0                 0x08004abd   Thumb Code   166  flash_app.o(i.Write_Cached_Log_To_Log0)
    Write_HideData                           0x08004b85   Thumb Code   148  sdcard_app.o(i.Write_HideData)
    Write_Log_Data                           0x08004c3d   Thumb Code   190  sdcard_app.o(i.Write_Log_Data)
    Write_OverLimit_Data                     0x08004d49   Thumb Code   300  sdcard_app.o(i.Write_OverLimit_Data)
    Write_Sample_Data_MultiChannel           0x08004ed1   Thumb Code   400  sdcard_app.o(i.Write_Sample_Data_MultiChannel)
    __0printf                                0x080050e1   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x080050e1   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x080050e1   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x080050e1   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x080050e1   Thumb Code     0  printfa.o(i.__0printf)
    __0snprintf                              0x08005101   Thumb Code    44  printfa.o(i.__0snprintf)
    __1snprintf                              0x08005101   Thumb Code     0  printfa.o(i.__0snprintf)
    __2snprintf                              0x08005101   Thumb Code     0  printfa.o(i.__0snprintf)
    __c89snprintf                            0x08005101   Thumb Code     0  printfa.o(i.__0snprintf)
    snprintf                                 0x08005101   Thumb Code     0  printfa.o(i.__0snprintf)
    __0vsnprintf                             0x08005131   Thumb Code    40  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08005131   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08005131   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08005131   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08005131   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __aeabi_errno_addr                       0x0800515d   Thumb Code     4  errno.o(i.__aeabi_errno_addr)
    __rt_errno_addr                          0x0800515d   Thumb Code     0  errno.o(i.__aeabi_errno_addr)
    __hardfp_atof                            0x08005169   Thumb Code    44  atof.o(i.__hardfp_atof)
    __read_errno                             0x080051a1   Thumb Code     6  errno.o(i.__read_errno)
    __scatterload_copy                       0x080051ad   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080051bb   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080051bd   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x080051cd   Thumb Code     6  errno.o(i.__set_errno)
    _is_digit                                0x0800535d   Thumb Code    14  scanf_fp.o(i._is_digit)
    adc_calibration_enable                   0x08005ab1   Thumb Code    42  gd32f4xx_adc.o(i.adc_calibration_enable)
    adc_channel_length_config                0x08005adb   Thumb Code    82  gd32f4xx_adc.o(i.adc_channel_length_config)
    adc_clock_config                         0x08005b2d   Thumb Code    28  gd32f4xx_adc.o(i.adc_clock_config)
    adc_data_alignment_config                0x08005b51   Thumb Code    22  gd32f4xx_adc.o(i.adc_data_alignment_config)
    adc_deinit                               0x08005b67   Thumb Code    20  gd32f4xx_adc.o(i.adc_deinit)
    adc_enable                               0x08005b7b   Thumb Code    18  gd32f4xx_adc.o(i.adc_enable)
    adc_resolution_config                    0x08005b8d   Thumb Code    16  gd32f4xx_adc.o(i.adc_resolution_config)
    adc_routine_channel_config               0x08005b9d   Thumb Code   172  gd32f4xx_adc.o(i.adc_routine_channel_config)
    clust2sect                               0x08006095   Thumb Code    26  ff.o(i.clust2sect)
    delay_1ms                                0x080064d1   Thumb Code    16  systick.o(i.delay_1ms)
    delay_decrement                          0x080064e5   Thumb Code    18  systick.o(i.delay_decrement)
    disk_initialize                          0x0800691b   Thumb Code   134  diskio.o(i.disk_initialize)
    disk_ioctl                               0x080069a1   Thumb Code     6  diskio.o(i.disk_ioctl)
    disk_read                                0x080069a7   Thumb Code    80  diskio.o(i.disk_read)
    disk_status                              0x080069f7   Thumb Code    12  diskio.o(i.disk_status)
    disk_write                               0x08006a03   Thumb Code    80  diskio.o(i.disk_write)
    dma_channel_disable                      0x08006a53   Thumb Code    32  gd32f4xx_dma.o(i.dma_channel_disable)
    dma_channel_enable                       0x08006a73   Thumb Code    32  gd32f4xx_dma.o(i.dma_channel_enable)
    dma_channel_subperipheral_select         0x08006a93   Thumb Code    38  gd32f4xx_dma.o(i.dma_channel_subperipheral_select)
    dma_deinit                               0x08006ab9   Thumb Code   166  gd32f4xx_dma.o(i.dma_deinit)
    dma_flag_clear                           0x08006b5f   Thumb Code    62  gd32f4xx_dma.o(i.dma_flag_clear)
    dma_flag_get                             0x08006b9d   Thumb Code    76  gd32f4xx_dma.o(i.dma_flag_get)
    dma_flow_controller_config               0x08006be9   Thumb Code    64  gd32f4xx_dma.o(i.dma_flow_controller_config)
    dma_multi_data_mode_init                 0x08006c29   Thumb Code   352  gd32f4xx_dma.o(i.dma_multi_data_mode_init)
    dma_single_data_mode_init                0x08006e41   Thumb Code   340  gd32f4xx_dma.o(i.dma_single_data_mode_init)
    f_close                                  0x0800704d   Thumb Code    22  ff.o(i.f_close)
    f_lseek                                  0x08007063   Thumb Code   432  ff.o(i.f_lseek)
    f_mkdir                                  0x08007215   Thumb Code   388  ff.o(i.f_mkdir)
    f_mount                                  0x0800739d   Thumb Code    38  ff.o(i.f_mount)
    f_open                                   0x080073c9   Thumb Code   368  ff.o(i.f_open)
    f_opendir                                0x0800753d   Thumb Code   114  ff.o(i.f_opendir)
    f_sync                                   0x080075b5   Thumb Code   184  ff.o(i.f_sync)
    f_write                                  0x0800766d   Thumb Code   526  ff.o(i.f_write)
    ff_convert                               0x0800787d   Thumb Code    70  sdcard_app.o(i.ff_convert)
    ff_wtoupper                              0x080078c9   Thumb Code    42  sdcard_app.o(i.ff_wtoupper)
    fputc                                    0x08007a15   Thumb Code    32  usart.o(i.fputc)
    gd30ad3344_init                          0x08007a39   Thumb Code   344  gd30ad3344.o(i.gd30ad3344_init)
    gen_numname                              0x08007b9d   Thumb Code   202  ff.o(i.gen_numname)
    get_fat                                  0x08007c8d   Thumb Code   228  ff.o(i.get_fat)
    get_fattime                              0x08007d71   Thumb Code    66  diskio.o(i.get_fattime)
    gpio_af_set                              0x08007db3   Thumb Code    94  gd32f4xx_gpio.o(i.gpio_af_set)
    gpio_bit_reset                           0x08007e11   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_reset)
    gpio_bit_set                             0x08007e15   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_set)
    gpio_input_bit_get                       0x08007e8d   Thumb Code    16  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    gpio_mode_set                            0x08007e9d   Thumb Code    78  gd32f4xx_gpio.o(i.gpio_mode_set)
    gpio_output_options_set                  0x08007eeb   Thumb Code    66  gd32f4xx_gpio.o(i.gpio_output_options_set)
    main                                     0x08007f61   Thumb Code    14  main.o(i.main)
    nvic_config                              0x08008035   Thumb Code    22  sdcard_app.o(i.nvic_config)
    nvic_irq_enable                          0x0800804d   Thumb Code   186  gd32f4xx_misc.o(i.nvic_irq_enable)
    nvic_priority_group_set                  0x08008111   Thumb Code    10  gd32f4xx_misc.o(i.nvic_priority_group_set)
    pmu_backup_write_enable                  0x08008125   Thumb Code    14  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    put_fat                                  0x08008139   Thumb Code   310  ff.o(i.put_fat)
    rcu_all_reset_flag_clear                 0x08008529   Thumb Code    14  gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear)
    rcu_clock_freq_get                       0x0800853d   Thumb Code   264  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    rcu_flag_get                             0x08008685   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_flag_get)
    rcu_osci_on                              0x080086a9   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_osci_on)
    rcu_periph_clock_enable                  0x080086cd   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x080086f1   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x08008715   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    rcu_rtc_clock_config                     0x08008739   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    rs485_printf                             0x080087b9   Thumb Code   118  usart.o(i.rs485_printf)
    rs485_usart1_config                      0x0800883d   Thumb Code   240  usart.o(i.rs485_usart1_config)
    rtc_current_time_get                     0x0800893d   Thumb Code    96  gd32f4xx_rtc.o(i.rtc_current_time_get)
    rtc_init                                 0x080089a1   Thumb Code   216  gd32f4xx_rtc.o(i.rtc_init)
    rtc_init_mode_enter                      0x08008a7d   Thumb Code    66  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    rtc_init_mode_exit                       0x08008ac5   Thumb Code    14  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    rtc_pre_config                           0x08008ad9   Thumb Code    78  rtc.o(i.rtc_pre_config)
    rtc_register_sync_wait                   0x08008b35   Thumb Code    92  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    rtc_setup_default                        0x08008b95   Thumb Code    74  rtc.o(i.rtc_setup_default)
    scheduler_init                           0x08008bf1   Thumb Code     8  scheduler.o(i.scheduler_init)
    scheduler_run                            0x08008bfd   Thumb Code    76  scheduler.o(i.scheduler_run)
    sd_block_read                            0x08008c55   Thumb Code   538  sdcard.o(i.sd_block_read)
    sd_block_write                           0x08008e95   Thumb Code   784  sdcard.o(i.sd_block_write)
    sd_bus_mode_config                       0x080091cd   Thumb Code   144  sdcard.o(i.sd_bus_mode_config)
    sd_card_information_get                  0x0800935d   Thumb Code   686  sdcard.o(i.sd_card_information_get)
    sd_card_init                             0x0800961d   Thumb Code   268  sdcard.o(i.sd_card_init)
    sd_card_select_deselect                  0x08009739   Thumb Code    38  sdcard.o(i.sd_card_select_deselect)
    sd_cardstatus_get                        0x08009819   Thumb Code    66  sdcard.o(i.sd_cardstatus_get)
    sd_init                                  0x08009879   Thumb Code    70  sdcard.o(i.sd_init)
    sd_interrupts_process                    0x080098c1   Thumb Code   286  sdcard.o(i.sd_interrupts_process)
    sd_multiblocks_read                      0x080099f1   Thumb Code   672  sdcard.o(i.sd_multiblocks_read)
    sd_multiblocks_write                     0x08009cb5   Thumb Code   898  sdcard.o(i.sd_multiblocks_write)
    sd_power_on                              0x0800a065   Thumb Code   290  sdcard.o(i.sd_power_on)
    sd_transfer_mode_config                  0x0800a309   Thumb Code    20  sdcard.o(i.sd_transfer_mode_config)
    sd_transfer_stop                         0x0800a321   Thumb Code    36  sdcard.o(i.sd_transfer_stop)
    sdio_bus_mode_set                        0x0800a345   Thumb Code    22  gd32f4xx_sdio.o(i.sdio_bus_mode_set)
    sdio_clock_config                        0x0800a361   Thumb Code    44  gd32f4xx_sdio.o(i.sdio_clock_config)
    sdio_clock_enable                        0x0800a395   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_clock_enable)
    sdio_command_index_get                   0x0800a3a9   Thumb Code     8  gd32f4xx_sdio.o(i.sdio_command_index_get)
    sdio_command_response_config             0x0800a3b5   Thumb Code    52  gd32f4xx_sdio.o(i.sdio_command_response_config)
    sdio_csm_enable                          0x0800a3ed   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_csm_enable)
    sdio_data_config                         0x0800a401   Thumb Code    54  gd32f4xx_sdio.o(i.sdio_data_config)
    sdio_data_read                           0x0800a43d   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_data_read)
    sdio_data_transfer_config                0x0800a449   Thumb Code    24  gd32f4xx_sdio.o(i.sdio_data_transfer_config)
    sdio_data_write                          0x0800a465   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_data_write)
    sdio_deinit                              0x0800a471   Thumb Code    20  gd32f4xx_sdio.o(i.sdio_deinit)
    sdio_dma_disable                         0x0800a485   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dma_disable)
    sdio_dma_enable                          0x0800a499   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dma_enable)
    sdio_dsm_disable                         0x0800a4ad   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dsm_disable)
    sdio_dsm_enable                          0x0800a4c1   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dsm_enable)
    sdio_flag_clear                          0x0800a4d5   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_flag_clear)
    sdio_flag_get                            0x0800a4e1   Thumb Code    16  gd32f4xx_sdio.o(i.sdio_flag_get)
    sdio_hardware_clock_disable              0x0800a4f5   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_hardware_clock_disable)
    sdio_interrupt_disable                   0x0800a509   Thumb Code    12  gd32f4xx_sdio.o(i.sdio_interrupt_disable)
    sdio_interrupt_enable                    0x0800a519   Thumb Code    12  gd32f4xx_sdio.o(i.sdio_interrupt_enable)
    sdio_interrupt_flag_clear                0x0800a529   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear)
    sdio_interrupt_flag_get                  0x0800a535   Thumb Code    16  gd32f4xx_sdio.o(i.sdio_interrupt_flag_get)
    sdio_power_state_get                     0x0800a549   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_power_state_get)
    sdio_power_state_set                     0x0800a555   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_power_state_set)
    sdio_response_get                        0x0800a561   Thumb Code    56  gd32f4xx_sdio.o(i.sdio_response_get)
    sdio_wait_type_set                       0x0800a59d   Thumb Code    22  gd32f4xx_sdio.o(i.sdio_wait_type_set)
    spi_dma_disable                          0x0800a5b9   Thumb Code    22  gd32f4xx_spi.o(i.spi_dma_disable)
    spi_dma_enable                           0x0800a5cf   Thumb Code    22  gd32f4xx_spi.o(i.spi_dma_enable)
    spi_enable                               0x0800a5e5   Thumb Code    10  gd32f4xx_spi.o(i.spi_enable)
    spi_flash_buffer_read                    0x0800a5f1   Thumb Code    80  spi_flash.o(i.spi_flash_buffer_read)
    spi_flash_buffer_write                   0x0800a645   Thumb Code   196  spi_flash.o(i.spi_flash_buffer_write)
    spi_flash_init                           0x0800a709   Thumb Code   146  spi_flash.o(i.spi_flash_init)
    spi_flash_page_write                     0x0800a7a5   Thumb Code    86  spi_flash.o(i.spi_flash_page_write)
    spi_flash_read_id                        0x0800a801   Thumb Code    78  spi_flash.o(i.spi_flash_read_id)
    spi_flash_sector_erase                   0x0800a855   Thumb Code    62  spi_flash.o(i.spi_flash_sector_erase)
    spi_flash_send_byte                      0x0800a899   Thumb Code    50  spi_flash.o(i.spi_flash_send_byte)
    spi_flash_wait_for_write_end             0x0800a8d1   Thumb Code    50  spi_flash.o(i.spi_flash_wait_for_write_end)
    spi_flash_write_enable                   0x0800a909   Thumb Code    30  spi_flash.o(i.spi_flash_write_enable)
    spi_gd30ad3344_send_halfword_dma         0x0800a92d   Thumb Code   268  gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma)
    spi_i2s_data_receive                     0x0800aa4d   Thumb Code     8  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    spi_i2s_data_transmit                    0x0800aa55   Thumb Code     4  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    spi_i2s_flag_get                         0x0800aa59   Thumb Code    16  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    spi_init                                 0x0800aa69   Thumb Code    50  gd32f4xx_spi.o(i.spi_init)
    systick_config                           0x0800ac95   Thumb Code    74  systick.o(i.systick_config)
    timer_deinit                             0x0800ace5   Thumb Code   374  gd32f4xx_timer.o(i.timer_deinit)
    timer_enable                             0x0800ae69   Thumb Code    10  gd32f4xx_timer.o(i.timer_enable)
    timer_init                               0x0800ae75   Thumb Code   122  gd32f4xx_timer.o(i.timer_init)
    timer_interrupt_enable                   0x0800af0d   Thumb Code     8  gd32f4xx_timer.o(i.timer_interrupt_enable)
    timer_interrupt_flag_clear               0x0800af15   Thumb Code     6  gd32f4xx_timer.o(i.timer_interrupt_flag_clear)
    timer_interrupt_flag_get                 0x0800af1b   Thumb Code    24  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    usart_baudrate_set                       0x0800af35   Thumb Code   224  gd32f4xx_usart.o(i.usart_baudrate_set)
    usart_data_receive                       0x0800b01d   Thumb Code    10  gd32f4xx_usart.o(i.usart_data_receive)
    usart_data_transmit                      0x0800b027   Thumb Code     8  gd32f4xx_usart.o(i.usart_data_transmit)
    usart_deinit                             0x0800b031   Thumb Code   210  gd32f4xx_usart.o(i.usart_deinit)
    usart_enable                             0x0800b10d   Thumb Code    10  gd32f4xx_usart.o(i.usart_enable)
    usart_flag_get                           0x0800b117   Thumb Code    30  gd32f4xx_usart.o(i.usart_flag_get)
    usart_hardware_flow_cts_config           0x0800b135   Thumb Code    20  gd32f4xx_usart.o(i.usart_hardware_flow_cts_config)
    usart_hardware_flow_rts_config           0x0800b149   Thumb Code    20  gd32f4xx_usart.o(i.usart_hardware_flow_rts_config)
    usart_interrupt_enable                   0x0800b15d   Thumb Code    26  gd32f4xx_usart.o(i.usart_interrupt_enable)
    usart_interrupt_flag_clear               0x0800b177   Thumb Code    26  gd32f4xx_usart.o(i.usart_interrupt_flag_clear)
    usart_interrupt_flag_get                 0x0800b191   Thumb Code    56  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    usart_parity_config                      0x0800b1c9   Thumb Code    16  gd32f4xx_usart.o(i.usart_parity_config)
    usart_receive_config                     0x0800b1d9   Thumb Code    16  gd32f4xx_usart.o(i.usart_receive_config)
    usart_stop_bit_set                       0x0800b1e9   Thumb Code    16  gd32f4xx_usart.o(i.usart_stop_bit_set)
    usart_transmit_config                    0x0800b1f9   Thumb Code    16  gd32f4xx_usart.o(i.usart_transmit_config)
    usart_word_length_set                    0x0800b209   Thumb Code    16  gd32f4xx_usart.o(i.usart_word_length_set)
    F6x8                                     0x0800b242   Data         552  oled.o(.constdata)
    F8X16                                    0x0800b46a   Data        1520  oled.o(.constdata)
    asc2_1206                                0x0800ba5a   Data        1140  oled.o(.constdata)
    asc2_1608                                0x0800bece   Data        1520  oled.o(.constdata)
    asc2_2412                                0x0800c4be   Data        3420  oled.o(.constdata)
    __ctype_table                            0x0800d333   Data         129  ctype_o.o(.constdata)
    Region$$Table$$Base                      0x0800d4c0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800d4e0   Number         0  anon$$obj.o(Region$$Table)
    prescaler_a                              0x20000004   Data           4  rtc.o(.data)
    prescaler_s                              0x20000008   Data           4  rtc.o(.data)
    RTCSRC_FLAG                              0x2000000c   Data           4  rtc.o(.data)
    sd_scr                                   0x20000010   Data           8  sdcard.o(.data)
    GD30AD3344_InitStruct                    0x20000034   Data           2  gd30ad3344.o(.data)
    uart_rx_ticks                            0x20000038   Data           4  usart.o(.data)
    uart_rx_index                            0x2000003c   Data           1  usart.o(.data)
    uart_rx_idle_flag                        0x2000003d   Data           1  usart.o(.data)
    rs485_rx_ticks                           0x20000040   Data           4  usart.o(.data)
    rs485_rx_index                           0x20000044   Data           1  usart.o(.data)
    rs485_rx_idle_flag                       0x20000045   Data           1  usart.o(.data)
    current_ratio                            0x20000048   Data           4  adc_app.o(.data)
    current_limit                            0x2000004c   Data           4  adc_app.o(.data)
    ch0_ratio                                0x20000050   Data           4  adc_app.o(.data)
    ch1_ratio                                0x20000054   Data           4  adc_app.o(.data)
    ch2_ratio                                0x20000058   Data           4  adc_app.o(.data)
    ch0_limit                                0x2000005c   Data           4  adc_app.o(.data)
    ch1_limit                                0x20000060   Data           4  adc_app.o(.data)
    ch2_limit                                0x20000064   Data           4  adc_app.o(.data)
    over_limit_state                         0x20000068   Data           1  adc_app.o(.data)
    adc_state                                0x20000069   Data           1  adc_app.o(.data)
    system_state                             0x2000006a   Data           1  adc_app.o(.data)
    sample_interval                          0x2000006c   Data           4  adc_app.o(.data)
    ch0_data                                 0x20000070   Data           4  adc_app.o(.data)
    ch1_data                                 0x20000074   Data           4  adc_app.o(.data)
    ch2_data                                 0x20000078   Data           4  adc_app.o(.data)
    ch0_data_valid                           0x2000007c   Data           1  adc_app.o(.data)
    ch1_data_valid                           0x2000007d   Data           1  adc_app.o(.data)
    ch2_data_valid                           0x2000007e   Data           1  adc_app.o(.data)
    intensity_flag                           0x2000007f   Data           1  adc_app.o(.data)
    intensity_flag_single                    0x20000080   Data           1  adc_app.o(.data)
    res_flag                                 0x20000081   Data           1  adc_app.o(.data)
    res_flag_single                          0x20000082   Data           1  adc_app.o(.data)
    vol_flag                                 0x20000083   Data           1  adc_app.o(.data)
    vol_flag_single                          0x20000084   Data           1  adc_app.o(.data)
    read_mode                                0x20000085   Data           1  adc_app.o(.data)
    flash_id                                 0x20000088   Data           4  flash_app.o(.data)
    device_id_buffer                         0x2000008c   Data          21  flash_app.o(.data)
    current_log_id                           0x200000a4   Data           4  flash_app.o(.data)
    test                                     0x200000a8   Data          10  function.o(.data)
    uwTick                                   0x200000b4   Data           4  function.o(.data)
    key_val                                  0x200000b8   Data           1  key_app.o(.data)
    key_down                                 0x200000b9   Data           1  key_app.o(.data)
    key_up                                   0x200000ba   Data           1  key_app.o(.data)
    key_old                                  0x200000bb   Data           1  key_app.o(.data)
    current_sample_period                    0x200000bc   Data           1  key_app.o(.data)
    current_display_mode                     0x200000bd   Data           1  key_app.o(.data)
    oled1_buffer                             0x200000be   Data         128  oled_app.o(.data)
    task_num                                 0x20000140   Data           1  scheduler.o(.data)
    bw                                       0x20000168   Data           4  sdcard_app.o(.data)
    record_state                             0x2000016c   Data           1  sdcard_app.o(.data)
    log_file_created                         0x20000171   Data           1  sdcard_app.o(.data)
    MYDEVICE_ID                              0x20000190   Data           2  usart_app.o(.data)
    current_output_mode                      0x20000194   Data           1  usart_app.o(.data)
    SystemCoreClock                          0x20000198   Data           4  system_gd32f4xx.o(.data)
    __stdout                                 0x200001a4   Data           4  stdout.o(.data)
    OLED_GRAM                                0x200001ac   Data         576  oled.o(.bss)
    rtc_initpara                             0x200003ec   Data          20  rtc.o(.bss)
    rtc_alarm                                0x20000400   Data          16  rtc.o(.bss)
    gd30_send_array                          0x20000430   Data          12  gd30ad3344.o(.bss)
    gd30_receive_array                       0x2000043c   Data          12  gd30ad3344.o(.bss)
    uart_rx_buffer                           0x20000448   Data         128  usart.o(.bss)
    rs485_rx_buffer                          0x200004c8   Data         128  usart.o(.bss)
    latest_data                              0x20000648   Data          12  adc_app.o(.bss)
    oled2_buffer                             0x20000654   Data         128  oled_app.o(.bss)
    fdst                                     0x200006d4   Data         548  sdcard_app.o(.bss)
    fs                                       0x200008f8   Data         560  sdcard_app.o(.bss)
    filebuffer                               0x20000b28   Data         128  sdcard_app.o(.bss)
    overlimit_file                           0x20000ba8   Data         548  sdcard_app.o(.bss)
    hidedata_file                            0x20000dcc   Data         548  sdcard_app.o(.bss)
    log_file                                 0x20000ff0   Data         548  sdcard_app.o(.bss)
    __initial_sp                             0x20001c98   Data           0  startup_gd32f450_470.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000d68c, Max: 0x00080000, ABSOLUTE, COMPRESSED[0x0000d540])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000d4e0, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO         7518    RESET               startup_gd32f450_470.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         7838  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         8132    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         8135    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         8137    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         8139    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         8140    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         8142    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         8144    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         8133    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c0   0x080001c0   0x00000024   Code   RO         7519    .text               startup_gd32f450_470.o
    0x080001e4   0x080001e4   0x00000024   Code   RO         7841    .text               mc_w.l(memcpya.o)
    0x08000208   0x08000208   0x00000024   Code   RO         7843    .text               mc_w.l(memseta.o)
    0x0800022c   0x0800022c   0x00000024   Code   RO         7845    .text               mc_w.l(strstr.o)
    0x08000250   0x08000250   0x00000018   Code   RO         7847    .text               mc_w.l(strncpy.o)
    0x08000268   0x08000268   0x0000000e   Code   RO         7849    .text               mc_w.l(strlen.o)
    0x08000276   0x08000276   0x0000001c   Code   RO         7851    .text               mc_w.l(strcmp.o)
    0x08000292   0x08000292   0x00000012   Code   RO         7853    .text               mc_w.l(strcpy.o)
    0x080002a4   0x080002a4   0x0000001e   Code   RO         7855    .text               mc_w.l(strncmp.o)
    0x080002c2   0x080002c2   0x0000001a   Code   RO         8126    .text               mc_w.l(atoi.o)
    0x080002dc   0x080002dc   0x00000026   Code   RO         8128    .text               mf_w.l(f2d.o)
    0x08000302   0x08000302   0x00000038   Code   RO         8130    .text               mf_w.l(d2f.o)
    0x0800033a   0x0800033a   0x0000002c   Code   RO         8147    .text               mc_w.l(uidiv.o)
    0x08000366   0x08000366   0x00000062   Code   RO         8149    .text               mc_w.l(uldiv.o)
    0x080003c8   0x080003c8   0x0000009c   Code   RO         8158    .text               mc_w.l(strtod.o)
    0x08000464   0x08000464   0x00000070   Code   RO         8160    .text               mc_w.l(strtol.o)
    0x080004d4   0x080004d4   0x00000000   Code   RO         8162    .text               mc_w.l(iusefp.o)
    0x080004d4   0x080004d4   0x0000006e   Code   RO         8163    .text               mf_w.l(fepilogue.o)
    0x08000542   0x08000542   0x0000014e   Code   RO         8165    .text               mf_w.l(dadd.o)
    0x08000690   0x08000690   0x000000e4   Code   RO         8167    .text               mf_w.l(dmul.o)
    0x08000774   0x08000774   0x000000de   Code   RO         8169    .text               mf_w.l(ddiv.o)
    0x08000852   0x08000852   0x00000030   Code   RO         8171    .text               mf_w.l(dfixul.o)
    0x08000882   0x08000882   0x00000002   PAD
    0x08000884   0x08000884   0x00000030   Code   RO         8173    .text               mf_w.l(cdrcmple.o)
    0x080008b4   0x080008b4   0x00000024   Code   RO         8175    .text               mc_w.l(init.o)
    0x080008d8   0x080008d8   0x0000001e   Code   RO         8177    .text               mc_w.l(llshl.o)
    0x080008f6   0x080008f6   0x00000020   Code   RO         8179    .text               mc_w.l(llushr.o)
    0x08000916   0x08000916   0x00000024   Code   RO         8181    .text               mc_w.l(llsshr.o)
    0x0800093a   0x0800093a   0x00000002   PAD
    0x0800093c   0x0800093c   0x00000008   Code   RO         8185    .text               mc_w.l(ctype_o.o)
    0x08000944   0x08000944   0x00000012   Code   RO         8207    .text               mc_w.l(isspace_o.o)
    0x08000956   0x08000956   0x00000002   PAD
    0x08000958   0x08000958   0x00000360   Code   RO         8213    .text               mc_w.l(scanf_fp.o)
    0x08000cb8   0x08000cb8   0x00000040   Code   RO         8217    .text               mc_w.l(_sgetc.o)
    0x08000cf8   0x08000cf8   0x0000009e   Code   RO         8219    .text               mc_w.l(_strtoul.o)
    0x08000d96   0x08000d96   0x000000ba   Code   RO         8221    .text               mf_w.l(depilogue.o)
    0x08000e50   0x08000e50   0x0000001c   Code   RO         8226    .text               mc_w.l(_chval.o)
    0x08000e6c   0x08000e6c   0x00000018   Code   RO         8228    .text               mf_w.l(dfltul.o)
    0x08000e84   0x08000e84   0x00000056   Code   RO         8238    .text               mc_w.l(__dczerorl2.o)
    0x08000eda   0x08000eda   0x00000002   PAD
    0x08000edc   0x08000edc   0x0000006c   Code   RO          870    i.ADC_Init          adc.o
    0x08000f48   0x08000f48   0x0000011c   Code   RO         1140    i.ADC_Proc          adc_app.o
    0x08001064   0x08001064   0x00000004   Code   RO            3    i.BusFault_Handler  gd32f4xx_it.o
    0x08001068   0x08001068   0x000000ec   Code   RO         1257    i.Cache_Log_To_Flash  flash_app.o
    0x08001154   0x08001154   0x0000001a   Code   RO         1258    i.Calculate_Config_Checksum  flash_app.o
    0x0800116e   0x0800116e   0x0000001a   Code   RO         1259    i.Calculate_Multi_Channel_Config_Checksum  flash_app.o
    0x08001188   0x08001188   0x00000024   Code   RO         1260    i.Check_Cached_Log_In_Flash  flash_app.o
    0x080011ac   0x080011ac   0x00000024   Code   RO         1573    i.Close_OverLimit_File  sdcard_app.o
    0x080011d0   0x080011d0   0x0000003c   Code   RO         1574    i.Close_Sample_File  sdcard_app.o
    0x0800120c   0x0800120c   0x000000e4   Code   RO         1504    i.Configure_RTC_With_DateTime  rtc_app.o
    0x080012f0   0x080012f0   0x000000f0   Code   RO         1803    i.Convert_RTC_To_Unix_Timestamp  usart_app.o
    0x080013e0   0x080013e0   0x0000003c   Code   RO         1804    i.Convert_Uint32_To_Hex_String  usart_app.o
    0x0800141c   0x0800141c   0x00000084   Code   RO         1575    i.Create_Default_Config_INI  sdcard_app.o
    0x080014a0   0x080014a0   0x000000ac   Code   RO         1576    i.Create_HideData_File  sdcard_app.o
    0x0800154c   0x0800154c   0x0000003c   Code   RO         1577    i.Create_Log0_File  sdcard_app.o
    0x08001588   0x08001588   0x00000090   Code   RO         1578    i.Create_Log_File   sdcard_app.o
    0x08001618   0x08001618   0x0000003c   Code   RO         1579    i.Create_Sample_File  sdcard_app.o
    0x08001654   0x08001654   0x00000002   Code   RO            4    i.DebugMon_Handler  gd32f4xx_it.o
    0x08001656   0x08001656   0x00000002   PAD
    0x08001658   0x08001658   0x00000058   Code   RO         1805    i.Encode_Voltage_To_Hex  usart_app.o
    0x080016b0   0x080016b0   0x0000017c   Code   RO         1261    i.Flash_Init        flash_app.o
    0x0800182c   0x0800182c   0x000000c4   Code   RO          922    i.GD30AD3344_AD_Read  gd30ad3344.o
    0x080018f0   0x080018f0   0x00000084   Code   RO          923    i.GD30AD3344_PGA_SET  gd30ad3344.o
    0x08001974   0x08001974   0x000000b4   Code   RO         1806    i.Generate_Encrypted_Output_With_Values  usart_app.o
    0x08001a28   0x08001a28   0x00000050   Code   RO         1807    i.Generate_Encrypted_Output_With_Values_OverLimit  usart_app.o
    0x08001a78   0x08001a78   0x00000074   Code   RO         1580    i.Generate_Filename  sdcard_app.o
    0x08001aec   0x08001aec   0x00000074   Code   RO         1581    i.Generate_HideData_Filename  sdcard_app.o
    0x08001b60   0x08001b60   0x00000038   Code   RO         1582    i.Generate_Log_Filename  sdcard_app.o
    0x08001b98   0x08001b98   0x00000078   Code   RO         1583    i.Generate_OverLimit_Filename  sdcard_app.o
    0x08001c10   0x08001c10   0x0000000c   Code   RO         1144    i.Get_Limit         adc_app.o
    0x08001c1c   0x08001c1c   0x00000078   Code   RO         1584    i.Get_Next_Log_ID_From_SD  sdcard_app.o
    0x08001c94   0x08001c94   0x0000000c   Code   RO         1145    i.Get_Ratio         adc_app.o
    0x08001ca0   0x08001ca0   0x0000000c   Code   RO         1146    i.Get_Sample_Interval  adc_app.o
    0x08001cac   0x08001cac   0x00000088   Code   RO         1809    i.Handle_Command_Get_Limit  usart_app.o
    0x08001d34   0x08001d34   0x00000210   Code   RO         1810    i.Handle_Command_Set_Limit  usart_app.o
    0x08001f44   0x08001f44   0x000001a4   Code   RO         1811    i.Handle_Command_Set_RTC  usart_app.o
    0x080020e8   0x080020e8   0x00000220   Code   RO         1812    i.Handle_Command_Set_Ratio  usart_app.o
    0x08002308   0x08002308   0x00000188   Code   RO         1816    i.Handle_Get_Data_Command  usart_app.o
    0x08002490   0x08002490   0x00000030   Code   RO         1817    i.Handle_Get_Device_ID_Command  usart_app.o
    0x080024c0   0x080024c0   0x000000fc   Code   RO         1818    i.Handle_Get_Ratio_Command  usart_app.o
    0x080025bc   0x080025bc   0x00000040   Code   RO         1390    i.Handle_Key1_Press  key_app.o
    0x080025fc   0x080025fc   0x00000040   Code   RO         1391    i.Handle_Key2_Press  key_app.o
    0x0800263c   0x0800263c   0x00000040   Code   RO         1392    i.Handle_Key3_Press  key_app.o
    0x0800267c   0x0800267c   0x00000044   Code   RO         1393    i.Handle_Key4_Press  key_app.o
    0x080026c0   0x080026c0   0x00000044   Code   RO         1394    i.Handle_Key5_Press  key_app.o
    0x08002704   0x08002704   0x00000044   Code   RO         1395    i.Handle_Key6_Press  key_app.o
    0x08002748   0x08002748   0x00000334   Code   RO         1820    i.Handle_Multi_Config_Read_Command  usart_app.o
    0x08002a7c   0x08002a7c   0x00000220   Code   RO         1821    i.Handle_Multi_Config_Save_Command  usart_app.o
    0x08002c9c   0x08002c9c   0x0000006c   Code   RO         1822    i.Handle_RTC_Now_Command  usart_app.o
    0x08002d08   0x08002d08   0x00000008   Code   RO         1823    i.Handle_Start_Command  usart_app.o
    0x08002d10   0x08002d10   0x00000038   Code   RO         1824    i.Handle_Stop_Command  usart_app.o
    0x08002d48   0x08002d48   0x00000004   Code   RO            5    i.HardFault_Handler  gd32f4xx_it.o
    0x08002d4c   0x08002d4c   0x00000058   Code   RO          393    i.I2C_Start         oled.o
    0x08002da4   0x08002da4   0x00000048   Code   RO          394    i.I2C_Stop          oled.o
    0x08002dec   0x08002dec   0x00000078   Code   RO          395    i.I2C_WaitAck       oled.o
    0x08002e64   0x08002e64   0x00000010   Code   RO          396    i.IIC_delay         oled.o
    0x08002e74   0x08002e74   0x0000012c   Code   RO         1585    i.Init_Data_Recording  sdcard_app.o
    0x08002fa0   0x08002fa0   0x00000020   Code   RO          625    i.Key_Init          key.o
    0x08002fc0   0x08002fc0   0x000000a0   Code   RO         1396    i.Key_Proc          key_app.o
    0x08003060   0x08003060   0x0000006c   Code   RO          626    i.Key_Read          key.o
    0x080030cc   0x080030cc   0x00000038   Code   RO          373    i.Led_Init          led.o
    0x08003104   0x08003104   0x00000010   Code   RO         1262    i.Log_Init          flash_app.o
    0x08003114   0x08003114   0x00000004   Code   RO            6    i.MemManage_Handler  gd32f4xx_it.o
    0x08003118   0x08003118   0x00000002   Code   RO            7    i.NMI_Handler       gd32f4xx_it.o
    0x0800311a   0x0800311a   0x00000002   PAD
    0x0800311c   0x0800311c   0x00000028   Code   RO          330    i.NVIC_SetPriority  systick.o
    0x08003144   0x08003144   0x00000048   Code   RO         1470    i.OLED_App_Init     oled_app.o
    0x0800318c   0x0800318c   0x00000030   Code   RO          397    i.OLED_Clear        oled.o
    0x080031bc   0x080031bc   0x00000060   Code   RO          398    i.OLED_ClearPoint   oled.o
    0x0800321c   0x0800321c   0x0000003c   Code   RO          405    i.OLED_DrawPoint    oled.o
    0x08003258   0x08003258   0x00000148   Code   RO          406    i.OLED_Init         oled.o
    0x080033a0   0x080033a0   0x0000004c   Code   RO          408    i.OLED_Refresh      oled.o
    0x080033ec   0x080033ec   0x000000fc   Code   RO          410    i.OLED_ShowChar     oled.o
    0x080034e8   0x080034e8   0x00000042   Code   RO          414    i.OLED_ShowString   oled.o
    0x0800352a   0x0800352a   0x00000038   Code   RO          416    i.OLED_WR_Byte      oled.o
    0x08003562   0x08003562   0x00000002   Code   RO            8    i.PendSV_Handler    gd32f4xx_it.o
    0x08003564   0x08003564   0x00000360   Code   RO         1827    i.RS485_Task        usart_app.o
    0x080038c4   0x080038c4   0x0000007c   Code   RO          565    i.RTC_Init          rtc.o
    0x08003940   0x08003940   0x00000046   Code   RO         1263    i.Read_Config_From_Flash  flash_app.o
    0x08003986   0x08003986   0x00000078   Code   RO         1264    i.Read_Multi_Channel_Config_From_Flash  flash_app.o
    0x080039fe   0x080039fe   0x00000008   Code   RO            9    i.SDIO_IRQHandler   gd32f4xx_it.o
    0x08003a06   0x08003a06   0x00000002   Code   RO           10    i.SVC_Handler       gd32f4xx_it.o
    0x08003a08   0x08003a08   0x000000a4   Code   RO         1265    i.Save_Config_To_Flash  flash_app.o
    0x08003aac   0x08003aac   0x0000011c   Code   RO         1266    i.Save_Multi_Channel_Config_To_Flash  flash_app.o
    0x08003bc8   0x08003bc8   0x0000005c   Code   RO          417    i.Send_Byte         oled.o
    0x08003c24   0x08003c24   0x00000014   Code   RO         1749    i.Set_ADC_Sample_Interval  timer_app.o
    0x08003c38   0x08003c38   0x00000014   Code   RO         1750    i.Set_ADC_Sampling_State  timer_app.o
    0x08003c4c   0x08003c4c   0x00000030   Code   RO         1751    i.Set_LED1_Blink_Mode  timer_app.o
    0x08003c7c   0x08003c7c   0x00000024   Code   RO         1752    i.Set_LED2_State    timer_app.o
    0x08003ca0   0x08003ca0   0x00000028   Code   RO         1148    i.Set_Limit         adc_app.o
    0x08003cc8   0x08003cc8   0x00000020   Code   RO         1149    i.Set_Over_Limit_State  adc_app.o
    0x08003ce8   0x08003ce8   0x00000028   Code   RO         1150    i.Set_Ratio         adc_app.o
    0x08003d10   0x08003d10   0x00000014   Code   RO         1151    i.Set_Sample_Interval  adc_app.o
    0x08003d24   0x08003d24   0x00000014   Code   RO         1152    i.Set_System_State  adc_app.o
    0x08003d38   0x08003d38   0x00000030   Code   RO         1153    i.Start_Sampling    adc_app.o
    0x08003d68   0x08003d68   0x00000028   Code   RO         1154    i.Stop_Sampling     adc_app.o
    0x08003d90   0x08003d90   0x00000018   Code   RO           11    i.SysTick_Handler   gd32f4xx_it.o
    0x08003da8   0x08003da8   0x000000d4   Code   RO         1997    i.SystemInit        system_gd32f4xx.o
    0x08003e7c   0x08003e7c   0x00000008   Code   RO         1356    i.System_Init       function.o
    0x08003e84   0x08003e84   0x00000020   Code   RO          896    i.TIMER1_IRQHandler  timer.o
    0x08003ea4   0x08003ea4   0x00000458   Code   RO         1753    i.Timer_ADC_Handler  timer_app.o
    0x080042fc   0x080042fc   0x00000058   Code   RO          897    i.Timer_Init        timer.o
    0x08004354   0x08004354   0x0000006c   Code   RO         1754    i.Timer_LED_Handler  timer_app.o
    0x080043c0   0x080043c0   0x000000b0   Code   RO          985    i.USART0_IRQHandler  usart.o
    0x08004470   0x08004470   0x000000b0   Code   RO          986    i.USART1_IRQHandler  usart.o
    0x08004520   0x08004520   0x000000f4   Code   RO         1587    i.Update_Config_INI  sdcard_app.o
    0x08004614   0x08004614   0x00000264   Code   RO         1398    i.Update_Current_Display  key_app.o
    0x08004878   0x08004878   0x0000004c   Code   RO         1155    i.Update_LED_Status  adc_app.o
    0x080048c4   0x080048c4   0x00000004   Code   RO           12    i.UsageFault_Handler  gd32f4xx_it.o
    0x080048c8   0x080048c8   0x00000058   Code   RO         1357    i.UsrFunction       function.o
    0x08004920   0x08004920   0x00000090   Code   RO         1267    i.Verify_Config_Data  flash_app.o
    0x080049b0   0x080049b0   0x0000010c   Code   RO         1268    i.Verify_Multi_Channel_Config_Data  flash_app.o
    0x08004abc   0x08004abc   0x000000c8   Code   RO         1269    i.Write_Cached_Log_To_Log0  flash_app.o
    0x08004b84   0x08004b84   0x000000b8   Code   RO         1589    i.Write_HideData    sdcard_app.o
    0x08004c3c   0x08004c3c   0x0000010c   Code   RO         1590    i.Write_Log_Data    sdcard_app.o
    0x08004d48   0x08004d48   0x00000188   Code   RO         1591    i.Write_OverLimit_Data  sdcard_app.o
    0x08004ed0   0x08004ed0   0x00000210   Code   RO         1593    i.Write_Sample_Data_MultiChannel  sdcard_app.o
    0x080050e0   0x080050e0   0x00000020   Code   RO         8098    i.__0printf         mc_w.l(printfa.o)
    0x08005100   0x08005100   0x00000030   Code   RO         8099    i.__0snprintf       mc_w.l(printfa.o)
    0x08005130   0x08005130   0x0000002c   Code   RO         8103    i.__0vsnprintf      mc_w.l(printfa.o)
    0x0800515c   0x0800515c   0x00000008   Code   RO         8151    i.__aeabi_errno_addr  mc_w.l(errno.o)
    0x08005164   0x08005164   0x00000004   PAD
    0x08005168   0x08005168   0x00000038   Code   RO         7832    i.__hardfp_atof     m_wm.l(atof.o)
    0x080051a0   0x080051a0   0x0000000c   Code   RO         8152    i.__read_errno      mc_w.l(errno.o)
    0x080051ac   0x080051ac   0x0000000e   Code   RO         8232    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080051ba   0x080051ba   0x00000002   Code   RO         8233    i.__scatterload_null  mc_w.l(handlers.o)
    0x080051bc   0x080051bc   0x0000000e   Code   RO         8234    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080051ca   0x080051ca   0x00000002   PAD
    0x080051cc   0x080051cc   0x0000000c   Code   RO         8153    i.__set_errno       mc_w.l(errno.o)
    0x080051d8   0x080051d8   0x00000184   Code   RO         8105    i._fp_digits        mc_w.l(printfa.o)
    0x0800535c   0x0800535c   0x0000000e   Code   RO         8215    i._is_digit         mc_w.l(scanf_fp.o)
    0x0800536a   0x0800536a   0x00000002   PAD
    0x0800536c   0x0800536c   0x000006dc   Code   RO         8106    i._printf_core      mc_w.l(printfa.o)
    0x08005a48   0x08005a48   0x00000024   Code   RO         8107    i._printf_post_padding  mc_w.l(printfa.o)
    0x08005a6c   0x08005a6c   0x0000002e   Code   RO         8108    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08005a9a   0x08005a9a   0x00000016   Code   RO         8109    i._snputc           mc_w.l(printfa.o)
    0x08005ab0   0x08005ab0   0x0000002a   Code   RO         2038    i.adc_calibration_enable  gd32f4xx_adc.o
    0x08005ada   0x08005ada   0x00000052   Code   RO         2040    i.adc_channel_length_config  gd32f4xx_adc.o
    0x08005b2c   0x08005b2c   0x00000024   Code   RO         2041    i.adc_clock_config  gd32f4xx_adc.o
    0x08005b50   0x08005b50   0x00000016   Code   RO         2042    i.adc_data_alignment_config  gd32f4xx_adc.o
    0x08005b66   0x08005b66   0x00000014   Code   RO         2043    i.adc_deinit        gd32f4xx_adc.o
    0x08005b7a   0x08005b7a   0x00000012   Code   RO         2050    i.adc_enable        gd32f4xx_adc.o
    0x08005b8c   0x08005b8c   0x00000010   Code   RO         2067    i.adc_resolution_config  gd32f4xx_adc.o
    0x08005b9c   0x08005b9c   0x000000ac   Code   RO         2068    i.adc_routine_channel_config  gd32f4xx_adc.o
    0x08005c48   0x08005c48   0x00000014   Code   RO         1829    i.bcd_to_decimal    usart_app.o
    0x08005c5c   0x08005c5c   0x00000090   Code   RO         7523    i.check_fs          ff.o
    0x08005cec   0x08005cec   0x00000014   Code   RO         7524    i.chk_chr           ff.o
    0x08005d00   0x08005d00   0x00000394   Code   RO         7525    i.chk_mounted       ff.o
    0x08006094   0x08006094   0x0000001a   Code   RO         7526    i.clust2sect        ff.o
    0x080060ae   0x080060ae   0x00000002   PAD
    0x080060b0   0x080060b0   0x00000030   Code   RO          651    i.cmdsent_error_check  sdcard.o
    0x080060e0   0x080060e0   0x00000090   Code   RO         7527    i.cmp_lfn           ff.o
    0x08006170   0x08006170   0x0000001a   Code   RO         1506    i.convert_to_bcd    rtc_app.o
    0x0800618a   0x0800618a   0x000000ca   Code   RO         7528    i.create_chain      ff.o
    0x08006254   0x08006254   0x0000027c   Code   RO         7529    i.create_name       ff.o
    0x080064d0   0x080064d0   0x00000014   Code   RO          331    i.delay_1ms         systick.o
    0x080064e4   0x080064e4   0x00000018   Code   RO          332    i.delay_decrement   systick.o
    0x080064fc   0x080064fc   0x000000de   Code   RO         7530    i.dir_find          ff.o
    0x080065da   0x080065da   0x00000118   Code   RO         7531    i.dir_next          ff.o
    0x080066f2   0x080066f2   0x0000018c   Code   RO         7533    i.dir_register      ff.o
    0x0800687e   0x0800687e   0x0000009c   Code   RO         7535    i.dir_sdi           ff.o
    0x0800691a   0x0800691a   0x00000086   Code   RO         7784    i.disk_initialize   diskio.o
    0x080069a0   0x080069a0   0x00000006   Code   RO         7785    i.disk_ioctl        diskio.o
    0x080069a6   0x080069a6   0x00000050   Code   RO         7786    i.disk_read         diskio.o
    0x080069f6   0x080069f6   0x0000000c   Code   RO         7787    i.disk_status       diskio.o
    0x08006a02   0x08006a02   0x00000050   Code   RO         7788    i.disk_write        diskio.o
    0x08006a52   0x08006a52   0x00000020   Code   RO         3078    i.dma_channel_disable  gd32f4xx_dma.o
    0x08006a72   0x08006a72   0x00000020   Code   RO         3079    i.dma_channel_enable  gd32f4xx_dma.o
    0x08006a92   0x08006a92   0x00000026   Code   RO         3080    i.dma_channel_subperipheral_select  gd32f4xx_dma.o
    0x08006ab8   0x08006ab8   0x000000a6   Code   RO         3083    i.dma_deinit        gd32f4xx_dma.o
    0x08006b5e   0x08006b5e   0x0000003e   Code   RO         3085    i.dma_flag_clear    gd32f4xx_dma.o
    0x08006b9c   0x08006b9c   0x0000004c   Code   RO         3086    i.dma_flag_get      gd32f4xx_dma.o
    0x08006be8   0x08006be8   0x00000040   Code   RO         3087    i.dma_flow_controller_config  gd32f4xx_dma.o
    0x08006c28   0x08006c28   0x00000164   Code   RO         3096    i.dma_multi_data_mode_init  gd32f4xx_dma.o
    0x08006d8c   0x08006d8c   0x000000b4   Code   RO          652    i.dma_receive_config  sdcard.o
    0x08006e40   0x08006e40   0x00000158   Code   RO         3103    i.dma_single_data_mode_init  gd32f4xx_dma.o
    0x08006f98   0x08006f98   0x000000b4   Code   RO          653    i.dma_transfer_config  sdcard.o
    0x0800704c   0x0800704c   0x00000016   Code   RO         7537    i.f_close           ff.o
    0x08007062   0x08007062   0x000001b0   Code   RO         7539    i.f_lseek           ff.o
    0x08007212   0x08007212   0x00000002   PAD
    0x08007214   0x08007214   0x00000188   Code   RO         7540    i.f_mkdir           ff.o
    0x0800739c   0x0800739c   0x0000002c   Code   RO         7541    i.f_mount           ff.o
    0x080073c8   0x080073c8   0x00000174   Code   RO         7542    i.f_open            ff.o
    0x0800753c   0x0800753c   0x00000078   Code   RO         7543    i.f_opendir         ff.o
    0x080075b4   0x080075b4   0x000000b8   Code   RO         7548    i.f_sync            ff.o
    0x0800766c   0x0800766c   0x0000020e   Code   RO         7552    i.f_write           ff.o
    0x0800787a   0x0800787a   0x00000002   PAD
    0x0800787c   0x0800787c   0x0000004c   Code   RO         1594    i.ff_convert        sdcard_app.o
    0x080078c8   0x080078c8   0x0000002a   Code   RO         1595    i.ff_wtoupper       sdcard_app.o
    0x080078f2   0x080078f2   0x00000002   PAD
    0x080078f4   0x080078f4   0x00000080   Code   RO         7553    i.fit_lfn           ff.o
    0x08007974   0x08007974   0x0000009e   Code   RO         7554    i.follow_path       ff.o
    0x08007a12   0x08007a12   0x00000002   PAD
    0x08007a14   0x08007a14   0x00000024   Code   RO          987    i.fputc             usart.o
    0x08007a38   0x08007a38   0x00000164   Code   RO          924    i.gd30ad3344_init   gd30ad3344.o
    0x08007b9c   0x08007b9c   0x000000ca   Code   RO         7555    i.gen_numname       ff.o
    0x08007c66   0x08007c66   0x00000002   PAD
    0x08007c68   0x08007c68   0x00000024   Code   RO         1830    i.get_days_in_month  usart_app.o
    0x08007c8c   0x08007c8c   0x000000e4   Code   RO         7556    i.get_fat           ff.o
    0x08007d70   0x08007d70   0x00000042   Code   RO         7789    i.get_fattime       diskio.o
    0x08007db2   0x08007db2   0x0000005e   Code   RO         4497    i.gpio_af_set       gd32f4xx_gpio.o
    0x08007e10   0x08007e10   0x00000004   Code   RO         4498    i.gpio_bit_reset    gd32f4xx_gpio.o
    0x08007e14   0x08007e14   0x00000004   Code   RO         4499    i.gpio_bit_set      gd32f4xx_gpio.o
    0x08007e18   0x08007e18   0x00000074   Code   RO          654    i.gpio_config       sdcard.o
    0x08007e8c   0x08007e8c   0x00000010   Code   RO         4503    i.gpio_input_bit_get  gd32f4xx_gpio.o
    0x08007e9c   0x08007e9c   0x0000004e   Code   RO         4505    i.gpio_mode_set     gd32f4xx_gpio.o
    0x08007eea   0x08007eea   0x00000042   Code   RO         4507    i.gpio_output_options_set  gd32f4xx_gpio.o
    0x08007f2c   0x08007f2c   0x00000034   Code   RO         1831    i.is_leap_year      usart_app.o
    0x08007f60   0x08007f60   0x0000000e   Code   RO          300    i.main              main.o
    0x08007f6e   0x08007f6e   0x00000026   Code   RO         7558    i.mem_cmp           ff.o
    0x08007f94   0x08007f94   0x0000001a   Code   RO         7559    i.mem_cpy           ff.o
    0x08007fae   0x08007fae   0x00000014   Code   RO         7560    i.mem_set           ff.o
    0x08007fc2   0x08007fc2   0x00000072   Code   RO         7561    i.move_window       ff.o
    0x08008034   0x08008034   0x00000016   Code   RO         1596    i.nvic_config       sdcard_app.o
    0x0800804a   0x0800804a   0x00000002   PAD
    0x0800804c   0x0800804c   0x000000c4   Code   RO         5070    i.nvic_irq_enable   gd32f4xx_misc.o
    0x08008110   0x08008110   0x00000014   Code   RO         5071    i.nvic_priority_group_set  gd32f4xx_misc.o
    0x08008124   0x08008124   0x00000014   Code   RO         5127    i.pmu_backup_write_enable  gd32f4xx_pmu.o
    0x08008138   0x08008138   0x00000136   Code   RO         7563    i.put_fat           ff.o
    0x0800826e   0x0800826e   0x00000002   PAD
    0x08008270   0x08008270   0x00000084   Code   RO          655    i.r1_error_check    sdcard.o
    0x080082f4   0x080082f4   0x000000ae   Code   RO          656    i.r1_error_type_check  sdcard.o
    0x080083a2   0x080083a2   0x00000002   PAD
    0x080083a4   0x080083a4   0x00000050   Code   RO          657    i.r2_error_check    sdcard.o
    0x080083f4   0x080083f4   0x0000003c   Code   RO          658    i.r3_error_check    sdcard.o
    0x08008430   0x08008430   0x000000a8   Code   RO          659    i.r6_error_check    sdcard.o
    0x080084d8   0x080084d8   0x00000050   Code   RO          660    i.r7_error_check    sdcard.o
    0x08008528   0x08008528   0x00000014   Code   RO         5267    i.rcu_all_reset_flag_clear  gd32f4xx_rcu.o
    0x0800853c   0x0800853c   0x00000124   Code   RO         5275    i.rcu_clock_freq_get  gd32f4xx_rcu.o
    0x08008660   0x08008660   0x00000024   Code   RO          661    i.rcu_config        sdcard.o
    0x08008684   0x08008684   0x00000024   Code   RO         5278    i.rcu_flag_get      gd32f4xx_rcu.o
    0x080086a8   0x080086a8   0x00000024   Code   RO         5291    i.rcu_osci_on       gd32f4xx_rcu.o
    0x080086cc   0x080086cc   0x00000024   Code   RO         5294    i.rcu_periph_clock_enable  gd32f4xx_rcu.o
    0x080086f0   0x080086f0   0x00000024   Code   RO         5297    i.rcu_periph_reset_disable  gd32f4xx_rcu.o
    0x08008714   0x08008714   0x00000024   Code   RO         5298    i.rcu_periph_reset_enable  gd32f4xx_rcu.o
    0x08008738   0x08008738   0x00000018   Code   RO         5303    i.rcu_rtc_clock_config  gd32f4xx_rcu.o
    0x08008750   0x08008750   0x00000068   Code   RO         7564    i.remove_chain      ff.o
    0x080087b8   0x080087b8   0x00000084   Code   RO          988    i.rs485_printf      usart.o
    0x0800883c   0x0800883c   0x00000100   Code   RO          989    i.rs485_usart1_config  usart.o
    0x0800893c   0x0800893c   0x00000064   Code   RO         5575    i.rtc_current_time_get  gd32f4xx_rtc.o
    0x080089a0   0x080089a0   0x000000dc   Code   RO         5580    i.rtc_init          gd32f4xx_rtc.o
    0x08008a7c   0x08008a7c   0x00000048   Code   RO         5581    i.rtc_init_mode_enter  gd32f4xx_rtc.o
    0x08008ac4   0x08008ac4   0x00000014   Code   RO         5582    i.rtc_init_mode_exit  gd32f4xx_rtc.o
    0x08008ad8   0x08008ad8   0x0000005c   Code   RO          566    i.rtc_pre_config    rtc.o
    0x08008b34   0x08008b34   0x00000060   Code   RO         5587    i.rtc_register_sync_wait  gd32f4xx_rtc.o
    0x08008b94   0x08008b94   0x0000005c   Code   RO          568    i.rtc_setup_default  rtc.o
    0x08008bf0   0x08008bf0   0x0000000c   Code   RO         1536    i.scheduler_init    scheduler.o
    0x08008bfc   0x08008bfc   0x00000058   Code   RO         1537    i.scheduler_run     scheduler.o
    0x08008c54   0x08008c54   0x00000240   Code   RO          662    i.sd_block_read     sdcard.o
    0x08008e94   0x08008e94   0x00000338   Code   RO          663    i.sd_block_write    sdcard.o
    0x080091cc   0x080091cc   0x00000094   Code   RO          664    i.sd_bus_mode_config  sdcard.o
    0x08009260   0x08009260   0x000000fc   Code   RO          665    i.sd_bus_width_config  sdcard.o
    0x0800935c   0x0800935c   0x000002c0   Code   RO          667    i.sd_card_information_get  sdcard.o
    0x0800961c   0x0800961c   0x0000011c   Code   RO          668    i.sd_card_init      sdcard.o
    0x08009738   0x08009738   0x00000026   Code   RO          669    i.sd_card_select_deselect  sdcard.o
    0x0800975e   0x0800975e   0x00000002   PAD
    0x08009760   0x08009760   0x000000b8   Code   RO          670    i.sd_card_state_get  sdcard.o
    0x08009818   0x08009818   0x00000048   Code   RO          671    i.sd_cardstatus_get  sdcard.o
    0x08009860   0x08009860   0x00000018   Code   RO          672    i.sd_datablocksize_get  sdcard.o
    0x08009878   0x08009878   0x00000046   Code   RO          674    i.sd_init           sdcard.o
    0x080098be   0x080098be   0x00000002   PAD
    0x080098c0   0x080098c0   0x00000130   Code   RO          675    i.sd_interrupts_process  sdcard.o
    0x080099f0   0x080099f0   0x000002c4   Code   RO          677    i.sd_multiblocks_read  sdcard.o
    0x08009cb4   0x08009cb4   0x000003b0   Code   RO          678    i.sd_multiblocks_write  sdcard.o
    0x0800a064   0x0800a064   0x0000012c   Code   RO          680    i.sd_power_on       sdcard.o
    0x0800a190   0x0800a190   0x00000178   Code   RO          681    i.sd_scr_get        sdcard.o
    0x0800a308   0x0800a308   0x00000018   Code   RO          683    i.sd_transfer_mode_config  sdcard.o
    0x0800a320   0x0800a320   0x00000024   Code   RO          685    i.sd_transfer_stop  sdcard.o
    0x0800a344   0x0800a344   0x0000001c   Code   RO         5828    i.sdio_bus_mode_set  gd32f4xx_sdio.o
    0x0800a360   0x0800a360   0x00000034   Code   RO         5835    i.sdio_clock_config  gd32f4xx_sdio.o
    0x0800a394   0x0800a394   0x00000014   Code   RO         5837    i.sdio_clock_enable  gd32f4xx_sdio.o
    0x0800a3a8   0x0800a3a8   0x0000000c   Code   RO         5838    i.sdio_command_index_get  gd32f4xx_sdio.o
    0x0800a3b4   0x0800a3b4   0x00000038   Code   RO         5839    i.sdio_command_response_config  gd32f4xx_sdio.o
    0x0800a3ec   0x0800a3ec   0x00000014   Code   RO         5841    i.sdio_csm_enable   gd32f4xx_sdio.o
    0x0800a400   0x0800a400   0x0000003c   Code   RO         5842    i.sdio_data_config  gd32f4xx_sdio.o
    0x0800a43c   0x0800a43c   0x0000000c   Code   RO         5844    i.sdio_data_read    gd32f4xx_sdio.o
    0x0800a448   0x0800a448   0x0000001c   Code   RO         5845    i.sdio_data_transfer_config  gd32f4xx_sdio.o
    0x0800a464   0x0800a464   0x0000000c   Code   RO         5846    i.sdio_data_write   gd32f4xx_sdio.o
    0x0800a470   0x0800a470   0x00000014   Code   RO         5847    i.sdio_deinit       gd32f4xx_sdio.o
    0x0800a484   0x0800a484   0x00000014   Code   RO         5848    i.sdio_dma_disable  gd32f4xx_sdio.o
    0x0800a498   0x0800a498   0x00000014   Code   RO         5849    i.sdio_dma_enable   gd32f4xx_sdio.o
    0x0800a4ac   0x0800a4ac   0x00000014   Code   RO         5850    i.sdio_dsm_disable  gd32f4xx_sdio.o
    0x0800a4c0   0x0800a4c0   0x00000014   Code   RO         5851    i.sdio_dsm_enable   gd32f4xx_sdio.o
    0x0800a4d4   0x0800a4d4   0x0000000c   Code   RO         5853    i.sdio_flag_clear   gd32f4xx_sdio.o
    0x0800a4e0   0x0800a4e0   0x00000014   Code   RO         5854    i.sdio_flag_get     gd32f4xx_sdio.o
    0x0800a4f4   0x0800a4f4   0x00000014   Code   RO         5855    i.sdio_hardware_clock_disable  gd32f4xx_sdio.o
    0x0800a508   0x0800a508   0x00000010   Code   RO         5857    i.sdio_interrupt_disable  gd32f4xx_sdio.o
    0x0800a518   0x0800a518   0x00000010   Code   RO         5858    i.sdio_interrupt_enable  gd32f4xx_sdio.o
    0x0800a528   0x0800a528   0x0000000c   Code   RO         5859    i.sdio_interrupt_flag_clear  gd32f4xx_sdio.o
    0x0800a534   0x0800a534   0x00000014   Code   RO         5860    i.sdio_interrupt_flag_get  gd32f4xx_sdio.o
    0x0800a548   0x0800a548   0x0000000c   Code   RO         5863    i.sdio_power_state_get  gd32f4xx_sdio.o
    0x0800a554   0x0800a554   0x0000000c   Code   RO         5864    i.sdio_power_state_set  gd32f4xx_sdio.o
    0x0800a560   0x0800a560   0x0000003c   Code   RO         5868    i.sdio_response_get  gd32f4xx_sdio.o
    0x0800a59c   0x0800a59c   0x0000001c   Code   RO         5873    i.sdio_wait_type_set  gd32f4xx_sdio.o
    0x0800a5b8   0x0800a5b8   0x00000016   Code   RO         6132    i.spi_dma_disable   gd32f4xx_spi.o
    0x0800a5ce   0x0800a5ce   0x00000016   Code   RO         6133    i.spi_dma_enable    gd32f4xx_spi.o
    0x0800a5e4   0x0800a5e4   0x0000000a   Code   RO         6134    i.spi_enable        gd32f4xx_spi.o
    0x0800a5ee   0x0800a5ee   0x00000002   PAD
    0x0800a5f0   0x0800a5f0   0x00000054   Code   RO         1043    i.spi_flash_buffer_read  spi_flash.o
    0x0800a644   0x0800a644   0x000000c4   Code   RO         1044    i.spi_flash_buffer_write  spi_flash.o
    0x0800a708   0x0800a708   0x0000009c   Code   RO         1046    i.spi_flash_init    spi_flash.o
    0x0800a7a4   0x0800a7a4   0x0000005c   Code   RO         1047    i.spi_flash_page_write  spi_flash.o
    0x0800a800   0x0800a800   0x00000054   Code   RO         1049    i.spi_flash_read_id  spi_flash.o
    0x0800a854   0x0800a854   0x00000044   Code   RO         1050    i.spi_flash_sector_erase  spi_flash.o
    0x0800a898   0x0800a898   0x00000038   Code   RO         1051    i.spi_flash_send_byte  spi_flash.o
    0x0800a8d0   0x0800a8d0   0x00000038   Code   RO         1054    i.spi_flash_wait_for_write_end  spi_flash.o
    0x0800a908   0x0800a908   0x00000024   Code   RO         1055    i.spi_flash_write_enable  spi_flash.o
    0x0800a92c   0x0800a92c   0x00000120   Code   RO          926    i.spi_gd30ad3344_send_halfword_dma  gd30ad3344.o
    0x0800aa4c   0x0800aa4c   0x00000008   Code   RO         6136    i.spi_i2s_data_receive  gd32f4xx_spi.o
    0x0800aa54   0x0800aa54   0x00000004   Code   RO         6137    i.spi_i2s_data_transmit  gd32f4xx_spi.o
    0x0800aa58   0x0800aa58   0x00000010   Code   RO         6139    i.spi_i2s_flag_get  gd32f4xx_spi.o
    0x0800aa68   0x0800aa68   0x00000032   Code   RO         6144    i.spi_init          gd32f4xx_spi.o
    0x0800aa9a   0x0800aa9a   0x00000020   Code   RO         7565    i.sum_sfn           ff.o
    0x0800aaba   0x0800aaba   0x000000ca   Code   RO         7566    i.sync              ff.o
    0x0800ab84   0x0800ab84   0x00000108   Code   RO         1998    i.system_clock_240m_25m_hxtal  system_gd32f4xx.o
    0x0800ac8c   0x0800ac8c   0x00000008   Code   RO         1999    i.system_clock_config  system_gd32f4xx.o
    0x0800ac94   0x0800ac94   0x00000050   Code   RO          333    i.systick_config    systick.o
    0x0800ace4   0x0800ace4   0x00000184   Code   RO         6466    i.timer_deinit      gd32f4xx_timer.o
    0x0800ae68   0x0800ae68   0x0000000a   Code   RO         6471    i.timer_enable      gd32f4xx_timer.o
    0x0800ae72   0x0800ae72   0x00000002   PAD
    0x0800ae74   0x0800ae74   0x00000098   Code   RO         6481    i.timer_init        gd32f4xx_timer.o
    0x0800af0c   0x0800af0c   0x00000008   Code   RO         6488    i.timer_interrupt_enable  gd32f4xx_timer.o
    0x0800af14   0x0800af14   0x00000006   Code   RO         6489    i.timer_interrupt_flag_clear  gd32f4xx_timer.o
    0x0800af1a   0x0800af1a   0x00000018   Code   RO         6490    i.timer_interrupt_flag_get  gd32f4xx_timer.o
    0x0800af32   0x0800af32   0x00000002   PAD
    0x0800af34   0x0800af34   0x000000e8   Code   RO         7119    i.usart_baudrate_set  gd32f4xx_usart.o
    0x0800b01c   0x0800b01c   0x0000000a   Code   RO         7123    i.usart_data_receive  gd32f4xx_usart.o
    0x0800b026   0x0800b026   0x00000008   Code   RO         7124    i.usart_data_transmit  gd32f4xx_usart.o
    0x0800b02e   0x0800b02e   0x00000002   PAD
    0x0800b030   0x0800b030   0x000000dc   Code   RO         7125    i.usart_deinit      gd32f4xx_usart.o
    0x0800b10c   0x0800b10c   0x0000000a   Code   RO         7129    i.usart_enable      gd32f4xx_usart.o
    0x0800b116   0x0800b116   0x0000001e   Code   RO         7131    i.usart_flag_get    gd32f4xx_usart.o
    0x0800b134   0x0800b134   0x00000014   Code   RO         7136    i.usart_hardware_flow_cts_config  gd32f4xx_usart.o
    0x0800b148   0x0800b148   0x00000014   Code   RO         7137    i.usart_hardware_flow_rts_config  gd32f4xx_usart.o
    0x0800b15c   0x0800b15c   0x0000001a   Code   RO         7139    i.usart_interrupt_enable  gd32f4xx_usart.o
    0x0800b176   0x0800b176   0x0000001a   Code   RO         7140    i.usart_interrupt_flag_clear  gd32f4xx_usart.o
    0x0800b190   0x0800b190   0x00000038   Code   RO         7141    i.usart_interrupt_flag_get  gd32f4xx_usart.o
    0x0800b1c8   0x0800b1c8   0x00000010   Code   RO         7154    i.usart_parity_config  gd32f4xx_usart.o
    0x0800b1d8   0x0800b1d8   0x00000010   Code   RO         7156    i.usart_receive_config  gd32f4xx_usart.o
    0x0800b1e8   0x0800b1e8   0x00000010   Code   RO         7167    i.usart_stop_bit_set  gd32f4xx_usart.o
    0x0800b1f8   0x0800b1f8   0x00000010   Code   RO         7171    i.usart_transmit_config  gd32f4xx_usart.o
    0x0800b208   0x0800b208   0x00000010   Code   RO         7172    i.usart_word_length_set  gd32f4xx_usart.o
    0x0800b218   0x0800b218   0x0000002a   Code   RO         7567    i.validate          ff.o
    0x0800b242   0x0800b242   0x00001fd8   Data   RO          419    .constdata          oled.o
    0x0800d21a   0x0800d21a   0x00000100   Data   RO         1598    .constdata          sdcard_app.o
    0x0800d31a   0x0800d31a   0x0000000c   Data   RO         1832    .constdata          usart_app.o
    0x0800d326   0x0800d326   0x0000000d   Data   RO         7569    .constdata          ff.o
    0x0800d333   0x0800d333   0x00000081   Data   RO         8186    .constdata          mc_w.l(ctype_o.o)
    0x0800d3b4   0x0800d3b4   0x00000004   Data   RO         8187    .constdata          mc_w.l(ctype_o.o)
    0x0800d3b8   0x0800d3b8   0x000000bc   Data   RO         1599    .conststring        sdcard_app.o
    0x0800d474   0x0800d474   0x0000004a   Data   RO         1755    .conststring        timer_app.o
    0x0800d4be   0x0800d4be   0x00000002   PAD
    0x0800d4c0   0x0800d4c0   0x00000020   Data   RO         8230    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800d4e0, Size: 0x00001c98, Max: 0x00030000, ABSOLUTE, COMPRESSED[0x00000060])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000004   Data   RW          334    .data               systick.o
    0x20000004   COMPRESSED   0x0000000c   Data   RW          573    .data               rtc.o
    0x20000010   COMPRESSED   0x00000024   Data   RW          687    .data               sdcard.o
    0x20000034   COMPRESSED   0x00000002   Data   RW          930    .data               gd30ad3344.o
    0x20000036   COMPRESSED   0x00000002   PAD
    0x20000038   COMPRESSED   0x0000000e   Data   RW          992    .data               usart.o
    0x20000046   COMPRESSED   0x00000002   PAD
    0x20000048   COMPRESSED   0x0000003e   Data   RW         1157    .data               adc_app.o
    0x20000086   COMPRESSED   0x00000002   PAD
    0x20000088   COMPRESSED   0x00000020   Data   RW         1270    .data               flash_app.o
    0x200000a8   COMPRESSED   0x00000010   Data   RW         1358    .data               function.o
    0x200000b8   COMPRESSED   0x00000006   Data   RW         1399    .data               key_app.o
    0x200000be   COMPRESSED   0x00000080   Data   RW         1473    .data               oled_app.o
    0x2000013e   COMPRESSED   0x00000002   PAD
    0x20000140   COMPRESSED   0x00000028   Data   RW         1539    .data               scheduler.o
    0x20000168   COMPRESSED   0x0000000d   Data   RW         1600    .data               sdcard_app.o
    0x20000175   COMPRESSED   0x00000003   PAD
    0x20000178   COMPRESSED   0x00000018   Data   RW         1756    .data               timer_app.o
    0x20000190   COMPRESSED   0x00000005   Data   RW         1833    .data               usart_app.o
    0x20000195   COMPRESSED   0x00000003   PAD
    0x20000198   COMPRESSED   0x00000004   Data   RW         2000    .data               system_gd32f4xx.o
    0x2000019c   COMPRESSED   0x00000006   Data   RW         7570    .data               ff.o
    0x200001a2   COMPRESSED   0x00000002   PAD
    0x200001a4   COMPRESSED   0x00000004   Data   RW         8146    .data               mc_w.l(stdout.o)
    0x200001a8   COMPRESSED   0x00000004   Data   RW         8154    .data               mc_w.l(errno.o)
    0x200001ac        -       0x00000240   Zero   RW          418    .bss                oled.o
    0x200003ec        -       0x00000024   Zero   RW          572    .bss                rtc.o
    0x20000410        -       0x00000020   Zero   RW          686    .bss                sdcard.o
    0x20000430        -       0x00000018   Zero   RW          929    .bss                gd30ad3344.o
    0x20000448        -       0x00000200   Zero   RW          991    .bss                usart.o
    0x20000648        -       0x0000000c   Zero   RW         1156    .bss                adc_app.o
    0x20000654        -       0x00000080   Zero   RW         1472    .bss                oled_app.o
    0x200006d4        -       0x00000bc0   Zero   RW         1597    .bss                sdcard_app.o
    0x20001294        -       0x00000200   Zero   RW         7568    .bss                ff.o
    0x20001494   COMPRESSED   0x00000004   PAD
    0x20001498        -       0x00000800   Zero   RW         7516    STACK               startup_gd32f450_470.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       108          8          0          0          0        539   adc.o
       636        140          0         62         12       8485   adc_app.o
       378          0          0          0          0       4824   diskio.o
      6838         76         13          6        512      32759   ff.o
      1970        248          0         32          0      12846   flash_app.o
        96         14          0         16          0       1754   function.o
       972         88          0          2         24       5814   gd30ad3344.o
       408          8          0          0          0       5527   gd32f4xx_adc.o
      1170          8          0          0          0       7383   gd32f4xx_dma.o
       262          0          0          0          0       4581   gd32f4xx_gpio.o
        56          6          0          0          0      99553   gd32f4xx_it.o
       216         20          0          0          0       1556   gd32f4xx_misc.o
        20          6          0          0          0        582   gd32f4xx_pmu.o
       516         60          0          0          0       6351   gd32f4xx_rcu.o
       508         24          0          0          0       4494   gd32f4xx_rtc.o
       628        136          0          0          0      16518   gd32f4xx_sdio.o
       132          0          0          0          0       5361   gd32f4xx_spi.o
       588         44          0          0          0       4836   gd32f4xx_timer.o
       738         18          0          0          0      11372   gd32f4xx_usart.o
       140         10          0          0          0       1110   key.o
      1168        474          0          6          0       5485   key_app.o
        56          6          0          0          0        503   led.o
        14          0          0          0          0       1719   main.o
      1370         58       8152          0        576       9994   oled.o
        72         24          0        128        128       1276   oled_app.o
       308         48          0         12         36       2561   rtc.o
       254         18          0          0          0       1868   rtc_app.o
       100         16          0         40          0       1912   scheduler.o
      7122        356          0         36         32      32104   sdcard.o
      3248        866        444         13       3008      19194   sdcard_app.o
       828         50          0          0          0       6624   spi_flash.o
        36          8        428          0       2048        888   startup_gd32f450_470.o
       484         32          0          4          0       2999   system_gd32f4xx.o
       164         24          0          4          0      29194   systick.o
       120          0          0          0          0       1113   timer.o
      1344        198         74         24          0       4702   timer_app.o
       776        126          0         14        512       4453   usart.o
      5476       1828         12          5          0      18600   usart_app.o

    ----------------------------------------------------------------------
     39354       <USER>       <GROUP>        420       6892     381434   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        34          0          2         16          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        56         12          0          0          0        132   atof.o
        86          0          0          0          0          0   __dczerorl2.o
        28          0          0          0          0         68   _chval.o
        64          0          0          0          0         84   _sgetc.o
       158          0          0          0          0         92   _strtoul.o
        26          0          0          0          0         80   atoi.o
         8          4        133          0          0         68   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        32         16          0          4          0        204   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        18          0          0          0          0         76   isspace_o.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2372         98          0          0          0        684   printfa.o
       878         12          0          0          0        216   scanf_fp.o
         0          0          0          4          0          0   stdout.o
        28          0          0          0          0         76   strcmp.o
        18          0          0          0          0         68   strcpy.o
        14          0          0          0          0         68   strlen.o
        30          0          0          0          0         80   strncmp.o
        24          0          0          0          0         76   strncpy.o
        36          0          0          0          0         80   strstr.o
       156         12          0          0          0        120   strtod.o
       112          0          0          0          0         88   strtol.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
        24          0          0          0          0         76   dfltul.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      5852        <USER>        <GROUP>          8          0       4036   Library Totals
        16          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

        56         12          0          0          0        132   m_wm.l
      4486        158        133          8          0       2848   mc_w.l
      1294          0          0          0          0       1056   mf_w.l

    ----------------------------------------------------------------------
      5852        <USER>        <GROUP>          8          0       4036   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     45206       5220       9290        428       6892     353822   Grand Totals
     45206       5220       9290         96       6892     353822   ELF Image Totals (compressed)
     45206       5220       9290         96          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                54496 (  53.22kB)
    Total RW  Size (RW Data + ZI Data)              7320 (   7.15kB)
    Total ROM Size (Code + RO Data + RW Data)      54592 (  53.31kB)

==============================================================================

