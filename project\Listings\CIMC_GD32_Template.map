Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    gd32f4xx_it.o(i.SDIO_IRQHandler) refers to sdcard.o(i.sd_interrupts_process) for sd_interrupts_process
    gd32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.delay_decrement) for delay_decrement
    gd32f4xx_it.o(i.SysTick_Handler) refers to function.o(.data) for uwTick
    main.o(i.main) refers to function.o(i.System_Init) for System_Init
    main.o(i.main) refers to function.o(i.UsrFunction) for UsrFunction
    systick.o(i.delay_1ms) refers to systick.o(.data) for delay
    systick.o(i.delay_decrement) refers to systick.o(.data) for delay
    systick.o(i.systick_config) refers to systick.o(i.NVIC_SetPriority) for NVIC_SetPriority
    systick.o(i.systick_config) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    led.o(i.Led_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    led.o(i.Led_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    led.o(i.Led_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    led.o(i.Led_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.I2C_Start) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.I2C_Stop) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.I2C_WaitAck) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.I2C_WaitAck) refers to oled.o(i.I2C_Stop) for I2C_Stop
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ClearPoint) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ColorTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisplayTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    oled.o(i.OLED_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.OLED_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.OLED_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.OLED_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Refresh) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_ShowChinese) for OLED_ShowChinese
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ClearPoint) for OLED_ClearPoint
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for asc2_1206
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_ClearPoint) for OLED_ClearPoint
    oled.o(i.OLED_ShowChinese) refers to oled.o(.data) for Hzk1
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPicture) refers to oled.o(i.OLED_WR_BP) for OLED_WR_BP
    oled.o(i.OLED_ShowPicture) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WR_BP) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_Start) for I2C_Start
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.Send_Byte) for Send_Byte
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_WaitAck) for I2C_WaitAck
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_Stop) for I2C_Stop
    oled.o(i.Send_Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.Send_Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.Send_Byte) refers to oled.o(i.IIC_delay) for IIC_delay
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rtc.o(i.RTC_Init) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    rtc.o(i.RTC_Init) refers to rtc.o(i.rtc_pre_config) for rtc_pre_config
    rtc.o(i.RTC_Init) refers to rtc.o(i.rtc_setup_default) for rtc_setup_default
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear) for rcu_all_reset_flag_clear
    rtc.o(i.RTC_Init) refers to rtc.o(.data) for RTCSRC_FLAG
    rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_rtc_clock_config) for rcu_rtc_clock_config
    rtc.o(i.rtc_pre_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rtc.o(i.rtc_pre_config) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    rtc.o(i.rtc_pre_config) refers to rtc.o(.data) for prescaler_s
    rtc.o(i.rtc_setup) refers to gd32f4xx_usart.o(i.usart_interrupt_disable) for usart_interrupt_disable
    rtc.o(i.rtc_setup) refers to printfa.o(i.__0printf) for __2printf
    rtc.o(i.rtc_setup) refers to rtc.o(i.usart_input_threshold) for usart_input_threshold
    rtc.o(i.rtc_setup) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc.o(i.rtc_setup) refers to rtc.o(i.rtc_show_time) for rtc_show_time
    rtc.o(i.rtc_setup) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    rtc.o(i.rtc_setup) refers to rtc.o(.data) for prescaler_a
    rtc.o(i.rtc_setup) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.rtc_setup_default) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rtc.o(i.rtc_setup_default) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    rtc.o(i.rtc_setup_default) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc.o(i.rtc_setup_default) refers to rtc.o(.data) for prescaler_a
    rtc.o(i.rtc_setup_default) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.rtc_show_alarm) refers to gd32f4xx_rtc.o(i.rtc_alarm_get) for rtc_alarm_get
    rtc.o(i.rtc_show_alarm) refers to printfa.o(i.__0printf) for __2printf
    rtc.o(i.rtc_show_alarm) refers to rtc.o(.bss) for rtc_alarm
    rtc.o(i.rtc_show_time) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    rtc.o(i.rtc_show_time) refers to printfa.o(i.__0printf) for __2printf
    rtc.o(i.rtc_show_time) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.usart_input_threshold) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    rtc.o(i.usart_input_threshold) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    rtc.o(i.usart_input_threshold) refers to printfa.o(i.__0printf) for __2printf
    key.o(i.Key_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    key.o(i.Key_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    key.o(i.Key_Read) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.r1_error_check) refers to sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdcard.o(i.r2_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.r3_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.r7_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.rcu_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_block_read) refers to sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_block_read) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdcard.o(i.sd_block_read) refers to sdcard.o(i.dma_receive_config) for dma_receive_config
    sdcard.o(i.sd_block_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdcard.o(i.sd_block_read) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_block_write) refers to sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_block_write) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdcard.o(i.sd_block_write) refers to sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdcard.o(i.sd_block_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdcard.o(i.sd_block_write) refers to sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdcard.o(i.sd_block_write) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_bus_mode_config) refers to sdcard.o(i.sd_bus_width_config) for sd_bus_width_config
    sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdcard.o(i.sd_bus_mode_config) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_bus_width_config) refers to sdcard.o(i.sd_scr_get) for sd_scr_get
    sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_bus_width_config) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_bus_width_config) refers to sdcard.o(.data) for sd_scr
    sdcard.o(i.sd_card_capacity_get) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_card_capacity_get) refers to sdcard.o(.bss) for sd_csd
    sdcard.o(i.sd_card_information_get) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_card_information_get) refers to sdcard.o(.bss) for sd_cid
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_power_state_get) for sdio_power_state_get
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_card_init) refers to sdcard.o(i.r2_error_check) for r2_error_check
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_card_init) refers to sdcard.o(i.r6_error_check) for r6_error_check
    sdcard.o(i.sd_card_init) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_card_init) refers to sdcard.o(.bss) for sd_cid
    sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_card_select_deselect) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_card_state_get) refers to sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdcard.o(i.sd_card_state_get) refers to sdcard.o(.data) for sd_rca
    sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_cardstatus_get) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_cardstatus_get) refers to sdcard.o(.data) for sd_rca
    sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_erase) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_erase) refers to sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdcard.o(i.sd_erase) refers to sdcard.o(.bss) for sd_csd
    sdcard.o(i.sd_erase) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_init) refers to sdcard.o(i.rcu_config) for rcu_config
    sdcard.o(i.sd_init) refers to sdcard.o(i.gpio_config) for gpio_config
    sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_deinit) for sdio_deinit
    sdcard.o(i.sd_init) refers to sdcard.o(i.sd_power_on) for sd_power_on
    sdcard.o(i.sd_init) refers to sdcard.o(i.sd_card_init) for sd_card_init
    sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_get) for sdio_interrupt_flag_get
    sdcard.o(i.sd_interrupts_process) refers to sdcard.o(i.sd_transfer_stop) for sd_transfer_stop
    sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear) for sdio_interrupt_flag_clear
    sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_disable) for sdio_interrupt_disable
    sdcard.o(i.sd_interrupts_process) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_lock_unlock) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_lock_unlock) refers to sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdcard.o(i.sd_lock_unlock) refers to sdcard.o(.bss) for sd_csd
    sdcard.o(i.sd_lock_unlock) refers to sdcard.o(.data) for sd_rca
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_multiblocks_read) refers to sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_multiblocks_read) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdcard.o(i.sd_multiblocks_read) refers to sdcard.o(i.dma_receive_config) for dma_receive_config
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdcard.o(i.sd_multiblocks_read) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_power_off) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_enable) for sdio_clock_enable
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_power_on) refers to sdcard.o(i.cmdsent_error_check) for cmdsent_error_check
    sdcard.o(i.sd_power_on) refers to sdcard.o(i.r7_error_check) for r7_error_check
    sdcard.o(i.sd_power_on) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_power_on) refers to sdcard.o(i.r3_error_check) for r3_error_check
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_power_on) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_scr_get) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_sdstatus_get) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_sdstatus_get) refers to sdcard.o(.data) for sd_rca
    sdcard.o(i.sd_transfer_mode_config) refers to sdcard.o(.data) for transmode
    sdcard.o(i.sd_transfer_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_transfer_stop) refers to sdcard.o(i.r1_error_check) for r1_error_check
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    adc.o(i.ADC_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    adc.o(i.ADC_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_deinit) for adc_deinit
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_resolution_config) for adc_resolution_config
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_channel_length_config) for adc_channel_length_config
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_routine_channel_config) for adc_routine_channel_config
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    adc.o(i.ADC_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    adc.o(i.ADC_Read) refers to gd32f4xx_adc.o(i.adc_software_trigger_enable) for adc_software_trigger_enable
    adc.o(i.ADC_Read) refers to gd32f4xx_adc.o(i.adc_flag_get) for adc_flag_get
    adc.o(i.ADC_Read) refers to gd32f4xx_adc.o(i.adc_routine_data_read) for adc_routine_data_read
    adc.o(i.ADC_Read) refers to gd32f4xx_adc.o(i.adc_flag_clear) for adc_flag_clear
    timer.o(i.TIMER1_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    timer.o(i.TIMER1_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    timer.o(i.TIMER1_IRQHandler) refers to timer_app.o(i.Timer_LED_Handler) for Timer_LED_Handler
    timer.o(i.TIMER1_IRQHandler) refers to timer_app.o(i.Timer_ADC_Handler) for Timer_ADC_Handler
    timer.o(i.Timer_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    timer.o(i.Timer_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    timer.o(i.Timer_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    timer.o(i.Timer_Init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    timer.o(i.Timer_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    timer.o(i.Timer_Init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    gd30ad3344.o(i.GD30AD3344_AD_Read) refers to gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) for spi_gd30ad3344_send_halfword_dma
    gd30ad3344.o(i.GD30AD3344_AD_Read) refers to gd30ad3344.o(i.GD30AD3344_PGA_SET) for GD30AD3344_PGA_SET
    gd30ad3344.o(i.GD30AD3344_AD_Read) refers to gd30ad3344.o(.data) for GD30AD3344_InitStruct
    gd30ad3344.o(i.gd30ad3344_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd30ad3344.o(i.gd30ad3344_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    gd30ad3344.o(i.gd30ad3344_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd30ad3344.o(i.gd30ad3344_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd30ad3344.o(i.gd30ad3344_init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    gd30ad3344.o(i.gd30ad3344_init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    gd30ad3344.o(i.gd30ad3344_init) refers to gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) for spi_gd30ad3344_send_halfword_dma
    gd30ad3344.o(i.gd30ad3344_init) refers to gd30ad3344.o(.data) for GD30AD3344_InitStruct
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma) refers to gd30ad3344.o(.bss) for gd30_send_array
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma) refers to gd30ad3344.o(.bss) for gd30_send_array
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma) refers to gd30ad3344.o(.bss) for gd30_send_array
    gd30ad3344.o(i.spi_gd30ad3344_wait_for_dma_end) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd30ad3344.o(i.spi_gd30ad3344_wait_for_dma_end) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    usart.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    usart.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    usart.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_clear) for usart_interrupt_flag_clear
    usart.o(i.USART0_IRQHandler) refers to usart.o(.data) for uart_rx_idle_flag
    usart.o(i.USART0_IRQHandler) refers to function.o(.data) for uwTick
    usart.o(i.USART0_IRQHandler) refers to usart.o(.bss) for uart_rx_buffer
    usart.o(i.USART1_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    usart.o(i.USART1_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    usart.o(i.USART1_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_clear) for usart_interrupt_flag_clear
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for rs485_rx_idle_flag
    usart.o(i.USART1_IRQHandler) refers to function.o(.data) for uwTick
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for rs485_rx_buffer
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    usart.o(i.rs485_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    usart.o(i.rs485_printf) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    usart.o(i.rs485_printf) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart.o(i.rs485_printf) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    usart.o(i.rs485_printf) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    usart.o(i.rs485_printf) refers to usart.o(.bss) for buffer
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_parity_config) for usart_parity_config
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_word_length_set) for usart_word_length_set
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_stop_bit_set) for usart_stop_bit_set
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_hardware_flow_cts_config) for usart_hardware_flow_cts_config
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_hardware_flow_rts_config) for usart_hardware_flow_rts_config
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    usart.o(i.rs485_usart1_config) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usart.o(i.rs485_usart1_config) refers to memseta.o(.text) for __aeabi_memclr
    usart.o(i.rs485_usart1_config) refers to usart.o(.data) for rs485_rx_index
    usart.o(i.rs485_usart1_config) refers to usart.o(.bss) for rs485_rx_buffer
    usart.o(i.usart0_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(i.usart0_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.usart0_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usart.o(i.usart0_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_parity_config) for usart_parity_config
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_word_length_set) for usart_word_length_set
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_stop_bit_set) for usart_stop_bit_set
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_hardware_flow_cts_config) for usart_hardware_flow_cts_config
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_hardware_flow_rts_config) for usart_hardware_flow_rts_config
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    usart.o(i.usart0_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    usart.o(i.usart0_config) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usart.o(i.usart0_config) refers to memseta.o(.text) for __aeabi_memclr
    usart.o(i.usart0_config) refers to usart.o(.data) for uart_rx_index
    usart.o(i.usart0_config) refers to usart.o(.bss) for uart_rx_buffer
    spi_flash.o(i.spi_flash_buffer_erase) refers to memseta.o(.text) for __aeabi_memclr4
    spi_flash.o(i.spi_flash_buffer_erase) refers to spi_flash.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    spi_flash.o(i.spi_flash_buffer_erase) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    spi_flash.o(i.spi_flash_buffer_erase) refers to spi_flash.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    spi_flash.o(i.spi_flash_buffer_read) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_buffer_read) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_buffer_read) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_buffer_write) refers to spi_flash.o(i.spi_flash_page_write) for spi_flash_page_write
    spi_flash.o(i.spi_flash_bulk_erase) refers to spi_flash.o(i.spi_flash_write_enable) for spi_flash_write_enable
    spi_flash.o(i.spi_flash_bulk_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_bulk_erase) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_bulk_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_bulk_erase) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    spi_flash.o(i.spi_flash_page_write) refers to spi_flash.o(i.spi_flash_write_enable) for spi_flash_write_enable
    spi_flash.o(i.spi_flash_page_write) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_page_write) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_page_write) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_page_write) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    spi_flash.o(i.spi_flash_read_byte) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_read_id) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_sector_erase) refers to spi_flash.o(i.spi_flash_write_enable) for spi_flash_write_enable
    spi_flash.o(i.spi_flash_sector_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_sector_erase) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_sector_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_sector_erase) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    spi_flash.o(i.spi_flash_send_byte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    spi_flash.o(i.spi_flash_send_byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    spi_flash.o(i.spi_flash_send_byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    spi_flash.o(i.spi_flash_send_halfword) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    spi_flash.o(i.spi_flash_send_halfword) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    spi_flash.o(i.spi_flash_send_halfword) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    spi_flash.o(i.spi_flash_start_read_sequence) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_start_read_sequence) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_wait_for_write_end) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_wait_for_write_end) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_wait_for_write_end) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_write_enable) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    adc_app.o(i.ADC_Proc) refers to gd30ad3344.o(i.GD30AD3344_AD_Read) for GD30AD3344_AD_Read
    adc_app.o(i.ADC_Proc) refers to key_app.o(i.Update_OLED_Display_By_Mode) for Update_OLED_Display_By_Mode
    adc_app.o(i.ADC_Proc) refers to adc_app.o(.data) for read_mode
    adc_app.o(i.Get_ADC_State) refers to adc_app.o(.data) for adc_state
    adc_app.o(i.Get_Latest_Data) refers to adc_app.o(.bss) for latest_data
    adc_app.o(i.Get_Limit) refers to adc_app.o(.data) for current_limit
    adc_app.o(i.Get_Ratio) refers to adc_app.o(.data) for current_ratio
    adc_app.o(i.Get_Sample_Interval) refers to adc_app.o(.data) for sample_interval
    adc_app.o(i.Get_System_State) refers to adc_app.o(.data) for system_state
    adc_app.o(i.Set_Limit) refers to adc_app.o(.data) for current_limit
    adc_app.o(i.Set_Over_Limit_State) refers to timer_app.o(i.Set_LED2_State) for Set_LED2_State
    adc_app.o(i.Set_Over_Limit_State) refers to adc_app.o(.data) for over_limit_state
    adc_app.o(i.Set_Ratio) refers to adc_app.o(.data) for current_ratio
    adc_app.o(i.Set_Sample_Interval) refers to timer_app.o(i.Set_ADC_Sample_Interval) for Set_ADC_Sample_Interval
    adc_app.o(i.Set_Sample_Interval) refers to adc_app.o(.data) for sample_interval
    adc_app.o(i.Set_System_State) refers to adc_app.o(i.Update_LED_Status) for Update_LED_Status
    adc_app.o(i.Set_System_State) refers to adc_app.o(.data) for system_state
    adc_app.o(i.Start_Sampling) refers to timer_app.o(i.Set_ADC_Sampling_State) for Set_ADC_Sampling_State
    adc_app.o(i.Start_Sampling) refers to timer_app.o(i.Set_ADC_Sample_Interval) for Set_ADC_Sample_Interval
    adc_app.o(i.Start_Sampling) refers to sdcard_app.o(i.Create_Sample_File) for Create_Sample_File
    adc_app.o(i.Start_Sampling) refers to adc_app.o(i.Update_LED_Status) for Update_LED_Status
    adc_app.o(i.Start_Sampling) refers to adc_app.o(.data) for adc_state
    adc_app.o(i.Stop_Sampling) refers to timer_app.o(i.Set_ADC_Sampling_State) for Set_ADC_Sampling_State
    adc_app.o(i.Stop_Sampling) refers to sdcard_app.o(i.Close_Sample_File) for Close_Sample_File
    adc_app.o(i.Stop_Sampling) refers to sdcard_app.o(i.Close_OverLimit_File) for Close_OverLimit_File
    adc_app.o(i.Stop_Sampling) refers to adc_app.o(i.Update_LED_Status) for Update_LED_Status
    adc_app.o(i.Stop_Sampling) refers to adc_app.o(.data) for adc_state
    adc_app.o(i.Update_LED_Status) refers to timer_app.o(i.Set_LED1_Blink_Mode) for Set_LED1_Blink_Mode
    adc_app.o(i.Update_LED_Status) refers to timer_app.o(i.Set_LED2_State) for Set_LED2_State
    adc_app.o(i.Update_LED_Status) refers to adc_app.o(.data) for system_state
    flash_app.o(i.Cache_Log_To_Flash) refers to strlen.o(.text) for strlen
    flash_app.o(i.Cache_Log_To_Flash) refers to strstr.o(.text) for strstr
    flash_app.o(i.Cache_Log_To_Flash) refers to memseta.o(.text) for __aeabi_memset
    flash_app.o(i.Cache_Log_To_Flash) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    flash_app.o(i.Cache_Log_To_Flash) refers to memcpya.o(.text) for __aeabi_memcpy
    flash_app.o(i.Cache_Log_To_Flash) refers to spi_flash.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    flash_app.o(i.Cache_Log_To_Flash) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    flash_app.o(i.Cache_Log_To_Flash) refers to spi_flash.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    flash_app.o(i.Check_Cached_Log_In_Flash) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    flash_app.o(i.Flash_Init) refers to usart.o(i.rs485_printf) for rs485_printf
    flash_app.o(i.Flash_Init) refers to spi_flash.o(i.spi_flash_init) for spi_flash_init
    flash_app.o(i.Flash_Init) refers to spi_flash.o(i.spi_flash_read_id) for spi_flash_read_id
    flash_app.o(i.Flash_Init) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    flash_app.o(i.Flash_Init) refers to spi_flash.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    flash_app.o(i.Flash_Init) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    flash_app.o(i.Flash_Init) refers to strlen.o(.text) for strlen
    flash_app.o(i.Flash_Init) refers to spi_flash.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    flash_app.o(i.Flash_Init) refers to flash_app.o(i.Read_Config_From_Flash) for Read_Config_From_Flash
    flash_app.o(i.Flash_Init) refers to adc_app.o(i.Set_Ratio) for Set_Ratio
    flash_app.o(i.Flash_Init) refers to adc_app.o(i.Set_Limit) for Set_Limit
    flash_app.o(i.Flash_Init) refers to adc_app.o(i.Set_Sample_Interval) for Set_Sample_Interval
    flash_app.o(i.Flash_Init) refers to flash_app.o(i.Log_Init) for Log_Init
    flash_app.o(i.Flash_Init) refers to adc_app.o(i.Set_System_State) for Set_System_State
    flash_app.o(i.Flash_Init) refers to flash_app.o(.data) for flash_id
    flash_app.o(i.Flash_Init) refers to key_app.o(.data) for current_sample_period
    flash_app.o(i.Log_Init) refers to flash_app.o(.data) for current_log_id
    flash_app.o(i.Read_Config_From_Flash) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    flash_app.o(i.Read_Config_From_Flash) refers to flash_app.o(i.Verify_Config_Data) for Verify_Config_Data
    flash_app.o(i.Read_Multi_Channel_Config_From_Flash) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    flash_app.o(i.Read_Multi_Channel_Config_From_Flash) refers to flash_app.o(i.Verify_Multi_Channel_Config_Data) for Verify_Multi_Channel_Config_Data
    flash_app.o(i.Save_Config_To_Flash) refers to flash_app.o(i.Calculate_Config_Checksum) for Calculate_Config_Checksum
    flash_app.o(i.Save_Config_To_Flash) refers to spi_flash.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    flash_app.o(i.Save_Config_To_Flash) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    flash_app.o(i.Save_Config_To_Flash) refers to spi_flash.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    flash_app.o(i.Save_Multi_Channel_Config_To_Flash) refers to flash_app.o(i.Calculate_Multi_Channel_Config_Checksum) for Calculate_Multi_Channel_Config_Checksum
    flash_app.o(i.Save_Multi_Channel_Config_To_Flash) refers to spi_flash.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    flash_app.o(i.Save_Multi_Channel_Config_To_Flash) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    flash_app.o(i.Save_Multi_Channel_Config_To_Flash) refers to spi_flash.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    flash_app.o(i.Verify_Config_Data) refers to flash_app.o(i.Calculate_Config_Checksum) for Calculate_Config_Checksum
    flash_app.o(i.Verify_Multi_Channel_Config_Data) refers to flash_app.o(i.Calculate_Multi_Channel_Config_Checksum) for Calculate_Multi_Channel_Config_Checksum
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to flash_app.o(i.Check_Cached_Log_In_Flash) for Check_Cached_Log_In_Flash
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to ff.o(i.f_mount) for f_mount
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to ff.o(i.f_mkdir) for f_mkdir
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to ff.o(i.f_open) for f_open
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to ff.o(i.f_lseek) for f_lseek
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to ff.o(i.f_write) for f_write
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to ff.o(i.f_sync) for f_sync
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to ff.o(i.f_close) for f_close
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to spi_flash.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    flash_app.o(i.Write_Cached_Log_To_Log0) refers to sdcard_app.o(.bss) for fs
    function.o(i.System_Init) refers to systick.o(i.systick_config) for systick_config
    function.o(i.UsrFunction) refers to oled.o(i.OLED_Init) for OLED_Init
    function.o(i.UsrFunction) refers to oled_app.o(i.OLED_App_Init) for OLED_App_Init
    function.o(i.UsrFunction) refers to key.o(i.Key_Init) for Key_Init
    function.o(i.UsrFunction) refers to led.o(i.Led_Init) for Led_Init
    function.o(i.UsrFunction) refers to usart.o(i.rs485_usart1_config) for rs485_usart1_config
    function.o(i.UsrFunction) refers to gd30ad3344.o(i.gd30ad3344_init) for gd30ad3344_init
    function.o(i.UsrFunction) refers to adc.o(i.ADC_Init) for ADC_Init
    function.o(i.UsrFunction) refers to scheduler.o(i.scheduler_init) for scheduler_init
    function.o(i.UsrFunction) refers to rtc.o(i.RTC_Init) for RTC_Init
    function.o(i.UsrFunction) refers to flash_app.o(i.Flash_Init) for Flash_Init
    function.o(i.UsrFunction) refers to sdcard_app.o(i.Init_Data_Recording) for Init_Data_Recording
    function.o(i.UsrFunction) refers to sdcard_app.o(i.Create_Default_Config_INI) for Create_Default_Config_INI
    function.o(i.UsrFunction) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    function.o(i.UsrFunction) refers to systick.o(i.delay_1ms) for delay_1ms
    function.o(i.UsrFunction) refers to timer.o(i.Timer_Init) for Timer_Init
    function.o(i.UsrFunction) refers to scheduler.o(i.scheduler_run) for scheduler_run
    key_app.o(i.Get_OLED_Display_Mode) refers to key_app.o(.data) for current_oled_display_mode
    key_app.o(i.Get_Sample_Period) refers to key_app.o(.data) for current_sample_period
    key_app.o(i.Handle_Key1_Press) refers to key_app.o(i.Set_OLED_Display_Mode) for Set_OLED_Display_Mode
    key_app.o(i.Handle_Key1_Press) refers to key_app.o(i.Update_OLED_Display_By_Mode) for Update_OLED_Display_By_Mode
    key_app.o(i.Handle_Key1_Press) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    key_app.o(i.Handle_Key2_Press) refers to key_app.o(i.Set_OLED_Display_Mode) for Set_OLED_Display_Mode
    key_app.o(i.Handle_Key2_Press) refers to key_app.o(i.Update_OLED_Display_By_Mode) for Update_OLED_Display_By_Mode
    key_app.o(i.Handle_Key2_Press) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    key_app.o(i.Handle_Key3_Press) refers to key_app.o(i.Set_OLED_Display_Mode) for Set_OLED_Display_Mode
    key_app.o(i.Handle_Key3_Press) refers to key_app.o(i.Update_OLED_Display_By_Mode) for Update_OLED_Display_By_Mode
    key_app.o(i.Handle_Key3_Press) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    key_app.o(i.Handle_Key4_Press) refers to key_app.o(i.Set_OLED_Display_Mode) for Set_OLED_Display_Mode
    key_app.o(i.Handle_Key4_Press) refers to key_app.o(i.Update_OLED_Display_By_Mode) for Update_OLED_Display_By_Mode
    key_app.o(i.Handle_Key4_Press) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    key_app.o(i.Handle_Key5_Press) refers to key_app.o(i.Set_OLED_Display_Mode) for Set_OLED_Display_Mode
    key_app.o(i.Handle_Key5_Press) refers to key_app.o(i.Update_OLED_Display_By_Mode) for Update_OLED_Display_By_Mode
    key_app.o(i.Handle_Key5_Press) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    key_app.o(i.Handle_Key6_Press) refers to key_app.o(i.Set_OLED_Display_Mode) for Set_OLED_Display_Mode
    key_app.o(i.Handle_Key6_Press) refers to key_app.o(i.Update_OLED_Display_By_Mode) for Update_OLED_Display_By_Mode
    key_app.o(i.Handle_Key6_Press) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    key_app.o(i.Key_Proc) refers to key.o(i.Key_Read) for Key_Read
    key_app.o(i.Key_Proc) refers to key_app.o(i.Handle_Key1_Press) for Handle_Key1_Press
    key_app.o(i.Key_Proc) refers to key_app.o(i.Handle_Key2_Press) for Handle_Key2_Press
    key_app.o(i.Key_Proc) refers to key_app.o(i.Handle_Key3_Press) for Handle_Key3_Press
    key_app.o(i.Key_Proc) refers to key_app.o(i.Handle_Key4_Press) for Handle_Key4_Press
    key_app.o(i.Key_Proc) refers to key_app.o(i.Handle_Key5_Press) for Handle_Key5_Press
    key_app.o(i.Key_Proc) refers to key_app.o(i.Handle_Key6_Press) for Handle_Key6_Press
    key_app.o(i.Key_Proc) refers to key_app.o(.data) for key_val
    key_app.o(i.Set_OLED_Display_Mode) refers to key_app.o(.data) for current_oled_display_mode
    key_app.o(i.Set_Sample_Period) refers to adc_app.o(i.Set_Sample_Interval) for Set_Sample_Interval
    key_app.o(i.Set_Sample_Period) refers to adc_app.o(i.Get_Ratio) for Get_Ratio
    key_app.o(i.Set_Sample_Period) refers to adc_app.o(i.Get_Limit) for Get_Limit
    key_app.o(i.Set_Sample_Period) refers to flash_app.o(i.Save_Config_To_Flash) for Save_Config_To_Flash
    key_app.o(i.Set_Sample_Period) refers to usart.o(i.rs485_printf) for rs485_printf
    key_app.o(i.Set_Sample_Period) refers to key_app.o(.data) for current_sample_period
    key_app.o(i.Update_OLED_Display_By_Mode) refers to strcpy.o(.text) for strcpy
    key_app.o(i.Update_OLED_Display_By_Mode) refers to f2d.o(.text) for __aeabi_f2d
    key_app.o(i.Update_OLED_Display_By_Mode) refers to printfa.o(i.__0snprintf) for __2snprintf
    key_app.o(i.Update_OLED_Display_By_Mode) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    key_app.o(i.Update_OLED_Display_By_Mode) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    key_app.o(i.Update_OLED_Display_By_Mode) refers to key_app.o(.data) for current_oled_display_mode
    key_app.o(i.Update_OLED_Display_By_Mode) refers to oled_app.o(.data) for oled1_buffer
    key_app.o(i.Update_OLED_Display_By_Mode) refers to adc_app.o(.data) for ch0_data
    key_app.o(i.Update_OLED_Display_By_Mode) refers to oled_app.o(.bss) for oled2_buffer
    oled_app.o(i.OLED_App_Init) refers to strcpy.o(.text) for strcpy
    oled_app.o(i.OLED_App_Init) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled_app.o(i.OLED_App_Init) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled_app.o(i.OLED_App_Init) refers to oled_app.o(.data) for oled1_buffer
    oled_app.o(i.OLED_App_Init) refers to oled_app.o(.bss) for oled2_buffer
    rtc_app.o(i.Configure_RTC_With_DateTime) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rtc_app.o(i.Configure_RTC_With_DateTime) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    rtc_app.o(i.Configure_RTC_With_DateTime) refers to rtc.o(i.rtc_pre_config) for rtc_pre_config
    rtc_app.o(i.Configure_RTC_With_DateTime) refers to rtc_app.o(i.convert_to_bcd) for convert_to_bcd
    rtc_app.o(i.Configure_RTC_With_DateTime) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc_app.o(i.Configure_RTC_With_DateTime) refers to rtc.o(.data) for prescaler_a
    rtc_app.o(i.RTC_Proc) refers to rtc.o(i.rtc_show_time) for rtc_show_time
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for task_num
    scheduler.o(i.scheduler_run) refers to function.o(.data) for uwTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for scheduler_task
    scheduler.o(.data) refers to key_app.o(i.Key_Proc) for Key_Proc
    scheduler.o(.data) refers to usart_app.o(i.RS485_Task) for RS485_Task
    scheduler.o(.data) refers to adc_app.o(i.ADC_Proc) for ADC_Proc
    sdcard_app.o(i.Check_TF_Card) refers to diskio.o(i.disk_initialize) for disk_initialize
    sdcard_app.o(i.Close_HideData_File) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Close_HideData_File) refers to sdcard_app.o(.bss) for hidedata_file
    sdcard_app.o(i.Close_HideData_File) refers to sdcard_app.o(.data) for hidedata_record_count
    sdcard_app.o(i.Close_OverLimit_File) refers to memseta.o(.text) for __aeabi_memclr4
    sdcard_app.o(i.Close_OverLimit_File) refers to sdcard_app.o(.data) for overlimit_record_count
    sdcard_app.o(i.Close_OverLimit_File) refers to sdcard_app.o(.bss) for current_overlimit_filename
    sdcard_app.o(i.Close_Sample_File) refers to memseta.o(.text) for __aeabi_memclr4
    sdcard_app.o(i.Close_Sample_File) refers to sdcard_app.o(.data) for record_state
    sdcard_app.o(i.Close_Sample_File) refers to sdcard_app.o(.bss) for current_filename
    sdcard_app.o(i.Create_Default_Config_INI) refers to memcpya.o(.text) for __aeabi_memcpy4
    sdcard_app.o(i.Create_Default_Config_INI) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Create_Default_Config_INI) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Create_Default_Config_INI) refers to strlen.o(.text) for strlen
    sdcard_app.o(i.Create_Default_Config_INI) refers to ff.o(i.f_write) for f_write
    sdcard_app.o(i.Create_Default_Config_INI) refers to ff.o(i.f_sync) for f_sync
    sdcard_app.o(i.Create_Default_Config_INI) refers to sdcard_app.o(.conststring) for .conststring
    sdcard_app.o(i.Create_HideData_File) refers to sdcard_app.o(i.Generate_HideData_Filename) for Generate_HideData_Filename
    sdcard_app.o(i.Create_HideData_File) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Create_HideData_File) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Create_HideData_File) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Create_HideData_File) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Create_HideData_File) refers to sdcard_app.o(.bss) for hidedata_file
    sdcard_app.o(i.Create_HideData_File) refers to sdcard_app.o(.data) for hidedata_record_count
    sdcard_app.o(i.Create_Log0_File) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Create_Log0_File) refers to ff.o(i.f_sync) for f_sync
    sdcard_app.o(i.Create_Log0_File) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Create_Log_File) refers to ff.o(i.f_mount) for f_mount
    sdcard_app.o(i.Create_Log_File) refers to ff.o(i.f_mkdir) for f_mkdir
    sdcard_app.o(i.Create_Log_File) refers to sdcard_app.o(i.Generate_Log_Filename) for Generate_Log_Filename
    sdcard_app.o(i.Create_Log_File) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Create_Log_File) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Create_Log_File) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Create_Log_File) refers to sdcard_app.o(.bss) for fs
    sdcard_app.o(i.Create_Log_File) refers to flash_app.o(.data) for current_log_id
    sdcard_app.o(i.Create_Sample_File) refers to sdcard_app.o(i.Generate_Filename) for Generate_Filename
    sdcard_app.o(i.Create_Sample_File) refers to sdcard_app.o(.data) for record_state
    sdcard_app.o(i.Create_Sample_File) refers to sdcard_app.o(.bss) for current_filename
    sdcard_app.o(i.Generate_Filename) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Generate_Filename) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Generate_HideData_Filename) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Generate_HideData_Filename) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Generate_Log_Filename) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Generate_Log_Filename) refers to flash_app.o(.data) for current_log_id
    sdcard_app.o(i.Generate_OverLimit_Filename) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Generate_OverLimit_Filename) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Get_Next_Log_ID_From_SD) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Get_Next_Log_ID_From_SD) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Get_Next_Log_ID_From_SD) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Init_Data_Recording) refers to sdcard_app.o(i.nvic_config) for nvic_config
    sdcard_app.o(i.Init_Data_Recording) refers to diskio.o(i.disk_initialize) for disk_initialize
    sdcard_app.o(i.Init_Data_Recording) refers to ff.o(i.f_mount) for f_mount
    sdcard_app.o(i.Init_Data_Recording) refers to ff.o(i.f_mkdir) for f_mkdir
    sdcard_app.o(i.Init_Data_Recording) refers to ff.o(i.f_opendir) for f_opendir
    sdcard_app.o(i.Init_Data_Recording) refers to sdcard_app.o(i.Create_Log0_File) for Create_Log0_File
    sdcard_app.o(i.Init_Data_Recording) refers to flash_app.o(i.Check_Cached_Log_In_Flash) for Check_Cached_Log_In_Flash
    sdcard_app.o(i.Init_Data_Recording) refers to flash_app.o(i.Write_Cached_Log_To_Log0) for Write_Cached_Log_To_Log0
    sdcard_app.o(i.Init_Data_Recording) refers to sdcard_app.o(i.Get_Next_Log_ID_From_SD) for Get_Next_Log_ID_From_SD
    sdcard_app.o(i.Init_Data_Recording) refers to sdcard_app.o(.bss) for fs
    sdcard_app.o(i.Init_Data_Recording) refers to flash_app.o(.data) for current_log_id
    sdcard_app.o(i.Init_Data_Recording) refers to sdcard_app.o(.data) for log_file_created
    sdcard_app.o(i.Is_Log_Folder_Missing) refers to ff.o(i.f_mount) for f_mount
    sdcard_app.o(i.Is_Log_Folder_Missing) refers to ff.o(i.f_opendir) for f_opendir
    sdcard_app.o(i.Is_Log_Folder_Missing) refers to sdcard_app.o(.bss) for fs
    sdcard_app.o(i.Update_Config_INI) refers to f2d.o(.text) for __aeabi_f2d
    sdcard_app.o(i.Update_Config_INI) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Update_Config_INI) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Update_Config_INI) refers to strlen.o(.text) for strlen
    sdcard_app.o(i.Update_Config_INI) refers to ff.o(i.f_write) for f_write
    sdcard_app.o(i.Update_Config_INI) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Update_Config_INI) refers to ff.o(i.f_sync) for f_sync
    sdcard_app.o(i.Update_Config_INI) refers to sdcard_app.o(.conststring) for .conststring
    sdcard_app.o(i.Update_Config_INI_Ratio_Only) refers to adc_app.o(i.Get_Limit) for Get_Limit
    sdcard_app.o(i.Update_Config_INI_Ratio_Only) refers to sdcard_app.o(i.Update_Config_INI) for Update_Config_INI
    sdcard_app.o(i.Write_HideData) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Write_HideData) refers to sdcard_app.o(i.Create_HideData_File) for Create_HideData_File
    sdcard_app.o(i.Write_HideData) refers to f2d.o(.text) for __aeabi_f2d
    sdcard_app.o(i.Write_HideData) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Write_HideData) refers to strlen.o(.text) for strlen
    sdcard_app.o(i.Write_HideData) refers to ff.o(i.f_write) for f_write
    sdcard_app.o(i.Write_HideData) refers to ff.o(i.f_sync) for f_sync
    sdcard_app.o(i.Write_HideData) refers to sdcard_app.o(.data) for hidedata_record_count
    sdcard_app.o(i.Write_HideData) refers to sdcard_app.o(.bss) for hidedata_file
    sdcard_app.o(i.Write_Log_Data) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Write_Log_Data) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Write_Log_Data) refers to sdcard_app.o(i.Create_Log_File) for Create_Log_File
    sdcard_app.o(i.Write_Log_Data) refers to flash_app.o(i.Cache_Log_To_Flash) for Cache_Log_To_Flash
    sdcard_app.o(i.Write_Log_Data) refers to strcmp.o(.text) for strcmp
    sdcard_app.o(i.Write_Log_Data) refers to strncpy.o(.text) for strncpy
    sdcard_app.o(i.Write_Log_Data) refers to strlen.o(.text) for strlen
    sdcard_app.o(i.Write_Log_Data) refers to ff.o(i.f_write) for f_write
    sdcard_app.o(i.Write_Log_Data) refers to ff.o(i.f_sync) for f_sync
    sdcard_app.o(i.Write_Log_Data) refers to sdcard_app.o(.data) for log_file_created
    sdcard_app.o(i.Write_Log_Data) refers to sdcard_app.o(.bss) for filebuffer
    sdcard_app.o(i.Write_OverLimit_Data) refers to sdcard_app.o(i.Generate_OverLimit_Filename) for Generate_OverLimit_Filename
    sdcard_app.o(i.Write_OverLimit_Data) refers to f2d.o(.text) for __aeabi_f2d
    sdcard_app.o(i.Write_OverLimit_Data) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Write_OverLimit_Data) refers to strncpy.o(.text) for strncpy
    sdcard_app.o(i.Write_OverLimit_Data) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Write_OverLimit_Data) refers to ff.o(i.f_lseek) for f_lseek
    sdcard_app.o(i.Write_OverLimit_Data) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Write_OverLimit_Data) refers to strlen.o(.text) for strlen
    sdcard_app.o(i.Write_OverLimit_Data) refers to ff.o(i.f_write) for f_write
    sdcard_app.o(i.Write_OverLimit_Data) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Write_OverLimit_Data) refers to sdcard_app.o(.data) for overlimit_record_count
    sdcard_app.o(i.Write_OverLimit_Data) refers to sdcard_app.o(.bss) for current_overlimit_filename
    sdcard_app.o(i.Write_Sample_Data) refers to sdcard_app.o(i.Generate_Filename) for Generate_Filename
    sdcard_app.o(i.Write_Sample_Data) refers to f2d.o(.text) for __aeabi_f2d
    sdcard_app.o(i.Write_Sample_Data) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Write_Sample_Data) refers to strncpy.o(.text) for strncpy
    sdcard_app.o(i.Write_Sample_Data) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Write_Sample_Data) refers to ff.o(i.f_lseek) for f_lseek
    sdcard_app.o(i.Write_Sample_Data) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Write_Sample_Data) refers to strlen.o(.text) for strlen
    sdcard_app.o(i.Write_Sample_Data) refers to ff.o(i.f_write) for f_write
    sdcard_app.o(i.Write_Sample_Data) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Write_Sample_Data) refers to sdcard_app.o(.data) for record_state
    sdcard_app.o(i.Write_Sample_Data) refers to sdcard_app.o(.bss) for current_filename
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to sdcard_app.o(i.Generate_Filename) for Generate_Filename
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to f2d.o(.text) for __aeabi_f2d
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to printfa.o(i.__0snprintf) for __2snprintf
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to strncpy.o(.text) for strncpy
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to ff.o(i.f_open) for f_open
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to ff.o(i.f_lseek) for f_lseek
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to strlen.o(.text) for strlen
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to ff.o(i.f_write) for f_write
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to ff.o(i.f_sync) for f_sync
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to ff.o(i.f_close) for f_close
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to sdcard_app.o(.data) for record_state
    sdcard_app.o(i.Write_Sample_Data_MultiChannel) refers to sdcard_app.o(.bss) for current_filename
    sdcard_app.o(i.ff_convert) refers to sdcard_app.o(.constdata) for unicode_table
    sdcard_app.o(i.nvic_config) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    sdcard_app.o(i.nvic_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    timer_app.o(i.Set_ADC_Sample_Interval) refers to timer_app.o(.data) for adc_target_count
    timer_app.o(i.Set_ADC_Sampling_State) refers to timer_app.o(.data) for adc_sampling_state
    timer_app.o(i.Set_LED1_Blink_Mode) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    timer_app.o(i.Set_LED1_Blink_Mode) refers to timer_app.o(.data) for led1_blink_mode
    timer_app.o(i.Set_LED2_State) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    timer_app.o(i.Set_LED2_State) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    timer_app.o(i.Timer_ADC_Handler) refers to gd30ad3344.o(i.GD30AD3344_AD_Read) for GD30AD3344_AD_Read
    timer_app.o(i.Timer_ADC_Handler) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    timer_app.o(i.Timer_ADC_Handler) refers to usart_app.o(i.Generate_Encrypted_Output_With_Values_OverLimit) for Generate_Encrypted_Output_With_Values_OverLimit
    timer_app.o(i.Timer_ADC_Handler) refers to f2d.o(.text) for __aeabi_f2d
    timer_app.o(i.Timer_ADC_Handler) refers to usart.o(i.rs485_printf) for rs485_printf
    timer_app.o(i.Timer_ADC_Handler) refers to adc_app.o(i.Set_Over_Limit_State) for Set_Over_Limit_State
    timer_app.o(i.Timer_ADC_Handler) refers to printfa.o(i.__0snprintf) for __2snprintf
    timer_app.o(i.Timer_ADC_Handler) refers to sdcard_app.o(i.Write_OverLimit_Data) for Write_OverLimit_Data
    timer_app.o(i.Timer_ADC_Handler) refers to usart_app.o(i.Generate_Encrypted_Output_With_Values) for Generate_Encrypted_Output_With_Values
    timer_app.o(i.Timer_ADC_Handler) refers to sdcard_app.o(i.Write_Sample_Data_MultiChannel) for Write_Sample_Data_MultiChannel
    timer_app.o(i.Timer_ADC_Handler) refers to timer_app.o(.data) for adc_sampling_state
    timer_app.o(i.Timer_ADC_Handler) refers to adc_app.o(.data) for read_mode
    timer_app.o(i.Timer_ADC_Handler) refers to adc_app.o(.bss) for latest_data
    timer_app.o(i.Timer_ADC_Handler) refers to function.o(.data) for uwTick
    timer_app.o(i.Timer_ADC_Handler) refers to usart_app.o(.data) for current_output_mode
    timer_app.o(i.Timer_ADC_Handler) refers to timer_app.o(.conststring) for .conststring
    timer_app.o(i.Timer_LED_Handler) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    timer_app.o(i.Timer_LED_Handler) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    timer_app.o(i.Timer_LED_Handler) refers to timer_app.o(.data) for led1_blink_mode
    usart_app.o(i.Convert_RTC_To_Unix_Timestamp) refers to usart_app.o(i.bcd_to_decimal) for bcd_to_decimal
    usart_app.o(i.Convert_RTC_To_Unix_Timestamp) refers to usart_app.o(i.is_leap_year) for is_leap_year
    usart_app.o(i.Convert_RTC_To_Unix_Timestamp) refers to usart_app.o(i.get_days_in_month) for get_days_in_month
    usart_app.o(i.Generate_Encrypted_Output_With_Values) refers to usart_app.o(i.Convert_RTC_To_Unix_Timestamp) for Convert_RTC_To_Unix_Timestamp
    usart_app.o(i.Generate_Encrypted_Output_With_Values) refers to usart_app.o(i.Encode_Voltage_To_Hex) for Encode_Voltage_To_Hex
    usart_app.o(i.Generate_Encrypted_Output_With_Values) refers to usart_app.o(i.Convert_Uint32_To_Hex_String) for Convert_Uint32_To_Hex_String
    usart_app.o(i.Generate_Encrypted_Output_With_Values) refers to printfa.o(i.__0snprintf) for __2snprintf
    usart_app.o(i.Generate_Encrypted_Output_With_Values) refers to printfa.o(i.__0printf) for __2printf
    usart_app.o(i.Generate_Encrypted_Output_With_Values) refers to sdcard_app.o(i.Write_HideData) for Write_HideData
    usart_app.o(i.Generate_Encrypted_Output_With_Values_OverLimit) refers to usart_app.o(i.Convert_RTC_To_Unix_Timestamp) for Convert_RTC_To_Unix_Timestamp
    usart_app.o(i.Generate_Encrypted_Output_With_Values_OverLimit) refers to usart_app.o(i.Encode_Voltage_To_Hex) for Encode_Voltage_To_Hex
    usart_app.o(i.Generate_Encrypted_Output_With_Values_OverLimit) refers to usart_app.o(i.Convert_Uint32_To_Hex_String) for Convert_Uint32_To_Hex_String
    usart_app.o(i.Generate_Encrypted_Output_With_Values_OverLimit) refers to printfa.o(i.__0printf) for __2printf
    usart_app.o(i.Handle_Command_Get_Limit) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Command_Get_Limit) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Command_Get_Limit) refers to adc_app.o(.data) for ch2_limit
    usart_app.o(i.Handle_Command_Set_Limit) refers to strstr.o(.text) for strstr
    usart_app.o(i.Handle_Command_Set_Limit) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    usart_app.o(i.Handle_Command_Set_Limit) refers to d2f.o(.text) for __aeabi_d2f
    usart_app.o(i.Handle_Command_Set_Limit) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Command_Set_Limit) refers to adc_app.o(i.Set_System_State) for Set_System_State
    usart_app.o(i.Handle_Command_Set_Limit) refers to sdcard_app.o(i.Update_Config_INI) for Update_Config_INI
    usart_app.o(i.Handle_Command_Set_Limit) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Command_Set_Limit) refers to printfa.o(i.__0snprintf) for __2snprintf
    usart_app.o(i.Handle_Command_Set_Limit) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Command_Set_Limit) refers to adc_app.o(.data) for ch0_limit
    usart_app.o(i.Handle_Command_Set_RTC) refers to strncpy.o(.text) for strncpy
    usart_app.o(i.Handle_Command_Set_RTC) refers to strcpy.o(.text) for strcpy
    usart_app.o(i.Handle_Command_Set_RTC) refers to atoi.o(.text) for atoi
    usart_app.o(i.Handle_Command_Set_RTC) refers to rtc_app.o(i.Configure_RTC_With_DateTime) for Configure_RTC_With_DateTime
    usart_app.o(i.Handle_Command_Set_RTC) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Command_Set_RTC) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    usart_app.o(i.Handle_Command_Set_RTC) refers to printfa.o(i.__0snprintf) for __2snprintf
    usart_app.o(i.Handle_Command_Set_RTC) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Command_Set_Ratio) refers to strstr.o(.text) for strstr
    usart_app.o(i.Handle_Command_Set_Ratio) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    usart_app.o(i.Handle_Command_Set_Ratio) refers to d2f.o(.text) for __aeabi_d2f
    usart_app.o(i.Handle_Command_Set_Ratio) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Command_Set_Ratio) refers to adc_app.o(i.Set_System_State) for Set_System_State
    usart_app.o(i.Handle_Command_Set_Ratio) refers to sdcard_app.o(i.Update_Config_INI) for Update_Config_INI
    usart_app.o(i.Handle_Command_Set_Ratio) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Command_Set_Ratio) refers to printfa.o(i.__0snprintf) for __2snprintf
    usart_app.o(i.Handle_Command_Set_Ratio) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Command_Set_Ratio) refers to adc_app.o(.data) for ch0_ratio
    usart_app.o(i.Handle_Conf_Command) refers to ff.o(i.f_open) for f_open
    usart_app.o(i.Handle_Conf_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Conf_Command) refers to ff.o(i.f_read) for f_read
    usart_app.o(i.Handle_Conf_Command) refers to ff.o(i.f_close) for f_close
    usart_app.o(i.Handle_Conf_Command) refers to strtok.o(.text) for strtok
    usart_app.o(i.Handle_Conf_Command) refers to strlen.o(.text) for strlen
    usart_app.o(i.Handle_Conf_Command) refers to strstr.o(.text) for strstr
    usart_app.o(i.Handle_Conf_Command) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    usart_app.o(i.Handle_Conf_Command) refers to d2f.o(.text) for __aeabi_d2f
    usart_app.o(i.Handle_Conf_Command) refers to adc_app.o(i.Set_Ratio) for Set_Ratio
    usart_app.o(i.Handle_Conf_Command) refers to adc_app.o(i.Set_Limit) for Set_Limit
    usart_app.o(i.Handle_Conf_Command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Config_Read_Command) refers to adc_app.o(i.Set_System_State) for Set_System_State
    usart_app.o(i.Handle_Config_Read_Command) refers to flash_app.o(i.Read_Config_From_Flash) for Read_Config_From_Flash
    usart_app.o(i.Handle_Config_Read_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Config_Read_Command) refers to adc_app.o(i.Set_Ratio) for Set_Ratio
    usart_app.o(i.Handle_Config_Read_Command) refers to adc_app.o(i.Set_Limit) for Set_Limit
    usart_app.o(i.Handle_Config_Read_Command) refers to adc_app.o(i.Set_Sample_Interval) for Set_Sample_Interval
    usart_app.o(i.Handle_Config_Read_Command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Config_Read_Command) refers to adc_app.o(i.Get_Ratio) for Get_Ratio
    usart_app.o(i.Handle_Config_Read_Command) refers to adc_app.o(i.Get_Limit) for Get_Limit
    usart_app.o(i.Handle_Config_Save_Command) refers to adc_app.o(i.Set_System_State) for Set_System_State
    usart_app.o(i.Handle_Config_Save_Command) refers to adc_app.o(i.Get_Ratio) for Get_Ratio
    usart_app.o(i.Handle_Config_Save_Command) refers to adc_app.o(i.Get_Limit) for Get_Limit
    usart_app.o(i.Handle_Config_Save_Command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Config_Save_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Config_Save_Command) refers to adc_app.o(i.Get_Sample_Interval) for Get_Sample_Interval
    usart_app.o(i.Handle_Config_Save_Command) refers to flash_app.o(i.Save_Config_To_Flash) for Save_Config_To_Flash
    usart_app.o(i.Handle_Get_Data_Command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Get_Data_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Get_Data_Command) refers to adc_app.o(.data) for read_mode
    usart_app.o(i.Handle_Get_Device_ID_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Get_Ratio_Command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Get_Ratio_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Get_Ratio_Command) refers to printfa.o(i.__0snprintf) for __2snprintf
    usart_app.o(i.Handle_Get_Ratio_Command) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Get_Ratio_Command) refers to adc_app.o(.data) for ch2_ratio
    usart_app.o(i.Handle_Hide_Command) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Hide_Command) refers to usart_app.o(.data) for current_output_mode
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to adc_app.o(i.Set_System_State) for Set_System_State
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to flash_app.o(i.Read_Multi_Channel_Config_From_Flash) for Read_Multi_Channel_Config_From_Flash
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to flash_app.o(i.Read_Config_From_Flash) for Read_Config_From_Flash
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to adc_app.o(i.Set_Sample_Interval) for Set_Sample_Interval
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to adc_app.o(i.Get_Sample_Interval) for Get_Sample_Interval
    usart_app.o(i.Handle_Multi_Config_Read_Command) refers to adc_app.o(.data) for ch0_ratio
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to adc_app.o(i.Set_System_State) for Set_System_State
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to adc_app.o(i.Get_Sample_Interval) for Get_Sample_Interval
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to f2d.o(.text) for __aeabi_f2d
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to flash_app.o(i.Save_Multi_Channel_Config_To_Flash) for Save_Multi_Channel_Config_To_Flash
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to adc_app.o(i.Get_Limit) for Get_Limit
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to adc_app.o(i.Get_Ratio) for Get_Ratio
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to flash_app.o(i.Save_Config_To_Flash) for Save_Config_To_Flash
    usart_app.o(i.Handle_Multi_Config_Save_Command) refers to adc_app.o(.data) for ch0_limit
    usart_app.o(i.Handle_RTC_Now_Command) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    usart_app.o(i.Handle_RTC_Now_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Start_Command) refers to adc_app.o(i.Start_Sampling) for Start_Sampling
    usart_app.o(i.Handle_Stop_Command) refers to adc_app.o(i.Stop_Sampling) for Stop_Sampling
    usart_app.o(i.Handle_Stop_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Stop_Command) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Stop_Command) refers to memseta.o(.text) for __aeabi_memclr
    usart_app.o(i.Handle_Stop_Command) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    usart_app.o(i.Handle_Stop_Command) refers to oled_app.o(.bss) for oled2_buffer
    usart_app.o(i.Handle_Test_Command) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Test_Command) refers to usart.o(i.rs485_printf) for rs485_printf
    usart_app.o(i.Handle_Test_Command) refers to sdcard_app.o(i.Check_TF_Card) for Check_TF_Card
    usart_app.o(i.Handle_Test_Command) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    usart_app.o(i.Handle_Test_Command) refers to printfa.o(i.__0snprintf) for __2snprintf
    usart_app.o(i.Handle_Test_Command) refers to flash_app.o(i.Cache_Log_To_Flash) for Cache_Log_To_Flash
    usart_app.o(i.Handle_Test_Command) refers to sdcard.o(i.sd_card_capacity_get) for sd_card_capacity_get
    usart_app.o(i.Handle_Test_Command) refers to flash_app.o(.data) for flash_id
    usart_app.o(i.Handle_Unhide_Command) refers to sdcard_app.o(i.Write_Log_Data) for Write_Log_Data
    usart_app.o(i.Handle_Unhide_Command) refers to sdcard_app.o(i.Close_HideData_File) for Close_HideData_File
    usart_app.o(i.Handle_Unhide_Command) refers to usart_app.o(.data) for current_output_mode
    usart_app.o(i.RS485_Task) refers to strncmp.o(.text) for strncmp
    usart_app.o(i.RS485_Task) refers to strcmp.o(.text) for strcmp
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Get_Device_ID_Command) for Handle_Get_Device_ID_Command
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Command_Set_RTC) for Handle_Command_Set_RTC
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_RTC_Now_Command) for Handle_RTC_Now_Command
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Command_Set_Ratio) for Handle_Command_Set_Ratio
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Get_Ratio_Command) for Handle_Get_Ratio_Command
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Get_Data_Command) for Handle_Get_Data_Command
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Command_Set_Limit) for Handle_Command_Set_Limit
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Command_Get_Limit) for Handle_Command_Get_Limit
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Start_Command) for Handle_Start_Command
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Stop_Command) for Handle_Stop_Command
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Multi_Config_Save_Command) for Handle_Multi_Config_Save_Command
    usart_app.o(i.RS485_Task) refers to usart_app.o(i.Handle_Multi_Config_Read_Command) for Handle_Multi_Config_Read_Command
    usart_app.o(i.RS485_Task) refers to memseta.o(.text) for __aeabi_memclr
    usart_app.o(i.RS485_Task) refers to usart.o(.data) for rs485_rx_index
    usart_app.o(i.RS485_Task) refers to function.o(.data) for uwTick
    usart_app.o(i.RS485_Task) refers to usart.o(.bss) for rs485_rx_buffer
    usart_app.o(i.RS485_Task) refers to usart_app.o(.data) for ratio_config_state
    usart_app.o(i.get_days_in_month) refers to usart_app.o(i.is_leap_year) for is_leap_year
    usart_app.o(i.get_days_in_month) refers to usart_app.o(.constdata) for days_in_month
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_240m_25m_hxtal) for system_clock_240m_25m_hxtal
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to timer.o(i.TIMER1_IRQHandler) for TIMER1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to usart.o(i.USART0_IRQHandler) for USART0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SDIO_IRQHandler) for SDIO_IRQHandler
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    ff.o(i.check_fs) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_status) for disk_status
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.chk_mounted) refers to ff.o(i.check_fs) for check_fs
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.chk_mounted) refers to ff.o(.data) for FatFs
    ff.o(i.cmp_lfn) refers to sdcard_app.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.cmp_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.create_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.create_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.create_name) refers to sdcard_app.o(i.ff_convert) for ff_convert
    ff.o(i.create_name) refers to ff.o(i.chk_chr) for chk_chr
    ff.o(i.create_name) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.create_name) refers to sdcard_app.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.dir_find) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_find) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_find) refers to ff.o(i.cmp_lfn) for cmp_lfn
    ff.o(i.dir_find) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_find) refers to ff.o(i.mem_cmp) for mem_cmp
    ff.o(i.dir_find) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_next) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_next) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.dir_next) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_next) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_next) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.dir_read) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_read) refers to ff.o(i.pick_lfn) for pick_lfn
    ff.o(i.dir_read) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_read) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.dir_register) refers to ff.o(i.gen_numname) for gen_numname
    ff.o(i.dir_register) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.dir_register) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_register) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_register) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_register) refers to ff.o(i.fit_lfn) for fit_lfn
    ff.o(i.dir_register) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_remove) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_remove) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_remove) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_sdi) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_sdi) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_chmod) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_chmod) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_chmod) refers to ff.o(i.sync) for sync
    ff.o(i.f_chmod) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_close) refers to ff.o(i.f_sync) for f_sync
    ff.o(i.f_getfree) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_getfree) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_getfree) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_lseek) refers to ff.o(i.validate) for validate
    ff.o(i.f_lseek) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_lseek) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_lseek) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_lseek) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_lseek) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_mkdir) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkdir) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_mkdir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_mkdir) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_mkdir) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_mkdir) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkdir) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkdir) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_mkdir) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_mkdir) refers to ff.o(i.sync) for sync
    ff.o(i.f_mkdir) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_mount) refers to ff.o(.data) for FatFs
    ff.o(i.f_open) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_open) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_open) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_open) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_open) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_open) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_open) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_opendir) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_opendir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_opendir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_opendir) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_read) refers to ff.o(i.validate) for validate
    ff.o(i.f_read) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_read) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_read) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_read) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_read) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_readdir) refers to ff.o(i.validate) for validate
    ff.o(i.f_readdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_readdir) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_readdir) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_readdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.f_readdir) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_rename) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_rename) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_rename) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_rename) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_rename) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_rename) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_rename) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_rename) refers to ff.o(i.sync) for sync
    ff.o(i.f_rename) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_stat) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_stat) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_stat) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_stat) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_sync) refers to ff.o(i.validate) for validate
    ff.o(i.f_sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_sync) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_sync) refers to ff.o(i.sync) for sync
    ff.o(i.f_truncate) refers to ff.o(i.validate) for validate
    ff.o(i.f_truncate) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_truncate) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_truncate) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.f_unlink) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_unlink) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_unlink) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_unlink) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_unlink) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_unlink) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_unlink) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_unlink) refers to ff.o(i.sync) for sync
    ff.o(i.f_unlink) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_utime) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_utime) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_utime) refers to ff.o(i.sync) for sync
    ff.o(i.f_utime) refers to ff.o(.bss) for LfnBuf
    ff.o(i.f_write) refers to ff.o(i.validate) for validate
    ff.o(i.f_write) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_write) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_write) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_write) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_write) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.fit_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.follow_path) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.follow_path) refers to ff.o(i.create_name) for create_name
    ff.o(i.follow_path) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.gen_numname) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.get_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.get_fileinfo) refers to sdcard_app.o(i.ff_convert) for ff_convert
    ff.o(i.move_window) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.move_window) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.pick_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.put_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.remove_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.remove_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.sync) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.sync) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.validate) refers to diskio.o(i.disk_status) for disk_status
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_init) for sd_init
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_card_information_get) for sd_card_information_get
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_card_select_deselect) for sd_card_select_deselect
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_cardstatus_get) for sd_cardstatus_get
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_bus_mode_config) for sd_bus_mode_config
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_transfer_mode_config) for sd_transfer_mode_config
    diskio.o(i.disk_read) refers to sdcard.o(i.sd_block_read) for sd_block_read
    diskio.o(i.disk_read) refers to sdcard.o(i.sd_multiblocks_read) for sd_multiblocks_read
    diskio.o(i.disk_write) refers to sdcard.o(i.sd_block_write) for sd_block_write
    diskio.o(i.disk_write) refers to sdcard.o(i.sd_multiblocks_write) for sd_multiblocks_write
    diskio.o(i.get_fattime) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    atof.o(i.__hardfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__hardfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.__softfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__softfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers to errno.o(i.__set_errno) for __set_errno
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    strtok.o(.text) refers to strtok.o(.data) for .data
    strtok_r.o(.text) refers to strtok_r.o(.data) for .data
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtod.o(.text) refers to scanf_fp.o(.text) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace_o.o(.text) for isspace
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(i.OLED_ColorTurn), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_Off), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_On), (28 bytes).
    Removing oled.o(i.OLED_DisplayTurn), (44 bytes).
    Removing oled.o(i.OLED_DrawCircle), (152 bytes).
    Removing oled.o(i.OLED_DrawLine), (146 bytes).
    Removing oled.o(i.OLED_Pow), (22 bytes).
    Removing oled.o(i.OLED_ScrollDisplay), (164 bytes).
    Removing oled.o(i.OLED_ShowChinese), (264 bytes).
    Removing oled.o(i.OLED_ShowNum), (116 bytes).
    Removing oled.o(i.OLED_ShowPicture), (78 bytes).
    Removing oled.o(i.OLED_WR_BP), (46 bytes).
    Removing oled.o(.data), (5688 bytes).
    Removing rtc.o(.rev16_text), (4 bytes).
    Removing rtc.o(.revsh_text), (4 bytes).
    Removing rtc.o(i.rtc_setup), (668 bytes).
    Removing rtc.o(i.rtc_show_alarm), (64 bytes).
    Removing rtc.o(i.rtc_show_time), (88 bytes).
    Removing rtc.o(i.usart_input_threshold), (252 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing sdcard.o(.rev16_text), (4 bytes).
    Removing sdcard.o(.revsh_text), (4 bytes).
    Removing sdcard.o(i.sd_card_capacity_get), (168 bytes).
    Removing sdcard.o(i.sd_erase), (324 bytes).
    Removing sdcard.o(i.sd_lock_unlock), (488 bytes).
    Removing sdcard.o(i.sd_power_off), (14 bytes).
    Removing sdcard.o(i.sd_sdstatus_get), (428 bytes).
    Removing sdcard.o(i.sd_transfer_state_get), (20 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(i.ADC_Read), (48 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing gd30ad3344.o(.rev16_text), (4 bytes).
    Removing gd30ad3344.o(.revsh_text), (4 bytes).
    Removing gd30ad3344.o(i.spi_gd30ad3344_send_byte_dma), (252 bytes).
    Removing gd30ad3344.o(i.spi_gd30ad3344_transmit_receive_dma), (296 bytes).
    Removing gd30ad3344.o(i.spi_gd30ad3344_wait_for_dma_end), (44 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(i.usart0_config), (228 bytes).
    Removing spi_flash.o(.rev16_text), (4 bytes).
    Removing spi_flash.o(.revsh_text), (4 bytes).
    Removing spi_flash.o(i.spi_flash_buffer_erase), (332 bytes).
    Removing spi_flash.o(i.spi_flash_bulk_erase), (44 bytes).
    Removing spi_flash.o(i.spi_flash_read_byte), (10 bytes).
    Removing spi_flash.o(i.spi_flash_send_halfword), (52 bytes).
    Removing spi_flash.o(i.spi_flash_start_read_sequence), (48 bytes).
    Removing adc_app.o(.rev16_text), (4 bytes).
    Removing adc_app.o(.revsh_text), (4 bytes).
    Removing adc_app.o(i.ADC_To_Voltage), (48 bytes).
    Removing adc_app.o(i.Get_ADC_State), (12 bytes).
    Removing adc_app.o(i.Get_Latest_Data), (16 bytes).
    Removing adc_app.o(i.Get_System_State), (12 bytes).
    Removing flash_app.o(.rev16_text), (4 bytes).
    Removing flash_app.o(.revsh_text), (4 bytes).
    Removing function.o(.rev16_text), (4 bytes).
    Removing function.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(i.Get_OLED_Display_Mode), (12 bytes).
    Removing key_app.o(i.Get_Sample_Period), (12 bytes).
    Removing key_app.o(i.Set_Sample_Period), (104 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(i.Oled_Task), (2 bytes).
    Removing rtc_app.o(.rev16_text), (4 bytes).
    Removing rtc_app.o(.revsh_text), (4 bytes).
    Removing rtc_app.o(i.RTC_Proc), (8 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(i.test_task), (2 bytes).
    Removing sdcard_app.o(.rev16_text), (4 bytes).
    Removing sdcard_app.o(.revsh_text), (4 bytes).
    Removing sdcard_app.o(i.Check_TF_Card), (36 bytes).
    Removing sdcard_app.o(i.Close_HideData_File), (24 bytes).
    Removing sdcard_app.o(i.Is_Log_Folder_Missing), (60 bytes).
    Removing sdcard_app.o(i.Update_Config_INI_Ratio_Only), (60 bytes).
    Removing sdcard_app.o(i.Write_Sample_Data), (392 bytes).
    Removing timer_app.o(.rev16_text), (4 bytes).
    Removing timer_app.o(.revsh_text), (4 bytes).
    Removing usart_app.o(.rev16_text), (4 bytes).
    Removing usart_app.o(.revsh_text), (4 bytes).
    Removing usart_app.o(i.Handle_Conf_Command), (464 bytes).
    Removing usart_app.o(i.Handle_Config_Read_Command), (260 bytes).
    Removing usart_app.o(i.Handle_Config_Save_Command), (204 bytes).
    Removing usart_app.o(i.Handle_Hide_Command), (32 bytes).
    Removing usart_app.o(i.Handle_Test_Command), (524 bytes).
    Removing usart_app.o(i.Handle_Unhide_Command), (36 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (272 bytes).
    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (96 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (34 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_config), (52 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_source_config), (48 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (124 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (46 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (58 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_data_read), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_software_trigger_enable), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_special_function_config), (90 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_mode_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_routine_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (50 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_sequence_channel_enable), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (56 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (52 bytes).
    Removing gd32f4xx_can.o(i.can_error_get), (12 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (32 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (272 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (30 bytes).
    Removing gd32f4xx_can.o(i.can_init), (290 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_get), (116 bytes).
    Removing gd32f4xx_can.o(i.can_message_receive), (228 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (336 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_receive_message_length_get), (26 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (164 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (80 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (10 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (124 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (48 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (168 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (36 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (16 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (48 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (12 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_deinit), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (38 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (22 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_source_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_mode_config), (40 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (20 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (20 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (36 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (52 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_get), (516 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_config), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (40 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (126 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_para_struct_init), (34 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (66 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_get), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (28 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (108 bytes).
    Removing gd32f4xx_enet.o(i.enet_default_init), (148 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_delay), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_chain_init), (200 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_enable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_receive), (248 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_transmit), (204 bytes).
    Removing gd32f4xx_enet.o(i.enet_init), (868 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (356 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_enable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_set), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_config), (216 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_write_read), (156 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (50 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (280 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (256 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (340 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (444 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (56 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt), (10 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_enable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_drop), (172 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_size_get), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_software_reset), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_transmit_checksum_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_txfifo_flush), (52 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (20 bytes).
    Removing gd32f4xx_enet.o(.bss), (15460 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_enet.o(.data), (20 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (72 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (172 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_init), (228 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (40 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (106 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (188 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (284 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_command_para_init), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (66 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (64 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (28 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (188 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (80 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_halfword_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (72 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_page_erase), (124 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_ready_wait), (32 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_sector_erase), (96 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_state_get), (76 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_word_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_double_bank_select), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (96 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (84 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (76 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (40 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (52 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (72 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (72 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_prescaler_value_config), (60 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_reload_value_config), (64 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_toggle), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_write), (10 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (206 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ack_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_clock_config), (228 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_deinit), (88 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_clear), (40 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_get), (30 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_master_addressing), (20 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_mode_addr_config), (28 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_start_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stop_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (316 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (36 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (20 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (28 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f4xx_misc.o(i.nvic_vector_table_set), (24 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_misc.o(i.systick_clksource_set), (40 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_deepsleepmode), (244 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (108 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ck48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout0_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (140 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_stab_wait), (348 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (132 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (44 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (72 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_get), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (128 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (68 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (116 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (204 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_clear), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (200 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (92 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (76 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (20 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (48 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (292 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_set), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_deinit), (172 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_format_error_clear), (6 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_read_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_write_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (28 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (172 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (24 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_config), (492 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_mode_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config), (38 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_shadow_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_output_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_struct_para_init), (22 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (188 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (152 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (48 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (228 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(i.trng_deinit), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_flag_get), (24 bytes).
    Removing gd32f4xx_trng.o(i.trng_get_true_random_data), (12 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (28 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_receive_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_clear), (52 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_disable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (104 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (34 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (28 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (16 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (12 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).
    Removing startup_gd32f450_470.o(HEAP), (2048 bytes).
    Removing ff.o(i.dir_read), (190 bytes).
    Removing ff.o(i.dir_remove), (96 bytes).
    Removing ff.o(i.f_chmod), (92 bytes).
    Removing ff.o(i.f_getfree), (276 bytes).
    Removing ff.o(i.f_read), (462 bytes).
    Removing ff.o(i.f_readdir), (96 bytes).
    Removing ff.o(i.f_rename), (300 bytes).
    Removing ff.o(i.f_stat), (68 bytes).
    Removing ff.o(i.f_truncate), (156 bytes).
    Removing ff.o(i.f_unlink), (188 bytes).
    Removing ff.o(i.f_utime), (96 bytes).
    Removing ff.o(i.get_fileinfo), (316 bytes).
    Removing ff.o(i.pick_lfn), (116 bytes).
    Removing diskio.o(.rev16_text), (4 bytes).
    Removing diskio.o(.revsh_text), (4 bytes).

932 unused section(s) (total 67600 bytes) removed from the image.
