<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\CIMC_GD32_Template.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\CIMC_GD32_Template.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Tue Aug 12 01:27:47 2025
<BR><P>
<H3>Maximum Stack Usage =       1832 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
RS485_Task &rArr; Handle_Command_Set_Ratio &rArr; Update_Config_INI &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1f]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1f]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1f]">ADC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2]">ADC_Proc</a> from adc_app.o(i.ADC_Proc) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[7]">BusFault_Handler</a> from gd32f4xx_it.o(i.BusFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[23]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[21]">CAN0_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[22]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[20]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">CAN1_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[18]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[19]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1a]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1b]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1c]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1d]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1e]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">DMA1_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">DMA1_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">DMA1_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[48]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[a]">DebugMon_Handler</a> from gd32f4xx_it.o(i.DebugMon_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">ENET_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[13]">EXTI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">EXTI10_15_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[14]">EXTI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[15]">EXTI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[16]">EXTI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[17]">EXTI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[24]">EXTI5_9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[11]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5]">HardFault_Handler</a> from gd32f4xx_it.o(i.HardFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2d]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[65]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[0]">Key_Proc</a> from key_app.o(i.Key_Proc) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[e]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6]">MemManage_Handler</a> from gd32f4xx_it.o(i.MemManage_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4]">NMI_Handler</a> from gd32f4xx_it.o(i.NMI_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[b]">PendSV_Handler</a> from gd32f4xx_it.o(i.PendSV_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[12]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1]">RS485_Task</a> from usart_app.o(i.RS485_Task) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[36]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[10]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">SDIO_IRQHandler</a> from gd32f4xx_it.o(i.SDIO_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[9]">SVC_Handler</a> from gd32f4xx_it.o(i.SVC_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[c]">SysTick_Handler</a> from gd32f4xx_it.o(i.SysTick_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[67]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[f]">TAMPER_STAMP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[25]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[28]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[27]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[26]">TIMER0_UP_TIMER9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[29]">TIMER1_IRQHandler</a> from timer.o(i.TIMER1_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2a]">TIMER2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2b]">TIMER3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[43]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">TIMER6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[38]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[64]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[63]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">UART3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">UART4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">UART6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">UART7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">USART0_IRQHandler</a> from usart.o(i.USART0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[33]">USART1_IRQHandler</a> from usart.o(i.USART1_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[34]">USART2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">USBFS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">USBFS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8]">UsageFault_Handler</a> from gd32f4xx_it.o(i.UsageFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[d]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[68]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[6a]">_sbackspace</a> from _sgetc.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[69]">_sgetc</a> from _sgetc.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[6d]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0snprintf)
 <LI><a href="#[6d]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0vsnprintf)
 <LI><a href="#[6c]">fputc</a> from usart.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[6b]">isspace</a> from isspace_o.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[66]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[68]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[1c8]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[6e]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[89]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[1c9]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[1ca]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[1cb]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[1cc]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[1cd]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[3]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>ENET_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[a1]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cache_Log_To_Flash
</UL>

<P><STRONG><a name="[b3]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Default_Config_INI
</UL>

<P><STRONG><a name="[1ce]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[71]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cache_Log_To_Flash
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[1cf]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[1d0]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[70]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[a7]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Close_Sample_File
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Close_OverLimit_File
</UL>

<P><STRONG><a name="[1d1]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[72]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[9f]"></a>strstr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cache_Log_To_Flash
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Ratio
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Limit
</UL>

<P><STRONG><a name="[de]"></a>strncpy</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, strncpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_RTC
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Sample_Data_MultiChannel
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_OverLimit_Data
</UL>

<P><STRONG><a name="[9e]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Default_Config_INI
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cache_Log_To_Flash
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Sample_Data_MultiChannel
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_OverLimit_Data
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_HideData
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Config_INI
</UL>

<P><STRONG><a name="[112]"></a>strcmp</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, strcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[df]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_App_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Current_Display
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_RTC
</UL>

<P><STRONG><a name="[111]"></a>strncmp</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[73]"></a>atoi</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, atoi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = atoi &rArr; strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_RTC
</UL>

<P><STRONG><a name="[d8]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Current_Display
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ADC_Handler
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Save_Command
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Read_Command
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Get_Ratio_Command
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Get_Data_Command
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Ratio
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Limit
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Get_Limit
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Sample_Data_MultiChannel
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_OverLimit_Data
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_HideData
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Config_INI
</UL>

<P><STRONG><a name="[76]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Ratio
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Limit
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[1d2]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[144]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[78]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[7d]"></a>__strtod_int</STRONG> (Thumb, 90 bytes, Stack size 40 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[75]"></a>strtol</STRONG> (Thumb, 112 bytes, Stack size 32 bytes, strtol.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>

<P><STRONG><a name="[1d3]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[77]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[1d4]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[80]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[84]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[85]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[86]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[87]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[88]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[141]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[6f]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[1d5]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[7a]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[1d6]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[79]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[1d7]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[81]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[1d8]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[7e]"></a>__rt_ctype_table</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ctype_o.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[6b]"></a>isspace</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, isspace_o.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = isspace
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[7c]"></a>_scanf_real</STRONG> (Thumb, 0 bytes, Stack size 104 bytes, scanf_fp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>

<P><STRONG><a name="[8c]"></a>_scanf_really_real</STRONG> (Thumb, 556 bytes, Stack size 104 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[69]"></a>_sgetc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[6a]"></a>_sbackspace</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[7f]"></a>_strtoul</STRONG> (Thumb, 158 bytes, Stack size 40 bytes, _strtoul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[83]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[82]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[8e]"></a>_chval</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
</UL>

<P><STRONG><a name="[8b]"></a>__aeabi_ul2d</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, dfltul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[1d9]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[1da]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[8f]"></a>ADC_Init</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, adc.o(i.ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = ADC_Init &rArr; gpio_mode_set
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_routine_channel_config
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_resolution_config
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_enable
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_deinit
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_data_alignment_config
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_clock_config
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_channel_length_config
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[2]"></a>ADC_Proc</STRONG> (Thumb, 202 bytes, Stack size 8 bytes, adc_app.o(i.ADC_Proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = ADC_Proc &rArr; Update_Current_Display &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Current_Display
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_AD_Read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[7]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[9d]"></a>Cache_Log_To_Flash</STRONG> (Thumb, 218 bytes, Stack size 536 bytes, flash_app.o(i.Cache_Log_To_Flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = Cache_Log_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
</UL>

<P><STRONG><a name="[119]"></a>Calculate_Config_Checksum</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, flash_app.o(i.Calculate_Config_Checksum))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Calculate_Config_Checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Verify_Config_Data
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_Config_To_Flash
</UL>

<P><STRONG><a name="[11a]"></a>Calculate_Multi_Channel_Config_Checksum</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, flash_app.o(i.Calculate_Multi_Channel_Config_Checksum))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Calculate_Multi_Channel_Config_Checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Verify_Multi_Channel_Config_Data
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_Multi_Channel_Config_To_Flash
</UL>

<P><STRONG><a name="[a5]"></a>Check_Cached_Log_In_Flash</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, flash_app.o(i.Check_Cached_Log_In_Flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Check_Cached_Log_In_Flash &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Data_Recording
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Cached_Log_To_Log0
</UL>

<P><STRONG><a name="[a6]"></a>Close_OverLimit_File</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, sdcard_app.o(i.Close_OverLimit_File))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Close_OverLimit_File
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Sampling
</UL>

<P><STRONG><a name="[a8]"></a>Close_Sample_File</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, sdcard_app.o(i.Close_Sample_File))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Close_Sample_File
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Sampling
</UL>

<P><STRONG><a name="[a9]"></a>Configure_RTC_With_DateTime</STRONG> (Thumb, 210 bytes, Stack size 40 bytes, rtc_app.o(i.Configure_RTC_With_DateTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = Configure_RTC_With_DateTime &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_backup_write_enable
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;convert_to_bcd
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_RTC
</UL>

<P><STRONG><a name="[ae]"></a>Convert_RTC_To_Unix_Timestamp</STRONG> (Thumb, 234 bytes, Stack size 48 bytes, usart_app.o(i.Convert_RTC_To_Unix_Timestamp))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = Convert_RTC_To_Unix_Timestamp &rArr; get_days_in_month
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_leap_year
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_days_in_month
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bcd_to_decimal
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Encrypted_Output_With_Values_OverLimit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Encrypted_Output_With_Values
</UL>

<P><STRONG><a name="[d1]"></a>Convert_Uint32_To_Hex_String</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, usart_app.o(i.Convert_Uint32_To_Hex_String))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Convert_Uint32_To_Hex_String
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Encrypted_Output_With_Values_OverLimit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Encrypted_Output_With_Values
</UL>

<P><STRONG><a name="[b2]"></a>Create_Default_Config_INI</STRONG> (Thumb, 112 bytes, Stack size 664 bytes, sdcard_app.o(i.Create_Default_Config_INI))
<BR><BR>[Stack]<UL><LI>Max Depth = 1112<LI>Call Chain = Create_Default_Config_INI &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[b8]"></a>Create_HideData_File</STRONG> (Thumb, 118 bytes, Stack size 176 bytes, sdcard_app.o(i.Create_HideData_File))
<BR><BR>[Stack]<UL><LI>Max Depth = 624<LI>Call Chain = Create_HideData_File &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_HideData_Filename
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_HideData
</UL>

<P><STRONG><a name="[bc]"></a>Create_Log0_File</STRONG> (Thumb, 44 bytes, Stack size 560 bytes, sdcard_app.o(i.Create_Log0_File))
<BR><BR>[Stack]<UL><LI>Max Depth = 1008<LI>Call Chain = Create_Log0_File &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Data_Recording
</UL>

<P><STRONG><a name="[bd]"></a>Create_Log_File</STRONG> (Thumb, 108 bytes, Stack size 72 bytes, sdcard_app.o(i.Create_Log_File))
<BR><BR>[Stack]<UL><LI>Max Depth = 520<LI>Call Chain = Create_Log_File &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Log_Filename
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
</UL>

<P><STRONG><a name="[c1]"></a>Create_Sample_File</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, sdcard_app.o(i.Create_Sample_File))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Create_Sample_File &rArr; Generate_Filename &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Filename
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Sampling
</UL>

<P><STRONG><a name="[a]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[d0]"></a>Encode_Voltage_To_Hex</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, usart_app.o(i.Encode_Voltage_To_Hex))
<BR><BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Encrypted_Output_With_Values_OverLimit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Encrypted_Output_With_Values
</UL>

<P><STRONG><a name="[c3]"></a>Flash_Init</STRONG> (Thumb, 254 bytes, Stack size 88 bytes, flash_app.o(i.Flash_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = Flash_Init &rArr; Read_Config_From_Flash &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Config_From_Flash
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Log_Init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_System_State
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Sample_Interval
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Ratio
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Limit
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[9b]"></a>GD30AD3344_AD_Read</STRONG> (Thumb, 182 bytes, Stack size 24 bytes, gd30ad3344.o(i.GD30AD3344_AD_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = GD30AD3344_AD_Read &rArr; spi_gd30ad3344_send_halfword_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_PGA_SET
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Proc
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ADC_Handler
</UL>

<P><STRONG><a name="[ce]"></a>GD30AD3344_PGA_SET</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, gd30ad3344.o(i.GD30AD3344_PGA_SET))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_AD_Read
</UL>

<P><STRONG><a name="[cf]"></a>Generate_Encrypted_Output_With_Values</STRONG> (Thumb, 130 bytes, Stack size 120 bytes, usart_app.o(i.Generate_Encrypted_Output_With_Values))
<BR><BR>[Stack]<UL><LI>Max Depth = 1048<LI>Call Chain = Generate_Encrypted_Output_With_Values &rArr; Write_HideData &rArr; Create_HideData_File &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Encode_Voltage_To_Hex
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Convert_Uint32_To_Hex_String
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Convert_RTC_To_Unix_Timestamp
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_HideData
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ADC_Handler
</UL>

<P><STRONG><a name="[d4]"></a>Generate_Encrypted_Output_With_Values_OverLimit</STRONG> (Thumb, 72 bytes, Stack size 48 bytes, usart_app.o(i.Generate_Encrypted_Output_With_Values_OverLimit))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Generate_Encrypted_Output_With_Values_OverLimit &rArr; Convert_RTC_To_Unix_Timestamp &rArr; get_days_in_month
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Encode_Voltage_To_Hex
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Convert_Uint32_To_Hex_String
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Convert_RTC_To_Unix_Timestamp
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ADC_Handler
</UL>

<P><STRONG><a name="[b9]"></a>Generate_HideData_Filename</STRONG> (Thumb, 62 bytes, Stack size 56 bytes, sdcard_app.o(i.Generate_HideData_Filename))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Generate_HideData_Filename &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_HideData_File
</UL>

<P><STRONG><a name="[d5]"></a>Generate_OverLimit_Filename</STRONG> (Thumb, 62 bytes, Stack size 56 bytes, sdcard_app.o(i.Generate_OverLimit_Filename))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Generate_OverLimit_Filename &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_OverLimit_Data
</UL>

<P><STRONG><a name="[ef]"></a>Get_Limit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc_app.o(i.Get_Limit))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Save_Command
</UL>

<P><STRONG><a name="[d6]"></a>Get_Next_Log_ID_From_SD</STRONG> (Thumb, 84 bytes, Stack size 624 bytes, sdcard_app.o(i.Get_Next_Log_ID_From_SD))
<BR><BR>[Stack]<UL><LI>Max Depth = 1072<LI>Call Chain = Get_Next_Log_ID_From_SD &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Data_Recording
</UL>

<P><STRONG><a name="[f0]"></a>Get_Ratio</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc_app.o(i.Get_Ratio))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Save_Command
</UL>

<P><STRONG><a name="[ec]"></a>Get_Sample_Interval</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, adc_app.o(i.Get_Sample_Interval))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Save_Command
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Read_Command
</UL>

<P><STRONG><a name="[d7]"></a>Handle_Command_Get_Limit</STRONG> (Thumb, 70 bytes, Stack size 48 bytes, usart_app.o(i.Handle_Command_Get_Limit))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Handle_Command_Get_Limit &rArr; rs485_printf &rArr; vsnprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[d9]"></a>Handle_Command_Set_Limit</STRONG> (Thumb, 376 bytes, Stack size 176 bytes, usart_app.o(i.Handle_Command_Set_Limit))
<BR><BR>[Stack]<UL><LI>Max Depth = 1824<LI>Call Chain = Handle_Command_Set_Limit &rArr; Update_Config_INI &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_System_State
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Config_INI
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[dd]"></a>Handle_Command_Set_RTC</STRONG> (Thumb, 350 bytes, Stack size 208 bytes, usart_app.o(i.Handle_Command_Set_RTC))
<BR><BR>[Stack]<UL><LI>Max Depth = 1128<LI>Call Chain = Handle_Command_Set_RTC &rArr; Write_Log_Data &rArr; Cache_Log_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Configure_RTC_With_DateTime
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[e0]"></a>Handle_Command_Set_Ratio</STRONG> (Thumb, 388 bytes, Stack size 176 bytes, usart_app.o(i.Handle_Command_Set_Ratio))
<BR><BR>[Stack]<UL><LI>Max Depth = 1824<LI>Call Chain = Handle_Command_Set_Ratio &rArr; Update_Config_INI &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_System_State
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Config_INI
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[e1]"></a>Handle_Get_Data_Command</STRONG> (Thumb, 286 bytes, Stack size 72 bytes, usart_app.o(i.Handle_Get_Data_Command))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = Handle_Get_Data_Command &rArr; rs485_printf &rArr; vsnprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[e2]"></a>Handle_Get_Device_ID_Command</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, usart_app.o(i.Handle_Get_Device_ID_Command))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = Handle_Get_Device_ID_Command &rArr; rs485_printf &rArr; vsnprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[e3]"></a>Handle_Get_Ratio_Command</STRONG> (Thumb, 134 bytes, Stack size 152 bytes, usart_app.o(i.Handle_Get_Ratio_Command))
<BR><BR>[Stack]<UL><LI>Max Depth = 1072<LI>Call Chain = Handle_Get_Ratio_Command &rArr; Write_Log_Data &rArr; Cache_Log_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[e4]"></a>Handle_Key1_Press</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, key_app.o(i.Handle_Key1_Press))
<BR><BR>[Stack]<UL><LI>Max Depth = 928<LI>Call Chain = Handle_Key1_Press &rArr; Write_Log_Data &rArr; Cache_Log_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Current_Display
</UL>
<BR>[Called By]<UL><LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
</UL>

<P><STRONG><a name="[e5]"></a>Handle_Key2_Press</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, key_app.o(i.Handle_Key2_Press))
<BR><BR>[Stack]<UL><LI>Max Depth = 928<LI>Call Chain = Handle_Key2_Press &rArr; Write_Log_Data &rArr; Cache_Log_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Current_Display
</UL>
<BR>[Called By]<UL><LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
</UL>

<P><STRONG><a name="[e6]"></a>Handle_Key3_Press</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, key_app.o(i.Handle_Key3_Press))
<BR><BR>[Stack]<UL><LI>Max Depth = 928<LI>Call Chain = Handle_Key3_Press &rArr; Write_Log_Data &rArr; Cache_Log_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Current_Display
</UL>
<BR>[Called By]<UL><LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
</UL>

<P><STRONG><a name="[e7]"></a>Handle_Key4_Press</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, key_app.o(i.Handle_Key4_Press))
<BR><BR>[Stack]<UL><LI>Max Depth = 928<LI>Call Chain = Handle_Key4_Press &rArr; Write_Log_Data &rArr; Cache_Log_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Current_Display
</UL>
<BR>[Called By]<UL><LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
</UL>

<P><STRONG><a name="[e8]"></a>Handle_Key5_Press</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, key_app.o(i.Handle_Key5_Press))
<BR><BR>[Stack]<UL><LI>Max Depth = 928<LI>Call Chain = Handle_Key5_Press &rArr; Write_Log_Data &rArr; Cache_Log_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Current_Display
</UL>
<BR>[Called By]<UL><LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
</UL>

<P><STRONG><a name="[e9]"></a>Handle_Key6_Press</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, key_app.o(i.Handle_Key6_Press))
<BR><BR>[Stack]<UL><LI>Max Depth = 928<LI>Call Chain = Handle_Key6_Press &rArr; Write_Log_Data &rArr; Cache_Log_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Current_Display
</UL>
<BR>[Called By]<UL><LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
</UL>

<P><STRONG><a name="[ea]"></a>Handle_Multi_Config_Read_Command</STRONG> (Thumb, 488 bytes, Stack size 80 bytes, usart_app.o(i.Handle_Multi_Config_Read_Command))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = Handle_Multi_Config_Read_Command &rArr; Read_Multi_Channel_Config_From_Flash &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Multi_Channel_Config_From_Flash
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Config_From_Flash
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_System_State
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Sample_Interval
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Sample_Interval
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[ed]"></a>Handle_Multi_Config_Save_Command</STRONG> (Thumb, 256 bytes, Stack size 40 bytes, usart_app.o(i.Handle_Multi_Config_Save_Command))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = Handle_Multi_Config_Save_Command &rArr; Save_Multi_Channel_Config_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_Multi_Channel_Config_To_Flash
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_Config_To_Flash
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_System_State
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Sample_Interval
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Ratio
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Limit
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[f2]"></a>Handle_RTC_Now_Command</STRONG> (Thumb, 50 bytes, Stack size 40 bytes, usart_app.o(i.Handle_RTC_Now_Command))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Handle_RTC_Now_Command &rArr; rs485_printf &rArr; vsnprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[f3]"></a>Handle_Start_Command</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, usart_app.o(i.Handle_Start_Command))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Handle_Start_Command &rArr; Start_Sampling &rArr; Create_Sample_File &rArr; Generate_Filename &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Sampling
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[f5]"></a>Handle_Stop_Command</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, usart_app.o(i.Handle_Stop_Command))
<BR><BR>[Stack]<UL><LI>Max Depth = 928<LI>Call Chain = Handle_Stop_Command &rArr; Write_Log_Data &rArr; Cache_Log_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Sampling
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RS485_Task
</UL>

<P><STRONG><a name="[5]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f7]"></a>I2C_Start</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, oled.o(i.I2C_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = I2C_Start &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[fc]"></a>I2C_Stop</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, oled.o(i.I2C_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = I2C_Stop &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
</UL>

<P><STRONG><a name="[fd]"></a>I2C_WaitAck</STRONG> (Thumb, 116 bytes, Stack size 8 bytes, oled.o(i.I2C_WaitAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = I2C_WaitAck &rArr; I2C_Stop &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[fa]"></a>IIC_delay</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, oled.o(i.IIC_delay))
<BR><BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Byte
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
</UL>

<P><STRONG><a name="[ff]"></a>Init_Data_Recording</STRONG> (Thumb, 198 bytes, Stack size 56 bytes, sdcard_app.o(i.Init_Data_Recording))
<BR><BR>[Stack]<UL><LI>Max Depth = 1584<LI>Call Chain = Init_Data_Recording &rArr; Write_Cached_Log_To_Log0 &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Cached_Log_To_Log0
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Cached_Log_In_Flash
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_config
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Next_Log_ID_From_SD
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Log0_File
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[104]"></a>Key_Init</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, key.o(i.Key_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Key_Init &rArr; gpio_mode_set
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[0]"></a>Key_Proc</STRONG> (Thumb, 144 bytes, Stack size 8 bytes, key_app.o(i.Key_Proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 936<LI>Call Chain = Key_Proc &rArr; Handle_Key6_Press &rArr; Write_Log_Data &rArr; Cache_Log_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Read
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key6_Press
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key5_Press
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key4_Press
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key3_Press
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key2_Press
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key1_Press
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[105]"></a>Key_Read</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, key.o(i.Key_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Key_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
</UL>

<P><STRONG><a name="[106]"></a>Led_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, led.o(i.Led_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Led_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[cb]"></a>Log_Init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, flash_app.o(i.Log_Init))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
</UL>

<P><STRONG><a name="[6]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[107]"></a>OLED_App_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, oled_app.o(i.OLED_App_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = OLED_App_Init &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[10a]"></a>OLED_Clear</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = OLED_Clear &rArr; OLED_Refresh &rArr; OLED_WR_Byte &rArr; I2C_WaitAck &rArr; I2C_Stop &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[10f]"></a>OLED_ClearPoint</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, oled.o(i.OLED_ClearPoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_ClearPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[10e]"></a>OLED_DrawPoint</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, oled.o(i.OLED_DrawPoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[10b]"></a>OLED_Init</STRONG> (Thumb, 324 bytes, Stack size 8 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = OLED_Init &rArr; OLED_Clear &rArr; OLED_Refresh &rArr; OLED_WR_Byte &rArr; I2C_WaitAck &rArr; I2C_Stop &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[109]"></a>OLED_Refresh</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, oled.o(i.OLED_Refresh))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = OLED_Refresh &rArr; OLED_WR_Byte &rArr; I2C_WaitAck &rArr; I2C_Stop &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_App_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Current_Display
</UL>

<P><STRONG><a name="[10d]"></a>OLED_ShowChar</STRONG> (Thumb, 240 bytes, Stack size 56 bytes, oled.o(i.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_DrawPoint
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ClearPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>

<P><STRONG><a name="[108]"></a>OLED_ShowString</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, oled.o(i.OLED_ShowString))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_App_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Current_Display
</UL>

<P><STRONG><a name="[10c]"></a>OLED_WR_Byte</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, oled.o(i.OLED_WR_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = OLED_WR_Byte &rArr; I2C_WaitAck &rArr; I2C_Stop &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Byte
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[b]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>RS485_Task</STRONG> (Thumb, 530 bytes, Stack size 8 bytes, usart_app.o(i.RS485_Task))
<BR><BR>[Stack]<UL><LI>Max Depth = 1832<LI>Call Chain = RS485_Task &rArr; Handle_Command_Set_Ratio &rArr; Update_Config_INI &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Stop_Command
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Start_Command
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_RTC_Now_Command
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Save_Command
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Read_Command
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Get_Ratio_Command
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Get_Device_ID_Command
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Get_Data_Command
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Ratio
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_RTC
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Limit
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Get_Limit
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[113]"></a>RTC_Init</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, rtc.o(i.RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = RTC_Init &rArr; rtc_setup_default &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_all_reset_flag_clear
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_backup_write_enable
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_setup_default
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[c7]"></a>Read_Config_From_Flash</STRONG> (Thumb, 70 bytes, Stack size 40 bytes, flash_app.o(i.Read_Config_From_Flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = Read_Config_From_Flash &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Verify_Config_Data
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Read_Command
</UL>

<P><STRONG><a name="[eb]"></a>Read_Multi_Channel_Config_From_Flash</STRONG> (Thumb, 120 bytes, Stack size 64 bytes, flash_app.o(i.Read_Multi_Channel_Config_From_Flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Read_Multi_Channel_Config_From_Flash &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Verify_Multi_Channel_Config_Data
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Read_Command
</UL>

<P><STRONG><a name="[3e]"></a>SDIO_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SDIO_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SDIO_IRQHandler &rArr; sd_interrupts_process &rArr; sd_transfer_stop &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f1]"></a>Save_Config_To_Flash</STRONG> (Thumb, 152 bytes, Stack size 40 bytes, flash_app.o(i.Save_Config_To_Flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Save_Config_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Calculate_Config_Checksum
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Save_Command
</UL>

<P><STRONG><a name="[ee]"></a>Save_Multi_Channel_Config_To_Flash</STRONG> (Thumb, 268 bytes, Stack size 64 bytes, flash_app.o(i.Save_Multi_Channel_Config_To_Flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Save_Multi_Channel_Config_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Calculate_Multi_Channel_Config_Checksum
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Save_Command
</UL>

<P><STRONG><a name="[110]"></a>Send_Byte</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, oled.o(i.Send_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Send_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[11e]"></a>Set_ADC_Sample_Interval</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, timer_app.o(i.Set_ADC_Sample_Interval))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Sampling
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Sample_Interval
</UL>

<P><STRONG><a name="[120]"></a>Set_ADC_Sampling_State</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, timer_app.o(i.Set_ADC_Sampling_State))
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Sampling
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Sampling
</UL>

<P><STRONG><a name="[11b]"></a>Set_LED1_Blink_Mode</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, timer_app.o(i.Set_LED1_Blink_Mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Set_LED1_Blink_Mode
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_LED_Status
</UL>

<P><STRONG><a name="[11c]"></a>Set_LED2_State</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, timer_app.o(i.Set_LED2_State))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Set_LED2_State
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_LED_Status
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Over_Limit_State
</UL>

<P><STRONG><a name="[c9]"></a>Set_Limit</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, adc_app.o(i.Set_Limit))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
</UL>

<P><STRONG><a name="[11d]"></a>Set_Over_Limit_State</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, adc_app.o(i.Set_Over_Limit_State))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Set_Over_Limit_State &rArr; Set_LED2_State
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_LED2_State
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ADC_Handler
</UL>

<P><STRONG><a name="[c8]"></a>Set_Ratio</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, adc_app.o(i.Set_Ratio))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
</UL>

<P><STRONG><a name="[ca]"></a>Set_Sample_Interval</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, adc_app.o(i.Set_Sample_Interval))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Set_Sample_Interval
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_ADC_Sample_Interval
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Read_Command
</UL>

<P><STRONG><a name="[cc]"></a>Set_System_State</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, adc_app.o(i.Set_System_State))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Set_System_State &rArr; Update_LED_Status &rArr; Set_LED2_State
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_LED_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Save_Command
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Read_Command
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Ratio
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Limit
</UL>

<P><STRONG><a name="[f4]"></a>Start_Sampling</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, adc_app.o(i.Start_Sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Start_Sampling &rArr; Create_Sample_File &rArr; Generate_Filename &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_ADC_Sampling_State
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_ADC_Sample_Interval
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Sample_File
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_LED_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Start_Command
</UL>

<P><STRONG><a name="[f6]"></a>Stop_Sampling</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, adc_app.o(i.Stop_Sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Stop_Sampling &rArr; Update_LED_Status &rArr; Set_LED2_State
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_ADC_Sampling_State
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Close_Sample_File
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Close_OverLimit_File
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_LED_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Stop_Command
</UL>

<P><STRONG><a name="[c]"></a>SysTick_Handler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>SystemInit</STRONG> (Thumb, 194 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit &rArr; system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[123]"></a>System_Init</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, function.o(i.System_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = System_Init &rArr; systick_config &rArr; NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[29]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, timer.o(i.TIMER1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 1224<LI>Call Chain = TIMER1_IRQHandler &rArr; Timer_ADC_Handler &rArr; Generate_Encrypted_Output_With_Values &rArr; Write_HideData &rArr; Create_HideData_File &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_LED_Handler
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ADC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[128]"></a>Timer_ADC_Handler</STRONG> (Thumb, 944 bytes, Stack size 168 bytes, timer_app.o(i.Timer_ADC_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 1216<LI>Call Chain = Timer_ADC_Handler &rArr; Generate_Encrypted_Output_With_Values &rArr; Write_HideData &rArr; Create_HideData_File &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Over_Limit_State
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_AD_Read
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Encrypted_Output_With_Values_OverLimit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Encrypted_Output_With_Values
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Sample_Data_MultiChannel
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_OverLimit_Data
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_IRQHandler
</UL>

<P><STRONG><a name="[12b]"></a>Timer_Init</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, timer.o(i.Timer_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Timer_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[127]"></a>Timer_LED_Handler</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, timer_app.o(i.Timer_LED_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Timer_LED_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_IRQHandler
</UL>

<P><STRONG><a name="[32]"></a>USART0_IRQHandler</STRONG> (Thumb, 130 bytes, Stack size 8 bytes, usart.o(i.USART0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART0_IRQHandler &rArr; usart_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USART1_IRQHandler</STRONG> (Thumb, 130 bytes, Stack size 8 bytes, usart.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART1_IRQHandler &rArr; usart_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[db]"></a>Update_Config_INI</STRONG> (Thumb, 222 bytes, Stack size 1200 bytes, sdcard_app.o(i.Update_Config_INI))
<BR><BR>[Stack]<UL><LI>Max Depth = 1648<LI>Call Chain = Update_Config_INI &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Ratio
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Limit
</UL>

<P><STRONG><a name="[9c]"></a>Update_Current_Display</STRONG> (Thumb, 342 bytes, Stack size 24 bytes, key_app.o(i.Update_Current_Display))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = Update_Current_Display &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Proc
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key6_Press
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key5_Press
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key4_Press
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key3_Press
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key2_Press
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key1_Press
</UL>

<P><STRONG><a name="[11f]"></a>Update_LED_Status</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, adc_app.o(i.Update_LED_Status))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Update_LED_Status &rArr; Set_LED2_State
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_LED2_State
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_LED1_Blink_Mode
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Sampling
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Sampling
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_System_State
</UL>

<P><STRONG><a name="[8]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[134]"></a>UsrFunction</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, function.o(i.UsrFunction))
<BR><BR>[Stack]<UL><LI>Max Depth = 1584<LI>Call Chain = UsrFunction &rArr; Init_Data_Recording &rArr; Write_Cached_Log_To_Log0 &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Led_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_App_Init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Data_Recording
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Default_Config_INI
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd30ad3344_init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[116]"></a>Verify_Config_Data</STRONG> (Thumb, 132 bytes, Stack size 12 bytes, flash_app.o(i.Verify_Config_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Verify_Config_Data &rArr; Calculate_Config_Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Calculate_Config_Checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Config_From_Flash
</UL>

<P><STRONG><a name="[117]"></a>Verify_Multi_Channel_Config_Data</STRONG> (Thumb, 250 bytes, Stack size 12 bytes, flash_app.o(i.Verify_Multi_Channel_Config_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Verify_Multi_Channel_Config_Data &rArr; Calculate_Multi_Channel_Config_Checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Calculate_Multi_Channel_Config_Checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Multi_Channel_Config_From_Flash
</UL>

<P><STRONG><a name="[103]"></a>Write_Cached_Log_To_Log0</STRONG> (Thumb, 166 bytes, Stack size 1080 bytes, flash_app.o(i.Write_Cached_Log_To_Log0))
<BR><BR>[Stack]<UL><LI>Max Depth = 1528<LI>Call Chain = Write_Cached_Log_To_Log0 &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Cached_Log_In_Flash
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Data_Recording
</UL>

<P><STRONG><a name="[d3]"></a>Write_HideData</STRONG> (Thumb, 148 bytes, Stack size 304 bytes, sdcard_app.o(i.Write_HideData))
<BR><BR>[Stack]<UL><LI>Max Depth = 928<LI>Call Chain = Write_HideData &rArr; Create_HideData_File &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_HideData_File
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Encrypted_Output_With_Values
</UL>

<P><STRONG><a name="[dc]"></a>Write_Log_Data</STRONG> (Thumb, 190 bytes, Stack size 320 bytes, sdcard_app.o(i.Write_Log_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 920<LI>Call Chain = Write_Log_Data &rArr; Cache_Log_To_Flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cache_Log_To_Flash
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Log_File
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Stop_Command
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Get_Ratio_Command
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Ratio
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_RTC
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Limit
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key6_Press
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key5_Press
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key4_Press
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key3_Press
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key2_Press
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Key1_Press
</UL>

<P><STRONG><a name="[129]"></a>Write_OverLimit_Data</STRONG> (Thumb, 300 bytes, Stack size 400 bytes, sdcard_app.o(i.Write_OverLimit_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 848<LI>Call Chain = Write_OverLimit_Data &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_OverLimit_Filename
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ADC_Handler
</UL>

<P><STRONG><a name="[12a]"></a>Write_Sample_Data_MultiChannel</STRONG> (Thumb, 400 bytes, Stack size 440 bytes, sdcard_app.o(i.Write_Sample_Data_MultiChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 888<LI>Call Chain = Write_Sample_Data_MultiChannel &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Filename
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ADC_Handler
</UL>

<P><STRONG><a name="[13a]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1db]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[d2]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Encrypted_Output_With_Values_OverLimit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Encrypted_Output_With_Values
</UL>

<P><STRONG><a name="[1dc]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[1dd]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[13c]"></a>__0snprintf</STRONG> (Thumb, 44 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1de]"></a>__1snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[bb]"></a>__2snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Current_Display
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ADC_Handler
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Get_Ratio_Command
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Ratio
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_RTC
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Limit
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Encrypted_Output_With_Values
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Sample_Data_MultiChannel
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_OverLimit_Data
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_HideData
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Config_INI
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Next_Log_ID_From_SD
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_OverLimit_Filename
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_HideData_Filename
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Log_File
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_HideData_File
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Log_Filename
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Filename
</UL>

<P><STRONG><a name="[1df]"></a>__c89snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[1e0]"></a>snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[13d]"></a>__0vsnprintf</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1e1]"></a>__1vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[1e2]"></a>__2vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[1e3]"></a>__c89vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[18b]"></a>vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
</UL>

<P><STRONG><a name="[74]"></a>__aeabi_errno_addr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>

<P><STRONG><a name="[1e4]"></a>__rt_errno_addr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr), UNUSED)

<P><STRONG><a name="[da]"></a>__hardfp_atof</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, atof.o(i.__hardfp_atof))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = __hardfp_atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Ratio
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Limit
</UL>

<P><STRONG><a name="[13e]"></a>__read_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__read_errno))
<BR><BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[1e5]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[1e6]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[1e7]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[13f]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[8d]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, scanf_fp.o(i._is_digit), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[9a]"></a>adc_calibration_enable</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_calibration_enable))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[96]"></a>adc_channel_length_config</STRONG> (Thumb, 82 bytes, Stack size 12 bytes, gd32f4xx_adc.o(i.adc_channel_length_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = adc_channel_length_config
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[90]"></a>adc_clock_config</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[95]"></a>adc_data_alignment_config</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_data_alignment_config))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[93]"></a>adc_deinit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_adc.o(i.adc_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = adc_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[98]"></a>adc_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_enable))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[94]"></a>adc_resolution_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_resolution_config))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[97]"></a>adc_routine_channel_config</STRONG> (Thumb, 172 bytes, Stack size 20 bytes, gd32f4xx_adc.o(i.adc_routine_channel_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = adc_routine_channel_config
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[15d]"></a>clust2sect</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ff.o(i.clust2sect))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[99]"></a>delay_1ms</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systick.o(i.delay_1ms))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[121]"></a>delay_decrement</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(i.delay_decrement))
<BR><BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[101]"></a>disk_initialize</STRONG> (Thumb, 134 bytes, Stack size 88 bytes, diskio.o(i.disk_initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = disk_initialize &rArr; sd_bus_mode_config &rArr; sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_mode_config
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_information_get
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Data_Recording
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[1c4]"></a>disk_ioctl</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, diskio.o(i.disk_ioctl))
<BR><BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
</UL>

<P><STRONG><a name="[148]"></a>disk_read</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, diskio.o(i.disk_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = disk_read &rArr; sd_multiblocks_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>

<P><STRONG><a name="[14a]"></a>disk_status</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, diskio.o(i.disk_status))
<BR><BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[16a]"></a>disk_write</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, diskio.o(i.disk_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>

<P><STRONG><a name="[16f]"></a>dma_channel_disable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_channel_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_channel_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
</UL>

<P><STRONG><a name="[174]"></a>dma_channel_enable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_channel_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_channel_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
</UL>

<P><STRONG><a name="[173]"></a>dma_channel_subperipheral_select</STRONG> (Thumb, 38 bytes, Stack size 12 bytes, gd32f4xx_dma.o(i.dma_channel_subperipheral_select))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dma_channel_subperipheral_select
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
</UL>

<P><STRONG><a name="[170]"></a>dma_deinit</STRONG> (Thumb, 166 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_deinit
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
</UL>

<P><STRONG><a name="[16e]"></a>dma_flag_clear</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
</UL>

<P><STRONG><a name="[1a9]"></a>dma_flag_get</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[172]"></a>dma_flow_controller_config</STRONG> (Thumb, 64 bytes, Stack size 12 bytes, gd32f4xx_dma.o(i.dma_flow_controller_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dma_flow_controller_config
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[171]"></a>dma_multi_data_mode_init</STRONG> (Thumb, 352 bytes, Stack size 16 bytes, gd32f4xx_dma.o(i.dma_multi_data_mode_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = dma_multi_data_mode_init
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[1c1]"></a>dma_single_data_mode_init</STRONG> (Thumb, 340 bytes, Stack size 16 bytes, gd32f4xx_dma.o(i.dma_single_data_mode_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = dma_single_data_mode_init
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
</UL>

<P><STRONG><a name="[b5]"></a>f_close</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, ff.o(i.f_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = f_close &rArr; f_sync &rArr; sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Default_Config_INI
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Cached_Log_To_Log0
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Sample_Data_MultiChannel
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_OverLimit_Data
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_HideData
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Config_INI
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Next_Log_ID_From_SD
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Log_File
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Log0_File
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_HideData_File
</UL>

<P><STRONG><a name="[139]"></a>f_lseek</STRONG> (Thumb, 432 bytes, Stack size 32 bytes, ff.o(i.f_lseek))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = f_lseek &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Cached_Log_To_Log0
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Sample_Data_MultiChannel
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_OverLimit_Data
</UL>

<P><STRONG><a name="[bf]"></a>f_mkdir</STRONG> (Thumb, 388 bytes, Stack size 88 bytes, ff.o(i.f_mkdir))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = f_mkdir &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Data_Recording
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Cached_Log_To_Log0
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Log_File
</UL>

<P><STRONG><a name="[be]"></a>f_mount</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, ff.o(i.f_mount))
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Data_Recording
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Cached_Log_To_Log0
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Log_File
</UL>

<P><STRONG><a name="[b4]"></a>f_open</STRONG> (Thumb, 368 bytes, Stack size 88 bytes, ff.o(i.f_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Default_Config_INI
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Cached_Log_To_Log0
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Sample_Data_MultiChannel
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_OverLimit_Data
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Config_INI
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Next_Log_ID_From_SD
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Log_File
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Log0_File
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_HideData_File
</UL>

<P><STRONG><a name="[102]"></a>f_opendir</STRONG> (Thumb, 114 bytes, Stack size 32 bytes, ff.o(i.f_opendir))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = f_opendir &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Data_Recording
</UL>

<P><STRONG><a name="[b7]"></a>f_sync</STRONG> (Thumb, 184 bytes, Stack size 24 bytes, ff.o(i.f_sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = f_sync &rArr; sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Default_Config_INI
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Cached_Log_To_Log0
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Sample_Data_MultiChannel
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_HideData
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Config_INI
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Log0_File
</UL>

<P><STRONG><a name="[b6]"></a>f_write</STRONG> (Thumb, 526 bytes, Stack size 64 bytes, ff.o(i.f_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Default_Config_INI
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Cached_Log_To_Log0
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Sample_Data_MultiChannel
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_OverLimit_Data
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_HideData
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Config_INI
</UL>

<P><STRONG><a name="[154]"></a>ff_convert</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, sdcard_app.o(i.ff_convert))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ff_convert
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[14f]"></a>ff_wtoupper</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, sdcard_app.o(i.ff_wtoupper))
<BR><BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
</UL>

<P><STRONG><a name="[6c]"></a>fputc</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, usart.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fputc &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[136]"></a>gd30ad3344_init</STRONG> (Thumb, 344 bytes, Stack size 32 bytes, gd30ad3344.o(i.gd30ad3344_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = gd30ad3344_init &rArr; spi_gd30ad3344_send_halfword_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[160]"></a>gen_numname</STRONG> (Thumb, 202 bytes, Stack size 40 bytes, ff.o(i.gen_numname))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = gen_numname &rArr; mem_cpy
</UL>
<BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[151]"></a>get_fat</STRONG> (Thumb, 228 bytes, Stack size 24 bytes, ff.o(i.get_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = get_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[177]"></a>get_fattime</STRONG> (Thumb, 66 bytes, Stack size 32 bytes, diskio.o(i.get_fattime))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = get_fattime &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[17d]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd30ad3344_init
</UL>

<P><STRONG><a name="[fb]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Byte
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Led_Init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_LED2_State
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_LED1_Blink_Mode
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_LED_Handler
</UL>

<P><STRONG><a name="[f9]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_Byte
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_LED2_State
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_LED_Handler
</UL>

<P><STRONG><a name="[fe]"></a>gpio_input_bit_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Read
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
</UL>

<P><STRONG><a name="[92]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Led_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd30ad3344_init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>

<P><STRONG><a name="[f8]"></a>gpio_output_options_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitAck
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Stop
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Start
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Led_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd30ad3344_init
</UL>

<P><STRONG><a name="[66]"></a>main</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 1592<LI>Call Chain = main &rArr; UsrFunction &rArr; Init_Data_Recording &rArr; Write_Cached_Log_To_Log0 &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[100]"></a>nvic_config</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, sdcard_app.o(i.nvic_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = nvic_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Data_Recording
</UL>

<P><STRONG><a name="[12f]"></a>nvic_irq_enable</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Init
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_config
</UL>

<P><STRONG><a name="[181]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_config
</UL>

<P><STRONG><a name="[aa]"></a>pmu_backup_write_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_pmu.o(i.pmu_backup_write_enable))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_setup_default
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Configure_RTC_With_DateTime
</UL>

<P><STRONG><a name="[152]"></a>put_fat</STRONG> (Thumb, 310 bytes, Stack size 32 bytes, ff.o(i.put_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[115]"></a>rcu_all_reset_flag_clear</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[1c7]"></a>rcu_clock_freq_get</STRONG> (Thumb, 264 bytes, Stack size 84 bytes, gd32f4xx_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>

<P><STRONG><a name="[19b]"></a>rcu_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
</UL>

<P><STRONG><a name="[19a]"></a>rcu_osci_on</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_osci_on))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
</UL>

<P><STRONG><a name="[91]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_config
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_setup_default
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Led_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd30ad3344_init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Configure_RTC_With_DateTime
</UL>

<P><STRONG><a name="[146]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_deinit
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
</UL>

<P><STRONG><a name="[145]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_deinit
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
</UL>

<P><STRONG><a name="[19c]"></a>rcu_rtc_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_rtc_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
</UL>

<P><STRONG><a name="[c4]"></a>rs485_printf</STRONG> (Thumb, 118 bytes, Stack size 40 bytes, usart.o(i.rs485_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = rs485_printf &rArr; vsnprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ADC_Handler
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Stop_Command
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_RTC_Now_Command
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Save_Command
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Multi_Config_Read_Command
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Get_Ratio_Command
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Get_Device_ID_Command
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Get_Data_Command
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Ratio
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_RTC
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_Limit
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Get_Limit
</UL>

<P><STRONG><a name="[135]"></a>rs485_usart1_config</STRONG> (Thumb, 240 bytes, Stack size 8 bytes, usart.o(i.rs485_usart1_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = rs485_usart1_config &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_word_length_set
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_stop_bit_set
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_parity_config
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_hardware_flow_rts_config
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_hardware_flow_cts_config
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[ba]"></a>rtc_current_time_get</STRONG> (Thumb, 96 bytes, Stack size 12 bytes, gd32f4xx_rtc.o(i.rtc_current_time_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = rtc_current_time_get
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Log_Data
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_ADC_Handler
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_RTC_Now_Command
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Handle_Command_Set_RTC
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Sample_Data_MultiChannel
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_OverLimit_Data
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_OverLimit_Filename
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_HideData_Filename
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_HideData_File
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Generate_Filename
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
</UL>

<P><STRONG><a name="[ad]"></a>rtc_init</STRONG> (Thumb, 216 bytes, Stack size 20 bytes, gd32f4xx_rtc.o(i.rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_exit
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_setup_default
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Configure_RTC_With_DateTime
</UL>

<P><STRONG><a name="[197]"></a>rtc_init_mode_enter</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[198]"></a>rtc_init_mode_exit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_exit))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[ab]"></a>rtc_pre_config</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, rtc.o(i.rtc_pre_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rtc_pre_config &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_rtc_clock_config
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_on
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Configure_RTC_With_DateTime
</UL>

<P><STRONG><a name="[199]"></a>rtc_register_sync_wait</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_register_sync_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_register_sync_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
</UL>

<P><STRONG><a name="[114]"></a>rtc_setup_default</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, rtc.o(i.rtc_setup_default))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = rtc_setup_default &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_backup_write_enable
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[137]"></a>scheduler_init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, scheduler.o(i.scheduler_init))
<BR><BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[138]"></a>scheduler_run</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, scheduler.o(i.scheduler_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = scheduler_run
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrFunction
</UL>

<P><STRONG><a name="[168]"></a>sd_block_read</STRONG> (Thumb, 538 bytes, Stack size 40 bytes, sdcard.o(i.sd_block_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = sd_block_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[16b]"></a>sd_block_write</STRONG> (Thumb, 784 bytes, Stack size 56 bytes, sdcard.o(i.sd_block_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = sd_block_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_write
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[166]"></a>sd_bus_mode_config</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, sdcard.o(i.sd_bus_mode_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = sd_bus_mode_config &rArr; sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[163]"></a>sd_card_information_get</STRONG> (Thumb, 686 bytes, Stack size 12 bytes, sdcard.o(i.sd_card_information_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sd_card_information_get
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[1b1]"></a>sd_card_init</STRONG> (Thumb, 268 bytes, Stack size 16 bytes, sdcard.o(i.sd_card_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_card_init &rArr; r6_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r2_error_check
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_power_state_get
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[164]"></a>sd_card_select_deselect</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, sdcard.o(i.sd_card_select_deselect))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_card_select_deselect &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[165]"></a>sd_cardstatus_get</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, sdcard.o(i.sd_cardstatus_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_cardstatus_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[162]"></a>sd_init</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, sdcard.o(i.sd_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = sd_init &rArr; sd_power_on &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_config
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[118]"></a>sd_interrupts_process</STRONG> (Thumb, 286 bytes, Stack size 8 bytes, sdcard.o(i.sd_interrupts_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_interrupts_process &rArr; sd_transfer_stop &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_flag_get
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_flag_clear
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_disable
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_IRQHandler
</UL>

<P><STRONG><a name="[169]"></a>sd_multiblocks_read</STRONG> (Thumb, 672 bytes, Stack size 48 bytes, sdcard.o(i.sd_multiblocks_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = sd_multiblocks_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[16c]"></a>sd_multiblocks_write</STRONG> (Thumb, 898 bytes, Stack size 56 bytes, sdcard.o(i.sd_multiblocks_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_write
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[1b4]"></a>sd_power_on</STRONG> (Thumb, 290 bytes, Stack size 24 bytes, sdcard.o(i.sd_power_on))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = sd_power_on &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r7_error_check
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r3_error_check
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_power_state_set
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_enable
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[167]"></a>sd_transfer_mode_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, sdcard.o(i.sd_transfer_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[1b6]"></a>sd_transfer_stop</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sdcard.o(i.sd_transfer_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = sd_transfer_stop &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[1ae]"></a>sdio_bus_mode_set</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_bus_mode_set))
<BR><BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[1ad]"></a>sdio_clock_config</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, gd32f4xx_sdio.o(i.sdio_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sdio_clock_config
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[1ba]"></a>sdio_clock_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[183]"></a>sdio_command_index_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_command_index_get))
<BR><BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>

<P><STRONG><a name="[1a2]"></a>sdio_command_response_config</STRONG> (Thumb, 52 bytes, Stack size 12 bytes, gd32f4xx_sdio.o(i.sdio_command_response_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sdio_command_response_config
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1a4]"></a>sdio_csm_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_csm_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[19d]"></a>sdio_data_config</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, gd32f4xx_sdio.o(i.sdio_data_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = sdio_data_config
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1a6]"></a>sdio_data_read</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_read))
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[19e]"></a>sdio_data_transfer_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_transfer_config))
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1aa]"></a>sdio_data_write</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_write))
<BR><BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[1b3]"></a>sdio_deinit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_sdio.o(i.sdio_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = sdio_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[1a0]"></a>sdio_dma_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dma_disable))
<BR><BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1a8]"></a>sdio_dma_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dma_enable))
<BR><BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[19f]"></a>sdio_dsm_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dsm_disable))
<BR><BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1a5]"></a>sdio_dsm_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dsm_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[14d]"></a>sdio_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r7_error_check
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r3_error_check
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r2_error_check
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[14c]"></a>sdio_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1af]"></a>sdio_hardware_clock_disable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_hardware_clock_disable))
<BR><BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[1b8]"></a>sdio_interrupt_disable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_disable))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[1a7]"></a>sdio_interrupt_enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1b7]"></a>sdio_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[1b5]"></a>sdio_interrupt_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[1b2]"></a>sdio_power_state_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_power_state_get))
<BR><BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[1b9]"></a>sdio_power_state_set</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_power_state_set))
<BR><BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[184]"></a>sdio_response_get</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_response_get))
<BR><BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1a3]"></a>sdio_wait_type_set</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_wait_type_set))
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1c3]"></a>spi_dma_disable</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_dma_disable))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
</UL>

<P><STRONG><a name="[1c2]"></a>spi_dma_enable</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_dma_enable))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_gd30ad3344_send_halfword_dma
</UL>

<P><STRONG><a name="[17f]"></a>spi_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_enable))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd30ad3344_init
</UL>

<P><STRONG><a name="[a0]"></a>spi_flash_buffer_read</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, spi_flash.o(i.spi_flash_buffer_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = spi_flash_buffer_read &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Cached_Log_To_Log0
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Multi_Channel_Config_From_Flash
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Config_From_Flash
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Cached_Log_In_Flash
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cache_Log_To_Flash
</UL>

<P><STRONG><a name="[a4]"></a>spi_flash_buffer_write</STRONG> (Thumb, 196 bytes, Stack size 32 bytes, spi_flash.o(i.spi_flash_buffer_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_Multi_Channel_Config_To_Flash
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_Config_To_Flash
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cache_Log_To_Flash
</UL>

<P><STRONG><a name="[c5]"></a>spi_flash_init</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, spi_flash.o(i.spi_flash_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = spi_flash_init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
</UL>

<P><STRONG><a name="[1bc]"></a>spi_flash_page_write</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, spi_flash.o(i.spi_flash_page_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>

<P><STRONG><a name="[c6]"></a>spi_flash_read_id</STRONG> (Thumb, 78 bytes, Stack size 24 bytes, spi_flash.o(i.spi_flash_read_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = spi_flash_read_id &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
</UL>

<P><STRONG><a name="[a2]"></a>spi_flash_sector_erase</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, spi_flash.o(i.spi_flash_sector_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = spi_flash_sector_erase &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Cached_Log_To_Log0
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_Multi_Channel_Config_To_Flash
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_Config_To_Flash
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cache_Log_To_Flash
</UL>

<P><STRONG><a name="[1bb]"></a>spi_flash_send_byte</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, spi_flash.o(i.spi_flash_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>

<P><STRONG><a name="[a3]"></a>spi_flash_wait_for_write_end</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, spi_flash.o(i.spi_flash_wait_for_write_end))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = spi_flash_wait_for_write_end &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Cached_Log_To_Log0
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_Multi_Channel_Config_To_Flash
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Save_Config_To_Flash
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_Init
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cache_Log_To_Flash
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>

<P><STRONG><a name="[1bd]"></a>spi_flash_write_enable</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, spi_flash.o(i.spi_flash_write_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = spi_flash_write_enable &rArr; spi_flash_send_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>

<P><STRONG><a name="[cd]"></a>spi_gd30ad3344_send_halfword_dma</STRONG> (Thumb, 268 bytes, Stack size 48 bytes, gd30ad3344.o(i.spi_gd30ad3344_send_halfword_dma))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = spi_gd30ad3344_send_halfword_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dma_enable
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dma_disable
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_mode_init
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd30ad3344_init
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD30AD3344_AD_Read
</UL>

<P><STRONG><a name="[1c0]"></a>spi_i2s_data_receive</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>

<P><STRONG><a name="[1bf]"></a>spi_i2s_data_transmit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>

<P><STRONG><a name="[1be]"></a>spi_i2s_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>

<P><STRONG><a name="[17e]"></a>spi_init</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_init))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd30ad3344_init
</UL>

<P><STRONG><a name="[124]"></a>systick_config</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, systick.o(i.systick_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = systick_config &rArr; NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[12c]"></a>timer_deinit</STRONG> (Thumb, 374 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Init
</UL>

<P><STRONG><a name="[130]"></a>timer_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_enable))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Init
</UL>

<P><STRONG><a name="[12d]"></a>timer_init</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_init))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Init
</UL>

<P><STRONG><a name="[12e]"></a>timer_interrupt_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Init
</UL>

<P><STRONG><a name="[126]"></a>timer_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_IRQHandler
</UL>

<P><STRONG><a name="[125]"></a>timer_interrupt_flag_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_IRQHandler
</UL>

<P><STRONG><a name="[18d]"></a>usart_baudrate_set</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, gd32f4xx_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
</UL>

<P><STRONG><a name="[132]"></a>usart_data_receive</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[17b]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[18c]"></a>usart_deinit</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
</UL>

<P><STRONG><a name="[196]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
</UL>

<P><STRONG><a name="[17c]"></a>usart_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_printf
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[193]"></a>usart_hardware_flow_cts_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_hardware_flow_cts_config))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
</UL>

<P><STRONG><a name="[194]"></a>usart_hardware_flow_rts_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_hardware_flow_rts_config))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
</UL>

<P><STRONG><a name="[195]"></a>usart_interrupt_enable</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
</UL>

<P><STRONG><a name="[133]"></a>usart_interrupt_flag_clear</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[131]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usart_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[18e]"></a>usart_parity_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_parity_config))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
</UL>

<P><STRONG><a name="[191]"></a>usart_receive_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
</UL>

<P><STRONG><a name="[190]"></a>usart_stop_bit_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_stop_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
</UL>

<P><STRONG><a name="[192]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
</UL>

<P><STRONG><a name="[18f]"></a>usart_word_length_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_word_length_set))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_usart1_config
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[1c6]"></a>NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, systick.o(i.NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
</UL>

<P><STRONG><a name="[14b]"></a>cmdsent_error_check</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, sdcard.o(i.cmdsent_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = cmdsent_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[16d]"></a>dma_receive_config</STRONG> (Thumb, 170 bytes, Stack size 64 bytes, sdcard.o(i.dma_receive_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_multi_data_mode_init
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flow_controller_config
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[175]"></a>dma_transfer_config</STRONG> (Thumb, 172 bytes, Stack size 64 bytes, sdcard.o(i.dma_transfer_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_multi_data_mode_init
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flow_controller_config
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[180]"></a>gpio_config</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, sdcard.o(i.gpio_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = gpio_config &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[182]"></a>r1_error_check</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, sdcard.o(i.r1_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_type_check
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[185]"></a>r1_error_type_check</STRONG> (Thumb, 174 bytes, Stack size 0 bytes, sdcard.o(i.r1_error_type_check))
<BR><BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>

<P><STRONG><a name="[186]"></a>r2_error_check</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, sdcard.o(i.r2_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = r2_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[187]"></a>r3_error_check</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, sdcard.o(i.r3_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = r3_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[188]"></a>r6_error_check</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, sdcard.o(i.r6_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = r6_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[189]"></a>r7_error_check</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, sdcard.o(i.r7_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = r7_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[18a]"></a>rcu_config</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sdcard.o(i.rcu_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rcu_config
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[1ac]"></a>sd_bus_width_config</STRONG> (Thumb, 242 bytes, Stack size 16 bytes, sdcard.o(i.sd_bus_width_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[1ab]"></a>sd_card_state_get</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, sdcard.o(i.sd_card_state_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = sd_card_state_get &rArr; sdio_command_response_config
</UL>
<BR>[Calls]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_type_check
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[1a1]"></a>sd_datablocksize_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, sdcard.o(i.sd_datablocksize_get))
<BR><BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1b0]"></a>sd_scr_get</STRONG> (Thumb, 366 bytes, Stack size 32 bytes, sdcard.o(i.sd_scr_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
</UL>
<BR>[Called By]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[ac]"></a>convert_to_bcd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, rtc_app.o(i.convert_to_bcd))
<BR><BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Configure_RTC_With_DateTime
</UL>

<P><STRONG><a name="[c2]"></a>Generate_Filename</STRONG> (Thumb, 62 bytes, Stack size 56 bytes, sdcard_app.o(i.Generate_Filename))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Generate_Filename &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Sample_File
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Sample_Data_MultiChannel
</UL>

<P><STRONG><a name="[c0]"></a>Generate_Log_Filename</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, sdcard_app.o(i.Generate_Log_Filename))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Generate_Log_Filename &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_Log_File
</UL>

<P><STRONG><a name="[af]"></a>bcd_to_decimal</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, usart_app.o(i.bcd_to_decimal))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Convert_RTC_To_Unix_Timestamp
</UL>

<P><STRONG><a name="[b1]"></a>get_days_in_month</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, usart_app.o(i.get_days_in_month))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = get_days_in_month
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_leap_year
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Convert_RTC_To_Unix_Timestamp
</UL>

<P><STRONG><a name="[b0]"></a>is_leap_year</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, usart_app.o(i.is_leap_year))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Convert_RTC_To_Unix_Timestamp
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_days_in_month
</UL>

<P><STRONG><a name="[1c5]"></a>system_clock_240m_25m_hxtal</STRONG> (Thumb, 250 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.system_clock_240m_25m_hxtal))
<BR><BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[122]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_240m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[147]"></a>check_fs</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, ff.o(i.check_fs))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = check_fs &rArr; disk_read &rArr; sd_multiblocks_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[155]"></a>chk_chr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, ff.o(i.chk_chr))
<BR><BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[149]"></a>chk_mounted</STRONG> (Thumb, 898 bytes, Stack size 80 bytes, ff.o(i.chk_mounted))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = chk_mounted &rArr; disk_initialize &rArr; sd_bus_mode_config &rArr; sd_bus_width_config &rArr; sd_scr_get &rArr; r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
</UL>

<P><STRONG><a name="[14e]"></a>cmp_lfn</STRONG> (Thumb, 138 bytes, Stack size 32 bytes, ff.o(i.cmp_lfn))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = cmp_lfn
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[150]"></a>create_chain</STRONG> (Thumb, 202 bytes, Stack size 32 bytes, ff.o(i.create_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[153]"></a>create_name</STRONG> (Thumb, 616 bytes, Stack size 56 bytes, ff.o(i.create_name))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = create_name &rArr; mem_set
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_chr
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[157]"></a>dir_find</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ff.o(i.dir_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cmp
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[15c]"></a>dir_next</STRONG> (Thumb, 280 bytes, Stack size 24 bytes, ff.o(i.dir_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[15e]"></a>dir_register</STRONG> (Thumb, 396 bytes, Stack size 56 bytes, ff.o(i.dir_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_numname
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fit_lfn
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[158]"></a>dir_sdi</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, ff.o(i.dir_sdi))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = dir_sdi &rArr; get_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[161]"></a>fit_lfn</STRONG> (Thumb, 122 bytes, Stack size 20 bytes, ff.o(i.fit_lfn))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = fit_lfn
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[178]"></a>follow_path</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, ff.o(i.follow_path))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
</UL>

<P><STRONG><a name="[15b]"></a>mem_cmp</STRONG> (Thumb, 38 bytes, Stack size 20 bytes, ff.o(i.mem_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = mem_cmp
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[15f]"></a>mem_cpy</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, ff.o(i.mem_cpy))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = mem_cpy
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_numname
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[156]"></a>mem_set</STRONG> (Thumb, 20 bytes, Stack size 12 bytes, ff.o(i.mem_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = mem_set
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[159]"></a>move_window</STRONG> (Thumb, 114 bytes, Stack size 24 bytes, ff.o(i.move_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[179]"></a>remove_chain</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, ff.o(i.remove_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = remove_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[15a]"></a>sum_sfn</STRONG> (Thumb, 32 bytes, Stack size 12 bytes, ff.o(i.sum_sfn))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sum_sfn
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[17a]"></a>sync</STRONG> (Thumb, 202 bytes, Stack size 16 bytes, ff.o(i.sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mkdir
</UL>

<P><STRONG><a name="[176]"></a>validate</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, ff.o(i.validate))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = validate
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_lseek
</UL>

<P><STRONG><a name="[140]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[13b]"></a>_printf_core</STRONG> (Thumb, 1744 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsnprintf
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0snprintf
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[143]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[142]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[6d]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 2]<UL><LI> printfa.o(i.__0vsnprintf)
<LI> printfa.o(i.__0snprintf)
</UL>
<P><STRONG><a name="[7b]"></a>_local_sscanf</STRONG> (Thumb, 54 bytes, Stack size 56 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
</UL>

<P><STRONG><a name="[8a]"></a>_fp_value</STRONG> (Thumb, 296 bytes, Stack size 64 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
