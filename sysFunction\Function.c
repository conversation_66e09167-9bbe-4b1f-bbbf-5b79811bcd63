#include "Function.h"
#include "Sdcard_APP.h"

char test[10]="TEST";
uint32_t uwTick;

void System_Init(void)
{
	systick_config();
}

void UsrFunction(void)
{
	OLED_Init();
	OLED_App_Init();
	Key_Init();
	Led_Init();
	//usart0_config();
	rs485_usart1_config();
	gd30ad3344_init();
	ADC_Init(); 
	scheduler_init();
	RTC_Init(); 
	Flash_Init();
	Init_Data_Recording();
	Create_Default_Config_INI();
	Write_Log_Data("system init");
	// 系统初始化时显示默认信息
	extern char oled1_buffer[128], oled2_buffer[128];
	strcpy(oled1_buffer, "System Init");
	strcpy(oled2_buffer, "Loading...");
	OLED_ShowString(0, 0, (unsigned char *)oled1_buffer, 16);
	OLED_ShowString(0, 16, (unsigned char *)oled2_buffer, 16);
	OLED_Refresh();
	delay_1ms(500);//等待系统稳定
	Timer_Init();
	while(1)
	{
		scheduler_run();
	}
}
