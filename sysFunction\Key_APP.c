#include "Key_APP.h"
#include "ADC_APP.h"
#include "Flash_APP.h"
#include "Sdcard_APP.h"

uint8_t key_val, key_down, key_up, key_old;
sample_period_t current_sample_period = SAMPLE_PERIOD_5S;

// 按键处理主函数
void Key_Proc(void)
{
	key_val = Key_Read();
	key_down = key_val & (key_val ^ key_old);
	key_up = ~key_val & (key_val ^ key_old);
	key_old = key_val;

	if(key_down & KEY1_VAL) Handle_Key1_Press();
	if(key_down & KEY2_VAL) Handle_Key2_Press();
	if(key_down & KEY3_VAL) Handle_Key3_Press();
	if(key_down & KEY4_VAL) Handle_Key4_Press();
	if(key_down & KEY5_VAL) Handle_Key5_Press();
	if(key_down & KEY6_VAL) Handle_Key6_Press();
}

// KEY1功能：显示Ch0原始数据
void Handle_Key1_Press(void)
{
	extern float ch0_data;
	extern char oled1_buffer[128], oled2_buffer[128];

	// 直接格式化显示Ch0原始数据
	strcpy(oled1_buffer, "Ch0 Raw Data");
	snprintf(oled2_buffer, sizeof(oled2_buffer), "%.4f V", ch0_data);

	// 立即更新OLED显示
	OLED_ShowString(0, 0, (unsigned char *)oled1_buffer, 16);
	OLED_ShowString(0, 16, (unsigned char *)oled2_buffer, 16);
	OLED_Refresh();

	Write_Log_Data("OLED display: Ch0 raw data (key1 press)");

	/* 原有功能（已注释）：启动/停止采集
	if(Get_ADC_State() == ADC_STATE_IDLE || Get_ADC_State() == ADC_STATE_STOPPED)
	{
		vol_flag_single = 1;
		//Start_Sampling();
		//rs485_printf("Periodic Sampling\r\n");
		//rs485_printf("sample cycle: %ds\r\n", current_sample_period);
		char start_msg[60];
		snprintf(start_msg, sizeof(start_msg), "sample start - cycle %ds (key press)", current_sample_period);
		Write_Log_Data(start_msg);
	}
	else
	{
		Stop_Sampling();
		//rs485_printf("Periodic Sampling STOP\r\n");
		Write_Log_Data("sample stop (key press)");
	}
	*/
}

// KEY2功能：显示Ch1原始数据
void Handle_Key2_Press(void)
{
	extern float ch1_data;
	extern char oled1_buffer[128], oled2_buffer[128];

	// 直接格式化显示Ch1原始数据
	strcpy(oled1_buffer, "Ch1 Raw Data");
	snprintf(oled2_buffer, sizeof(oled2_buffer), "%.4f A", ch1_data);

	// 立即更新OLED显示
	OLED_ShowString(0, 0, (unsigned char *)oled1_buffer, 16);
	OLED_ShowString(0, 16, (unsigned char *)oled2_buffer, 16);
	OLED_Refresh();

	Write_Log_Data("OLED display: Ch1 raw data (key2 press)");

	/* 原有功能（已注释）：设置采样周期为5秒
	Set_Sample_Period(SAMPLE_PERIOD_5S);
	Write_Log_Data("cycle switch to 5s (key press)");
	*/
}

// KEY3功能：显示Ch2原始数据
void Handle_Key3_Press(void)
{
	extern float ch2_data;
	extern char oled1_buffer[128], oled2_buffer[128];

	// 直接格式化显示Ch2原始数据
	strcpy(oled1_buffer, "Ch2 Raw Data");
	snprintf(oled2_buffer, sizeof(oled2_buffer), "%.4f Ω", ch2_data);

	// 立即更新OLED显示
	OLED_ShowString(0, 0, (unsigned char *)oled1_buffer, 16);
	OLED_ShowString(0, 16, (unsigned char *)oled2_buffer, 16);
	OLED_Refresh();

	Write_Log_Data("OLED display: Ch2 raw data (key3 press)");

	/* 原有功能（已注释）：设置采样周期为10秒
	Set_Sample_Period(SAMPLE_PERIOD_10S);
	Write_Log_Data("cycle switch to 10s (key press)");
	*/
}

// KEY4功能：显示Ch0变比后数据
void Handle_Key4_Press(void)
{
	extern float ch0_data, ch0_ratio;
	extern char oled1_buffer[128], oled2_buffer[128];

	// 直接格式化显示Ch0变比后数据
	strcpy(oled1_buffer, "Ch0 Ratio Data");
	snprintf(oled2_buffer, sizeof(oled2_buffer), "%.4f V", ch0_data * ch0_ratio);

	// 立即更新OLED显示
	OLED_ShowString(0, 0, (unsigned char *)oled1_buffer, 16);
	OLED_ShowString(0, 16, (unsigned char *)oled2_buffer, 16);
	OLED_Refresh();

	Write_Log_Data("OLED display: Ch0 ratio data (key4 press)");

	/* 原有功能（已注释）：设置采样周期为15秒
	Set_Sample_Period(SAMPLE_PERIOD_15S);
	Write_Log_Data("cycle switch to 15s (key press)");
	*/
}


// KEY5功能：显示Ch1变比后数据
void Handle_Key5_Press()
{
	extern float ch1_data, ch1_ratio;
	extern char oled1_buffer[128], oled2_buffer[128];

	// 直接格式化显示Ch1变比后数据
	strcpy(oled1_buffer, "Ch1 Ratio Data");
	snprintf(oled2_buffer, sizeof(oled2_buffer), "%.4f A", ch1_data * ch1_ratio);

	// 立即更新OLED显示
	OLED_ShowString(0, 0, (unsigned char *)oled1_buffer, 16);
	OLED_ShowString(0, 16, (unsigned char *)oled2_buffer, 16);
	OLED_Refresh();

	Write_Log_Data("OLED display: Ch1 ratio data (key5 press)");
}

// KEY6功能：显示Ch2变比后数据
void Handle_Key6_Press()
{
	extern float ch2_data, ch2_ratio;
	extern char oled1_buffer[128], oled2_buffer[128];

	// 直接格式化显示Ch2变比后数据
	strcpy(oled1_buffer, "Ch2 Ratio Data");
	snprintf(oled2_buffer, sizeof(oled2_buffer), "%.4f Ω", ch2_data * ch2_ratio);

	// 立即更新OLED显示
	OLED_ShowString(0, 0, (unsigned char *)oled1_buffer, 16);
	OLED_ShowString(0, 16, (unsigned char *)oled2_buffer, 16);
	OLED_Refresh();

	Write_Log_Data("OLED display: Ch2 ratio data (key6 press)");
}




void Set_Sample_Period(sample_period_t period)
{
	current_sample_period = period;
	// 设置ADC采样间隔，并转换为毫秒
	uint32_t interval_ms = period * 1000;
	Set_Sample_Interval(interval_ms);

	//保存配置到Flash
	float current_ratio = Get_Ratio(); 
	float current_limit = Get_Limit();
	Save_Config_To_Flash(current_ratio, current_limit, interval_ms);

	rs485_printf("sample cycle adjust: %ds\r\n", period);
}

sample_period_t Get_Sample_Period(void)
{
	return current_sample_period;
}

