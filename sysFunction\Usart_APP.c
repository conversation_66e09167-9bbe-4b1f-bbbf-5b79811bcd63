#include "Usart_APP.h"
#include "Oled_APP.h"
#include "RTC_APP.h"
#include "Flash_APP.h"
#include "ADC_APP.h"
#include "Sdcard_APP.h"
#include "Sdcard_APP.h"
#include "ff.h"
#include "diskio.h"

// 设备ID变量定义
uint16_t MYDEVICE_ID = 0x0001;

extern uint32_t flash_id;
extern FIL fdst;
extern FATFS fs;
extern UINT br, bw;
extern BYTE buffer[128];
extern BYTE filebuffer[128];

// RTC配置状态
typedef enum {
    RTC_CONFIG_IDLE = 0,      // 空闲状态
    RTC_CONFIG_WAITING_INPUT  // 等待输入日期时间
} rtc_config_state_t;

// Ratio配置状态
typedef enum {
    RATIO_CONFIG_IDLE = 0,    // 空闲状态
    RATIO_CONFIG_WAITING      // 等待输入变比值
} ratio_config_state_t;

// 当前Ratio配置状态
static ratio_config_state_t ratio_config_state = RATIO_CONFIG_IDLE;

// Limit配置状态
typedef enum {
    LIMIT_CONFIG_IDLE = 0,    // 空闲状态
    LIMIT_CONFIG_WAITING      // 等待输入阈值
} limit_config_state_t;

// 当前Limit配置状态
static limit_config_state_t limit_config_state = LIMIT_CONFIG_IDLE;

// 当前输出模式
output_mode_t current_output_mode = OUTPUT_MODE_NORMAL;


//RS485解析
void RS485_Task(void)
{
    if((rs485_rx_index > 0 && (uwTick - rs485_rx_ticks >= 20)) || rs485_rx_idle_flag)
    {
        rs485_rx_idle_flag = 0;
        rs485_rx_buffer[rs485_rx_index] = '\0';

        if(rs485_rx_index > 0)
        {
            if(rs485_rx_index >= 4 && strncmp((char*)rs485_rx_buffer, "test", 4) == 0)
            {
                //Handle_Test_Command();
            }
            else if(rs485_rx_index >= 23 && strcmp((char*)rs485_rx_buffer, "command:get_device_id\r\n") == 0)
            {
                Handle_Get_Device_ID_Command();
            }
            else if(rs485_rx_index >= 16 && strncmp((char*)rs485_rx_buffer, "command:set_RTC=", 16) == 0)
            {
                Handle_Command_Set_RTC((char*)rs485_rx_buffer, rs485_rx_index);
            }
            else if(rs485_rx_index >= 15 && strcmp((char*)rs485_rx_buffer, "command:get_RTC\r\n") == 0)
            {
                Handle_RTC_Now_Command();
            }
            else if(rs485_rx_index >= 18 && strncmp((char*)rs485_rx_buffer, "command:set_ratio:", 18) == 0)
            {
                Handle_Command_Set_Ratio((char*)rs485_rx_buffer, rs485_rx_index);
            }
            else if(rs485_rx_index >= 15 && strcmp((char*)rs485_rx_buffer, "command:get_ratio\r\n") == 0)
            {
                Handle_Get_Ratio_Command();
            }
            else if(rs485_rx_index >= 17 && strcmp((char*)rs485_rx_buffer, "command:get_data\r\n") == 0)
            {
                Handle_Get_Data_Command();
            }
            else if(rs485_rx_index >= 18 && strncmp((char*)rs485_rx_buffer, "command:set_limit:", 18) == 0)
            {
                Handle_Command_Set_Limit((char*)rs485_rx_buffer, rs485_rx_index);
            }
            else if(rs485_rx_index >= 18 && strcmp((char*)rs485_rx_buffer, "command:get_limit\r\n") == 0)
            {
                Handle_Command_Get_Limit();
            }
            else if(rs485_rx_index >= 22 && strncmp((char*)rs485_rx_buffer, "command:start_sample\r\n", 22) == 0)
            {
                Handle_Start_Command();
            }
            else if(rs485_rx_index >= 21 && strncmp((char*)rs485_rx_buffer, "command:stop_sample\r\n", 21) == 0)
            {
                Handle_Stop_Command();
            }
            else if(rs485_rx_index >= 11 && strncmp((char*)rs485_rx_buffer, "config save", 11) == 0)
            {
                //Handle_Config_Save_Command();
            }
            else if(rs485_rx_index >= 11 && strncmp((char*)rs485_rx_buffer, "config read", 11) == 0)
            {
                //Handle_Config_Read_Command();
            }
			 else if(rs485_rx_index >= 17 && strncmp((char*)rs485_rx_buffer, "multi config save", 17) == 0)
            {
                Handle_Multi_Config_Save_Command();
            }
            else if(rs485_rx_index >= 17 && strncmp((char*)rs485_rx_buffer, "multi config read", 17) == 0)
            {
                Handle_Multi_Config_Read_Command();
            }
            else if(rs485_rx_index >= 4 && strncmp((char*)rs485_rx_buffer, "conf", 4) == 0)
            {
                //Handle_Conf_Command();
            }
            else if(rs485_rx_index >= 4 && strncmp((char*)rs485_rx_buffer, "hide", 4) == 0)
            {
                //Handle_Hide_Command();
            }
            else if(rs485_rx_index >= 6 && strncmp((char*)rs485_rx_buffer, "unhide", 6) == 0)
            {
                //Handle_Unhide_Command();
            }
            else if(ratio_config_state == RATIO_CONFIG_WAITING)
            {
                //Parse_Ratio_Input((char*)rs485_rx_buffer, rs485_rx_index);
            }
            else if(limit_config_state == LIMIT_CONFIG_WAITING)
            {
                //Parse_Limit_Input((char*)rs485_rx_buffer, rs485_rx_index);
            }

            memset(rs485_rx_buffer, 0, rs485_rx_index);
            rs485_rx_index = 0;
        }
    }
}

// 设备ID管理函数
void Set_Device_ID(uint16_t device_id)
{
    MYDEVICE_ID = device_id;
}

uint16_t Get_Device_ID(void)
{
    return MYDEVICE_ID;
}

// 处理"get_device_id"命令的函数
void Handle_Get_Device_ID_Command(void)
{
    // 上报设备ID
    rs485_printf("report:device_id=0x%04X\r\n", MYDEVICE_ID);

    // 可选：记录日志
    //Write_Log_Data("device id requested");
}

// 处理"test"命令的函数
void Handle_Test_Command(void)
{
    Write_Log_Data("system hardware test");
	
    rs485_printf("========system selftest========\r\n");
    rs485_printf("flash............ok\r\n");
	
    uint8_t tf_card_status = Check_TF_Card();

    if(tf_card_status == 1)
    {
        rs485_printf("TF card............ok\r\n");
        Write_Log_Data("test ok");
    }
    else
    {
        rs485_printf("TF card............error\r\n");
        rtc_parameter_struct rtc_time;
        rtc_current_time_get(&rtc_time);
        char test_error_log[80];
        snprintf(test_error_log, sizeof(test_error_log),
                "20%02x-%02x-%02x %02x:%02x:%02x test error: tf card not found\r\n",
                rtc_time.year, rtc_time.month, rtc_time.date,
                rtc_time.hour, rtc_time.minute, rtc_time.second);
        Cache_Log_To_Flash(test_error_log);
    }

    rs485_printf("flash ID: 0x%X\r\n", flash_id & 0xFFFFFF);

    if(tf_card_status == 1)
    {
        uint32_t capacity = sd_card_capacity_get();
        rs485_printf("TF card memory: %d kB\r\n", capacity);
    }
    else
    {
        rs485_printf("can not find TF card\r\n");
    }

    // show_rtc_time_for_test();
    rtc_parameter_struct rtc_initpara;
    rtc_current_time_get(&rtc_initpara);

    rs485_printf("RTC: 20%02x-%02x-%02x %02x:%02x:%02x\r\n",
           rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
           rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);

    rs485_printf("========system selftest========\r\n");
}

// 处理"command:set_RTC="命令的函数
void Handle_Command_Set_RTC(char* input, uint16_t length)
{
    // 命令格式: command:set_RTC=YYYY-MM-DD HH:MM:SS
    // 检查长度是否足够
    if(length < 35)  // "command:set_RTC=" (16字符) + "YYYY-MM-DD HH:MM:SS" (19字符)
    {
//        rs485_printf("report:error=invalid_format\r\n");
        return;
    }
    
    // 提取日期时间部分
    char datetime_str[20];
    strncpy(datetime_str, input + 16, 19);  // 跳过"command:set_RTC="
    datetime_str[19] = '\0';
    
    uint16_t datetime[6] = {0}; // 年,月,日,时,分,秒
    
    // 检查日期时间格式
    if(datetime_str[4] != '-' || datetime_str[7] != '-' || datetime_str[10] != ' ' ||
       datetime_str[13] != ':' || datetime_str[16] != ':')
    {
//        rs485_printf("report:error=invalid_format\r\n");
        return;
    }
    
    // 临时修改字符串以便于解析
    char temp_str[20];
    strcpy(temp_str, datetime_str);
    temp_str[4] = '\0';
    temp_str[7] = '\0';
    temp_str[10] = '\0';
    temp_str[13] = '\0';
    temp_str[16] = '\0';
    
    // 解析各个部分
    datetime[0] = atoi(&temp_str[0]);   // 年
    datetime[1] = atoi(&temp_str[5]);   // 月
    datetime[2] = atoi(&temp_str[8]);   // 日
    datetime[3] = atoi(&temp_str[11]);  // 时
    datetime[4] = atoi(&temp_str[14]);  // 分
    datetime[5] = atoi(&temp_str[17]);  // 秒
    
    // 验证日期时间值的合法性
    if(datetime[0] < 2000 || datetime[0] > 2099 ||
       datetime[1] < 1 || datetime[1] > 12 ||
       datetime[2] < 1 || datetime[2] > 31 ||
       datetime[3] > 23 || datetime[4] > 59 || datetime[5] > 59)
    {
//        rs485_printf("report:error=invalid_value\r\n");
        return;
    }
    
    // 配置RTC
    uint8_t rtc_config_result = Configure_RTC_With_DateTime(datetime);
    
    if(rtc_config_result == 0)
    {
        // 配置成功，返回"report:ok"
        rs485_printf("report:ok\r\n");
        
        // 记录日志
        rtc_parameter_struct rtc_time;
        rtc_current_time_get(&rtc_time);
        
        char time_msg[100];
        snprintf(time_msg, sizeof(time_msg),
                "RTC set to 20%02x-%02x-%02x %02x:%02x:%02x via command",
                rtc_time.year, rtc_time.month, rtc_time.date,
                rtc_time.hour, rtc_time.minute, rtc_time.second);
        
        Write_Log_Data(time_msg);
    }
    else
    {
        // 配置失败
    //    rs485_printf("report:error=rtc_config_failed\r\n");
    }
}

// 处理"command:get_RTC"命令的函数
void Handle_RTC_Now_Command(void)
{
    rtc_parameter_struct rtc_time;
    rtc_current_time_get(&rtc_time);

    rs485_printf("report:CurrentTime=20%02x-%02x-%02x %02x:%02x:%02x\r\n",
           rtc_time.year, rtc_time.month, rtc_time.date,
           rtc_time.hour, rtc_time.minute, rtc_time.second);
}

// 处理"command:set_ratio"命令的函数
void Handle_Command_Set_Ratio(char* input, uint16_t length)
{
    // 提取参数部分
    char* params = input + 18;  // 跳过"command:set_ratio:"
    
    // 解析三个通道的变比值
    char* ch0_str = strstr(params, "ch0=");
    char* ch1_str = strstr(params, "ch1=");
    char* ch2_str = strstr(params, "ch2=");
    
    // 解析ch0
    if(ch0_str)
    {
        ch0_ratio = atof(ch0_str + 4);  // 跳过"ch0="
    }
    
    // 解析ch1
    if(ch1_str)
    {
        ch1_ratio = atof(ch1_str + 4);  // 跳过"ch1="
    }
    
    // 解析ch2
    if(ch2_str)
    {
        ch2_ratio = atof(ch2_str + 4);  // 跳过"ch2="
    }
    
    // 验证变比值的合法性（假设变比范围为0.1到100）
    if(ch0_ratio < 0.1f || ch0_ratio > 100.0f ||
       ch1_ratio < 0.1f || ch1_ratio > 100.0f ||
       ch2_ratio < 0.1f || ch2_ratio > 100.0f)
    {
        rs485_printf("report:error=invalid_ratio_value\r\n");
        return;
    }
    
    // 设置系统状态为配置模式
    Set_System_State(SYSTEM_STATE_CONFIG);

    // 保存配置到Flash (只保存ch0的变比到系统)
    //Save_Config_To_Flash(ch0_ratio, Get_Limit(), Get_Sample_Interval());

    // 保存配置到config.ini (保存所有通道的变比)
    Update_Config_INI(ch0_ratio, ch1_ratio, ch2_ratio,ch0_limit, ch1_limit, ch2_limit);

    // 恢复系统状态
    Set_System_State(SYSTEM_STATE_IDLE);
    
    // 返回成功响应
    rs485_printf("report:ok\r\n");
    
    // 记录日志
    char log_msg[100];
    snprintf(log_msg, sizeof(log_msg), 
            "ratio config updated: ch0=%.2f, ch1=%.2f, ch2=%.2f", 
            ch0_ratio, ch1_ratio, ch2_ratio);
    Write_Log_Data(log_msg);
}

// 处理"command:get_ratio"命令的函数
void Handle_Get_Ratio_Command(void)
{
    // 上报变比值
    rs485_printf("report:ch0ratio=%.2f,ch1ratio=%.2f,ch2ratio=%.2f\r\n",
                 ch0_ratio, ch1_ratio, ch2_ratio);

    // 可选：记录日志
    char log_msg[100];
    snprintf(log_msg, sizeof(log_msg), 
            "ch0ratio = %.2f, ch1ratio = %.2f, ch2ratio = %.2f", 
            ch0_ratio, ch1_ratio, ch2_ratio);
    Write_Log_Data(log_msg);
}

void Handle_Get_Data_Command()
{
    if(read_mode == 0)
        vol_flag_single = 1;
    else if(read_mode == 1)
        intensity_flag_single = 1;
    else if(read_mode == 2)
        res_flag_single = 1;
    // 检查各通道是否超阈值
    uint8_t ch0_over = (ch0_data > ch0_limit) ? 1 : 0;
    uint8_t ch1_over = (ch1_data > ch1_limit) ? 1 : 0;
    uint8_t ch2_over = (ch2_data > ch2_limit) ? 1 : 0;
    
    // 动态生成响应字符串
    rs485_printf("report:ch0%s=%.2f,ch1%s=%.2f,ch2%s=%.2f\r\n",
                 ch0_over ? "*" : "",  ch0_data * ch0_ratio,
                 ch1_over ? "*" : "",  ch1_data * ch1_ratio,
                 ch2_over ? "*" : "",  ch2_data * ch2_ratio);
}

// 处理"command:start_sample"命令的函数
void Handle_Start_Command(void)
{
    Start_Sampling();

    // 记录启动日志
    // uint32_t cycle_seconds = sample_interval / 1000;
    // char start_msg[60];
    // snprintf(start_msg, sizeof(start_msg), "sample start - cycle %ds (command)", cycle_seconds);
    // Write_Log_Data(start_msg);

    // 注意：不在这里发送响应，响应由定时器采样函数发送
    // 定时器会按照采样间隔发送: report:YYYY-MM-DD HH:MM:SS ch0=xx.xx,ch1=xx.xx,ch2=xx.xx
}

// 处理"stop"命令的函数
void Handle_Stop_Command(void)
{
    Stop_Sampling();
    rs485_printf("report:ok\r\n");

    Write_Log_Data("sample stop (command)");

    // 停止采样后保持当前显示状态，不强制清空
}

// 处理"config save"命令的函数
void Handle_Config_Save_Command(void)
{
    Set_System_State(SYSTEM_STATE_CONFIG);

    float current_ratio = Get_Ratio();
    float current_limit = Get_Limit();

    rs485_printf("ratio: %.1f\r\n", current_ratio);
    rs485_printf("limit: %.2f\r\n", current_limit);
    rs485_printf("save parameters to flash\r\n");

    uint32_t current_interval = Get_Sample_Interval();
    uint8_t save_result = Save_Config_To_Flash(current_ratio, current_limit, current_interval);

    if(!save_result)
    {
        rs485_printf("Config save failed\r\n");
    }

    Set_System_State(SYSTEM_STATE_IDLE);
}

// 处理"config read"命令的函数
void Handle_Config_Read_Command(void)
{
    Set_System_State(SYSTEM_STATE_CONFIG);

    float flash_ratio = 0.0f;
    float flash_limit = 0.0f;
    uint32_t flash_interval = 5000;
    uint8_t read_result = Read_Config_From_Flash(&flash_ratio, &flash_limit, &flash_interval);

    rs485_printf("read parameters from flash\r\n");

    if(read_result)
    {
        Set_Ratio(flash_ratio);
        Set_Limit(flash_limit);
        Set_Sample_Interval(flash_interval);
        rs485_printf("ratio: %.1f\r\n", flash_ratio);
        rs485_printf("limit: %.2f\r\n", flash_limit);
    }
    else
	{
        rs485_printf("ratio: %.1f\r\n", Get_Ratio());
        rs485_printf("limit: %.2f\r\n", Get_Limit());
    }

    Set_System_State(SYSTEM_STATE_IDLE);
}

// 处理"command_set_limit"命令的函数
void Handle_Command_Set_Limit(char* input, uint16_t length)
{
    char* params = input + 18;  // 跳过"command:set_limit:"
    
    // 解析三个通道的阈值
    char* ch0_str = strstr(params, "ch0=");
    char* ch1_str = strstr(params, "ch1=");
    char* ch2_str = strstr(params, "ch2=");
    
    // 解析ch0
    if(ch0_str)
    {
        ch0_limit = atof(ch0_str + 4);  // 跳过"ch0="
    }
    
    // 解析ch1
    if(ch1_str)
    {
        ch1_limit = atof(ch1_str + 4);  // 跳过"ch1="
    }
    
    // 解析ch2
    if(ch2_str)
    {
        ch2_limit = atof(ch2_str + 4);  // 跳过"ch2="
    }
    
    // 验证阈值的合法性（范围0.0到200.0）
    if(ch0_limit < 0.0f || ch0_limit > 200.0f ||
       ch1_limit < 0.0f || ch1_limit > 200.0f ||
       ch2_limit < 0.0f || ch2_limit > 200.0f)
    {
        rs485_printf("report:error=invalid_limit_value\r\n");
        return;
    }
    
    // 设置系统状态为配置模式
    Set_System_State(SYSTEM_STATE_CONFIG);

    // 保存配置到Flash (只保存ch0的阈值到系统)
    //Save_Config_To_Flash(Get_Ratio(), ch0_limit, Get_Sample_Interval());

    // 保存配置到config.ini
    Update_Config_INI(ch0_ratio, ch1_ratio, ch2_ratio, ch0_limit, ch1_limit, ch2_limit);

    // 恢复系统状态
    Set_System_State(SYSTEM_STATE_IDLE);
    
    // 返回成功响应
    rs485_printf("report:ok\r\n");
    
    // 记录日志
    char log_msg[100];
    snprintf(log_msg, sizeof(log_msg), 
            "limit config updated: ch0=%.2f, ch1=%.2f, ch2=%.2f", 
            ch0_limit, ch1_limit, ch2_limit);
    Write_Log_Data(log_msg);
}

//处理command_get_limit指令
void Handle_Command_Get_Limit()
{
    rs485_printf("report:ch0limit=%.2f,ch1limit=%.2f,ch2limit=%.2f\r\n",
        ch0_limit, ch1_limit, ch2_limit);

}

// BCD转十进制辅助函数
static uint8_t bcd_to_decimal(uint8_t bcd_value)
{
    return ((bcd_value >> 4) & 0x0F) * 10 + (bcd_value & 0x0F);
}

// 判断是否为闰年
static uint8_t is_leap_year(uint16_t year)
{
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
}

// 获取指定月份的天数
static uint8_t get_days_in_month(uint8_t month, uint16_t year)
{
    static const uint8_t days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    if(month == 2 && is_leap_year(year)) {
        return 29;
    }
    return days_in_month[month - 1];
}

// 将RTC时间转换为Unix时间戳
uint32_t Convert_RTC_To_Unix_Timestamp(rtc_parameter_struct* rtc_time)
{
    if(rtc_time == NULL) return 0;

    uint16_t year = 2000 + bcd_to_decimal(rtc_time->year);
    uint8_t month = bcd_to_decimal(rtc_time->month);
    uint8_t day = bcd_to_decimal(rtc_time->date);
    uint8_t hour = bcd_to_decimal(rtc_time->hour);
    uint8_t minute = bcd_to_decimal(rtc_time->minute);
    uint8_t second = bcd_to_decimal(rtc_time->second);

    if(year < 1970 || month < 1 || month > 12 || day < 1 || day > 31 ||
       hour > 23 || minute > 59 || second > 59)
    {
        return 0;
    }

    uint32_t days = 0;

    for(uint16_t y = 1970; y < year; y++)
    {
        days += is_leap_year(y) ? 366 : 365;
    }

    for(uint8_t m = 1; m < month; m++)
    {
        days += get_days_in_month(m, year);
    }

    days += (day - 1);

    uint32_t unix_timestamp = days * 86400UL + hour * 3600UL + minute * 60UL + second;

    if(unix_timestamp >= 28800)
    {
        unix_timestamp -= 28800;
    }

    return unix_timestamp;
}

// 将电压值编码为4字节HEX格式
uint32_t Encode_Voltage_To_Hex(float voltage)
{
    if(voltage < 0.0f || voltage > 655.35f)
    {
        return 0;
    }

    uint16_t integer_part = (uint16_t)voltage;
    float fractional_part = voltage - (float)integer_part;

    uint16_t frac_encoded = (uint16_t)(fractional_part * 65536.0f + 0.5f);

    uint32_t encoded_value = ((uint32_t)integer_part << 16) | frac_encoded;

    return encoded_value;
}

// 将32位值转换为8位HEX字符串
void Convert_Uint32_To_Hex_String(uint32_t value, char* hex_str)
{
    if(hex_str == NULL) return;

    for(uint8_t i = 0; i < 8; i++)
    {
        uint8_t nibble = (value >> (28 - i * 4)) & 0x0F;

        if(nibble < 10)
        {
            hex_str[i] = '0' + nibble;
        }
        else
        {
            hex_str[i] = 'A' + (nibble - 10);
        }
    }

    hex_str[8] = '\0';
}

// 处理"hide"命令的函数
void Handle_Hide_Command(void)
{
    Write_Log_Data("hide data");
    current_output_mode = OUTPUT_MODE_ENCRYPTED;
}

// 处理"unhide"命令的函数
void Handle_Unhide_Command(void)
{
    Write_Log_Data("unhide data");
    current_output_mode = OUTPUT_MODE_NORMAL;
    Close_HideData_File();
}

// 生成加密格式输出的函数（正常数据）
void Generate_Encrypted_Output_With_Values(rtc_parameter_struct* rtc_time, float actual_voltage)
{
    if(rtc_time == NULL) return;

    uint32_t timestamp = Convert_RTC_To_Unix_Timestamp(rtc_time);
    uint32_t voltage_hex = Encode_Voltage_To_Hex(actual_voltage);

    char timestamp_str[9], voltage_str[9];
    Convert_Uint32_To_Hex_String(timestamp, timestamp_str);
    Convert_Uint32_To_Hex_String(voltage_hex, voltage_str);

    char full_hex_str[17];
    snprintf(full_hex_str, sizeof(full_hex_str), "%s%s", timestamp_str, voltage_str);
    printf("%s\r\n", full_hex_str);

    char timestamp_readable[32];
    snprintf(timestamp_readable, sizeof(timestamp_readable), "20%02x-%02x-%02x %02x:%02x:%02x",
             rtc_time->year, rtc_time->month, rtc_time->date,
             rtc_time->hour, rtc_time->minute, rtc_time->second);
    Write_HideData(timestamp_readable, actual_voltage, full_hex_str);
}

// 生成加密格式输出的函数（超限版本，末尾加*）
void Generate_Encrypted_Output_With_Values_OverLimit(rtc_parameter_struct* rtc_time, float actual_voltage)
{
    if(rtc_time == NULL) return;

    uint32_t timestamp = Convert_RTC_To_Unix_Timestamp(rtc_time);
    uint32_t voltage_hex = Encode_Voltage_To_Hex(actual_voltage);

    char timestamp_str[9], voltage_str[9];
    Convert_Uint32_To_Hex_String(timestamp, timestamp_str);
    Convert_Uint32_To_Hex_String(voltage_hex, voltage_str);
    printf("%s%s*\r\n", timestamp_str, voltage_str);
}

// 处理"conf"命令的函数
uint8_t Handle_Conf_Command(void)
{
    FIL config_file;
    FRESULT result;
    char buffer[512];
    UINT br_local;

    result = f_open(&config_file, "0:/config.ini", FA_READ);
    if(result != FR_OK)
    {
        rs485_printf("config.ini file not found.\r\n");
        return 1;
    }

    result = f_read(&config_file, buffer, sizeof(buffer)-1, &br_local);
    f_close(&config_file);

    if(result != FR_OK)
    {
        return 1;
    }

    buffer[br_local] = '\0';

    float file_ratio = 0.0f;
    float file_limit = 0.0f;
    char* line = strtok(buffer, "\r\n");
    uint8_t in_ratio_section = 0;
    uint8_t in_limit_section = 0;

    while(line != NULL)
    {
        while(*line == ' ' || *line == '\t') line++;

        if(strlen(line) == 0)
        {
            line = strtok(NULL, "\r\n");
            continue;
        }

        if(strstr(line, "[Ratio]") != NULL)
        {
            in_ratio_section = 1;
            in_limit_section = 0;
        }
        else if(strstr(line, "[Limit]") != NULL)
        {
            in_ratio_section = 0;
            in_limit_section = 1;
        }
        else if(strstr(line, "Ch0 = ") != NULL)
        {
            char* value_str = strstr(line, "= ") + 2;
            float value = atof(value_str);

            if(in_ratio_section)
            {
                file_ratio = value;
            }
            else if(in_limit_section)
            {
                file_limit = value;
            }
        }

        line = strtok(NULL, "\r\n");
    }

    Set_Ratio(file_ratio);
    Set_Limit(file_limit);

    rs485_printf("Ratio = %.2f\r\n", file_ratio);
    rs485_printf("\r\nLimit = %.2f\r\n", file_limit);
    rs485_printf("\r\nconfig read success\r\n");

    return 0;
}

// ==================== 三通道配置Flash存储命令处理函数 ====================

// 处理"multi config save"命令的函数
void Handle_Multi_Config_Save_Command(void)
{
    Set_System_State(SYSTEM_STATE_CONFIG);

    // 获取当前三通道配置参数
    extern float ch0_ratio, ch1_ratio, ch2_ratio;
    extern float ch0_limit, ch1_limit, ch2_limit;
    uint32_t current_interval = Get_Sample_Interval();

    rs485_printf("Multi-Channel Config Save\r\n");
    rs485_printf("ch0: ratio=%.2f, limit=%.2f\r\n", ch0_ratio, ch0_limit);
    rs485_printf("ch1: ratio=%.2f, limit=%.2f\r\n", ch1_ratio, ch1_limit);
    rs485_printf("ch2: ratio=%.2f, limit=%.2f\r\n", ch2_ratio, ch2_limit);
    rs485_printf("interval: %dms\r\n", current_interval);
    rs485_printf("saving multi-channel parameters to flash...\r\n");

    // 保存三通道配置到Flash (不包含采样间隔)
    uint8_t multi_save_result = Save_Multi_Channel_Config_To_Flash(
        ch0_ratio, ch1_ratio, ch2_ratio,
        ch0_limit, ch1_limit, ch2_limit
    );

    // 使用原有方式保存采样间隔 (保存到原有的单通道配置区域)
    uint8_t interval_save_result = Save_Config_To_Flash(
        Get_Ratio(), Get_Limit(), current_interval
    );

    if(multi_save_result && interval_save_result)
    {
        rs485_printf("Multi-Channel Config save SUCCESS\r\n");
    }
    else
    {
        rs485_printf("Multi-Channel Config save FAILED\r\n");
    }

    Set_System_State(SYSTEM_STATE_IDLE);
}

// 处理"multi config read"命令的函数
void Handle_Multi_Config_Read_Command(void)
{
    Set_System_State(SYSTEM_STATE_CONFIG);

    float flash_ch0_ratio = 0.0f, flash_ch1_ratio = 0.0f, flash_ch2_ratio = 0.0f;
    float flash_ch0_limit = 0.0f, flash_ch1_limit = 0.0f, flash_ch2_limit = 0.0f;
    float flash_ratio = 0.0f, flash_limit = 0.0f;
    uint32_t flash_interval = 5000;

    rs485_printf("Multi-Channel Config Read\r\n");
    rs485_printf("reading multi-channel parameters from flash...\r\n");

    // 从Flash读取三通道配置 (不包含采样间隔)
    uint8_t multi_read_result = Read_Multi_Channel_Config_From_Flash(
        &flash_ch0_ratio, &flash_ch1_ratio, &flash_ch2_ratio,
        &flash_ch0_limit, &flash_ch1_limit, &flash_ch2_limit
    );

    // 使用原有方式读取采样间隔
    uint8_t interval_read_result = Read_Config_From_Flash(
        &flash_ratio, &flash_limit, &flash_interval
    );

    if(multi_read_result && interval_read_result)
    {
        // 读取成功，应用配置到系统
        extern float ch0_ratio, ch1_ratio, ch2_ratio;
        extern float ch0_limit, ch1_limit, ch2_limit;

        ch0_ratio = flash_ch0_ratio;
        ch1_ratio = flash_ch1_ratio;
        ch2_ratio = flash_ch2_ratio;
        ch0_limit = flash_ch0_limit;
        ch1_limit = flash_ch1_limit;
        ch2_limit = flash_ch2_limit;
        Set_Sample_Interval(flash_interval);

        rs485_printf("Multi-Channel Config read SUCCESS\r\n");
        rs485_printf("ch0: ratio=%.2f, limit=%.2f\r\n", ch0_ratio, ch0_limit);
        rs485_printf("ch1: ratio=%.2f, limit=%.2f\r\n", ch1_ratio, ch1_limit);
        rs485_printf("ch2: ratio=%.2f, limit=%.2f\r\n", ch2_ratio, ch2_limit);
        rs485_printf("interval: %dms\r\n", flash_interval);
    }
    else
    {
        rs485_printf("Multi-Channel Config read FAILED\r\n");
        rs485_printf("using current system parameters:\r\n");

        extern float ch0_ratio, ch1_ratio, ch2_ratio;
        extern float ch0_limit, ch1_limit, ch2_limit;

        rs485_printf("ch0: ratio=%.2f, limit=%.2f\r\n", ch0_ratio, ch0_limit);
        rs485_printf("ch1: ratio=%.2f, limit=%.2f\r\n", ch1_ratio, ch1_limit);
        rs485_printf("ch2: ratio=%.2f, limit=%.2f\r\n", ch2_ratio, ch2_limit);
        rs485_printf("interval: %dms\r\n", Get_Sample_Interval());
    }

    Set_System_State(SYSTEM_STATE_IDLE);
}

