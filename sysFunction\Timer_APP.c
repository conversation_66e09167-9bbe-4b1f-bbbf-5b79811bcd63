#include "Timer_APP.h"
#include "LED.h"
#include "ADC.h"
#include "RTC_APP.h"
#include "ADC_APP.h"
#include "Usart_APP.h"
#include "Sdcard_APP.h"

// LED控制变量
static led_blink_mode_t led1_blink_mode = LED_BLINK_OFF;
static uint32_t led1_counter = 0;
static uint8_t led1_current_state = 0;

// ADC采样控制变量
static adc_sampling_state_t adc_sampling_state = ADC_SAMPLING_OFF;
static uint32_t adc_counter = 0;             // ADC计数器(1ms单位)
static uint32_t adc_target_count = 5000;     // 目标计数值(5000ms/1ms=5000)

/**
 * @brief 设置LED1闪烁模式
 * @param mode: 闪烁模式
 */
void Set_LED1_Blink_Mode(led_blink_mode_t mode)
{
    led1_blink_mode = mode;
    led1_counter = 0;
    
    if(mode == LED_BLINK_OFF)
    {
        led1_current_state = 0;
        gpio_bit_reset(LED1_PORT, LED1_PIN);
    }
}

/**
 * @brief 设置LED2状态
 * @param state: LED状态 (0关闭, 1常亮)
 */
void Set_LED2_State(uint8_t state)
{
    if(state)
    {
        gpio_bit_set(LED2_PORT, LED2_PIN);
    }
    else
    {
        gpio_bit_reset(LED2_PORT, LED2_PIN);
    }
}

/**
 * @brief 设置ADC采样状态
 * @param state: 采样状态 (ADC_SAMPLING_OFF/ADC_SAMPLING_ON)
 */
void Set_ADC_Sampling_State(adc_sampling_state_t state)
{
    adc_sampling_state = state;
    adc_counter = 0; // 重置计数器
}

/**
 * @brief 设置ADC采样间隔
 * @param interval_ms: 采样间隔(毫秒)
 */
void Set_ADC_Sample_Interval(uint32_t interval_ms)
{
    adc_target_count = interval_ms; // 计算目标计数值(1ms为基准)
    adc_counter = 0; // 重置计数器
}

/**
 * @brief 定时器LED控制处理函数 - 每1ms调用一次
 * 由硬件层定时器中断调用
 */
void Timer_LED_Handler(void)
{
    if(led1_blink_mode == LED_BLINK_ON)
    {
        led1_counter++;
        if(led1_counter >= 500)
        {
            led1_counter = 0;
            led1_current_state = !led1_current_state;
            if(led1_current_state)
            {
                gpio_bit_set(LED1_PORT, LED1_PIN);
            }
            else
            {
                gpio_bit_reset(LED1_PORT, LED1_PIN);
            }
        }
    }
    else
    {
        gpio_bit_reset(LED1_PORT, LED1_PIN);
    }
}

/**
 * @brief 定时器ADC采样处理函数 - 每1ms调用一次
 * 由硬件层定时器中断调用
 */
void Timer_ADC_Handler(void)
{
    // 采样状态下才进行处理
    if(adc_sampling_state == ADC_SAMPLING_ON)
    {
        adc_counter++;
        if(adc_counter >= adc_target_count)
        {
            adc_counter = 0; // 重置计数器
            static uint32_t sample_count = 0;
            sample_count++;

            // 根据read_mode固定读取一个通道数据，避免数据错乱
            extern uint8_t read_mode;  // 0-电压读取 1-电流读取 2-电阻读取
            extern float ch0_data, ch1_data, ch2_data;

            // 只读取read_mode指定的通道，其他通道保持上次的数据
            if(read_mode == 0) {
                // 电压读取
                ch0_data = (GD30AD3344_AD_Read(GD30AD3344_Channel_4, GD30AD3344_PGA_6V144) - 0.033f) * 4.88888f;
            }
            else if(read_mode == 1) {
                // 电流读取
                ch1_data = (GD30AD3344_AD_Read(GD30AD3344_Channel_5, GD30AD3344_PGA_6V144) - 0.033f) * 10.7801f;
            }
            else if(read_mode == 2) {
                // 电阻读取
                ch2_data = (GD30AD3344_AD_Read(GD30AD3344_Channel_6, GD30AD3344_PGA_6V144) - 0.033f) * 6130.76923f;
            }

            // 应用变比计算实际值（使用当前缓存的数据）
            float ch0_actual = ch0_data * ch0_ratio;
            float ch1_actual = ch1_data * ch1_ratio;
            float ch2_actual = ch2_data * ch2_ratio;

            // 更新latest_data (使用当前读取通道的数据)
            extern adc_data_t latest_data;
            if(read_mode == 0) {
                latest_data.raw_value = (uint16_t)(ch0_data * 1000);
                latest_data.voltage = ch0_data;
            }
            else if(read_mode == 1) {
                latest_data.raw_value = (uint16_t)(ch1_data * 1000);
                latest_data.voltage = ch1_data;
            }
            else if(read_mode == 2) {
                latest_data.raw_value = (uint16_t)(ch2_data * 1000);
                latest_data.voltage = ch2_data;
            }
            latest_data.timestamp = uwTick;
            rtc_parameter_struct rtc_time;
            rtc_current_time_get(&rtc_time);

            // 三通道判限检查
            uint8_t ch0_over_limit = (ch0_actual > ch0_limit) ? 1 : 0;
            uint8_t ch1_over_limit = (ch1_actual > ch1_limit) ? 1 : 0;
            uint8_t ch2_over_limit = (ch2_actual > ch2_limit) ? 1 : 0;
           uint8_t any_over_limit = ch0_over_limit || ch1_over_limit || ch2_over_limit;

            extern output_mode_t current_output_mode;
            if(any_over_limit)
            {
                if(current_output_mode == OUTPUT_MODE_ENCRYPTED)
                {
                    // 加密模式：超限时输出HEX字符串末尾加*
                    Generate_Encrypted_Output_With_Values_OverLimit(&rtc_time, ch0_actual);
                }
                else
                {
                    // 正常模式：超限时也按统一格式输出，但在数据后添加超限标识
                    rs485_printf("report:20%02x-%02x-%02x %02x:%02x:%02x ch0=%.2f,ch1=%.2f,ch2=%.2f OverLimit",
                           rtc_time.year, rtc_time.month, rtc_time.date,
                           rtc_time.hour, rtc_time.minute, rtc_time.second,
                           ch0_actual, ch1_actual, ch2_actual);

                    // 标注具体哪些通道超限
                    if(ch0_over_limit) rs485_printf("[ch0:%.2f]", ch0_limit);
                    if(ch1_over_limit) rs485_printf("[ch1:%.2f]", ch1_limit);
                    if(ch2_over_limit) rs485_printf("[ch2:%.2f]", ch2_limit);
                    rs485_printf("\r\n");
                }
                Set_Over_Limit_State(1); // 设置超限状态并点亮LED2

                // 将超限数据写入overlimit文件 (记录所有超限通道)
                char timestamp_str[32];
                snprintf(timestamp_str, sizeof(timestamp_str), "20%02x-%02x-%02x %02x:%02x:%02x",
                         rtc_time.year, rtc_time.month, rtc_time.date,
                         rtc_time.hour, rtc_time.minute, rtc_time.second);

                if(ch0_over_limit) Write_OverLimit_Data(timestamp_str, ch0_actual, ch0_limit);
                if(ch1_over_limit) Write_OverLimit_Data(timestamp_str, ch1_actual, ch1_limit);
                if(ch2_over_limit) Write_OverLimit_Data(timestamp_str, ch2_actual, ch2_limit);
            }
            else
            {
                if(current_output_mode == OUTPUT_MODE_ENCRYPTED)
                {
                    // 加密模式：输出HEX字符串，使用ch0实际值
                    Generate_Encrypted_Output_With_Values(&rtc_time, ch0_actual);
                }
                else
                {
                    // 正常模式：按要求的格式输出三通道数据
                    rs485_printf("report:20%02x-%02x-%02x %02x:%02x:%02x ch0=%.2f,ch1=%.2f,ch2=%.2f\r\n",
                           rtc_time.year, rtc_time.month, rtc_time.date,
                           rtc_time.hour, rtc_time.minute, rtc_time.second,
                           ch0_actual, ch1_actual, ch2_actual);
                }
                Set_Over_Limit_State(0); // 清除超限状态
            }

            // 将数据写入SD卡（仅在正常模式下写入sample文件夹）
            if(current_output_mode == OUTPUT_MODE_NORMAL)
            {
                char timestamp_str[32];
                snprintf(timestamp_str, sizeof(timestamp_str), "20%02x-%02x-%02x %02x:%02x:%02x",
                         rtc_time.year, rtc_time.month, rtc_time.date,
                         rtc_time.hour, rtc_time.minute, rtc_time.second);

                // 写入三通道数据到sample文件
                Write_Sample_Data_MultiChannel(timestamp_str, ch0_actual, ch1_actual, ch2_actual,
                                              ch0_ratio, ch1_ratio, ch2_ratio);
            }
        }
    }
}
