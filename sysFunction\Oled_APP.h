#ifndef __OLED_APP_H
#define __OLED_APP_H

#include "HeaderFiles.h"

#define OLED_REFRESH_INTERVAL 300

extern char oled1_buffer[128];  // 第一行显示缓冲区
extern char oled2_buffer[128];  // 第二行显示缓冲区
extern uint32_t last_oled_update; // 上次OLED更新时间

void Oled_Task(void);
// void Update_OLED_Display(void);  // 已注释，改为使用按键控制的显示模式
void Get_Time_String(char* time_str);
void Get_Voltage_String(char* voltage_str);
void Display_System_Status(void);
void OLED_App_Init(void);

#endif
