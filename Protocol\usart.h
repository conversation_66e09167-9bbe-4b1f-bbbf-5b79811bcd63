#ifndef __USART_H__ 
#define __USART_H__ 

#include <stdio.h>
#include "gd32f4xx.h"
#include "string.h"
#include "HeaderFiles.h"


/* 串口接收缓冲区定义 */
#define UART_RX_BUFFER_SIZE    128

/* 串口相关标志位 */
#define UART_RX_FLAG_IDLE     (1 << 0)  /* 空闲检测标志 */
#define UART_RX_FLAG_FULL     (1 << 1)  /* 缓冲区满标志 */

/* USART DMA控制宏定义 */
#define USART_DENR_ENABLE     1         /* USART DMA接收使能 */
#define USART_DENR_DISABLE    0         /* USART DMA接收禁用 */


// 定义RS485方向控制引脚
#define RS485_DIR_PORT    GPIOA
#define RS485_DIR_PIN     GPIO_PIN_1  // 假设使用PA1作为方向控制引脚
#define RS485_DIR_CLK     RCU_GPIOA

#define RS485_TX_ENABLE   gpio_bit_set(RS485_DIR_PORT, RS485_DIR_PIN)
#define RS485_RX_ENABLE   gpio_bit_reset(RS485_DIR_PORT, RS485_DIR_PIN)

/* 串口DMA接收结构体 */
typedef struct {
    uint8_t rx_buffer[UART_RX_BUFFER_SIZE];  /* 接收缓冲区 */
    uint16_t rx_count;        /* 接收计数 */
    uint8_t  rx_flag;         /* 接收标志 */
} uart_dma_struct;

extern uint32_t uart_rx_ticks;
extern uint8_t uart_rx_index;
extern uint8_t uart_rx_buffer[128];
extern uint8_t uart_rx_idle_flag;

extern uint32_t rs485_rx_ticks;
extern uint8_t rs485_rx_index;
extern uint8_t rs485_rx_buffer[128];
extern uint8_t rs485_rx_idle_flag;

/* 函数声明 */
void usart0_config(void);
int fputc(int ch, FILE *f);
void rs485_usart1_config(void);
int rs485_printf(const char *format, ...);

#endif
