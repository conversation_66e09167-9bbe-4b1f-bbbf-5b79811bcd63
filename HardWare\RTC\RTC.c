/************************************************************
 * 版权：2025CIMC Copyright。 
 * 文件：RTC.c
 * 作者: <PERSON><PERSON> @ GigaDevice
 * 平台: 2025CIMC IHD-V04
 * 版本: Qiao Qin     2025/4/20     V0.01    original
************************************************************/


#include "RTC.h"

#define RTC_CLOCK_SOURCE_LXTAL		// 强制使用外部晶振
#define BKP_VALUE    0x32F0

rtc_parameter_struct   rtc_initpara;
rtc_alarm_struct  rtc_alarm;
__IO uint32_t prescaler_a = 0, prescaler_s = 0;
uint32_t RTCSRC_FLAG = 0;



/*!
    \brief      main function
    \param[in]  none
    \param[out] none
    \retval     none
*/
void RTC_Init(void)
{
    /* enable PMU clock */
    rcu_periph_clock_enable(RCU_PMU);
    /* enable the access of the RTC registers */
    pmu_backup_write_enable();
    
    /* 确保VBAT域和备份LDO启用 */
    PMU_CTL |= PMU_CTL_BKPWEN;    // 使能备份域写访问
    PMU_CS |= (uint32_t)PMU_CS_LVDF;  // 清除LVD标志
    
    /* 配置备份域LDO为开启状态 */
    uint32_t temp_cs = PMU_CS;
    temp_cs &= ~PMU_CS_BLDOON;         // 清除BLDOON位
    temp_cs |= (uint32_t)PMU_BLDOON_ON; // 设置BLDOON位为开启状态
    PMU_CS = temp_cs;
    
    /* 等待LDO准备就绪 */
    while((PMU_CS & PMU_CS_BLDORF) == RESET)
    {
    }

    rtc_pre_config();

    /* get RTC clock entry selection */
    RTCSRC_FLAG = GET_BITS(RCU_BDCTL, 8, 9);

    /* check if RTC has aready been configured */
    if((BKP_VALUE != RTC_BKP0) || (0x00 == RTCSRC_FLAG))
    {
        /* backup data register value is not correct or not yet programmed
        or RTC clock source is not configured (when the first time the program
        is executed or data in RCU_BDCTL is lost due to Vbat feeding) */
        rtc_setup_default(); // 使用默认时间设置，不要求用户输入
    }
    else
    {
        /* RTC已经配置过，静默处理，不输出任何信息 */
        /* 移除reset source检测输出和时间显示，保持初始化简洁 */
    }
    rcu_all_reset_flag_clear();
}

/*!
    \brief      RTC configuration function
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_pre_config(void)
{
    #if defined (RTC_CLOCK_SOURCE_LXTAL)
          // 设置LXTAL驱动能力为最高
          RCU_BDCTL |= RCU_BDCTL_LXTALDRI;

          // 启动LXTAL外部晶振
          rcu_osci_on(RCU_LXTAL);

          // 等待LXTAL稳定，增加超时时间确保启动成功
          uint32_t timeout = 0x500000;
          while((RESET == rcu_flag_get(RCU_FLAG_LXTALSTB)) && (timeout > 0)) {
              timeout--;
          }

          // 强制使用外部晶振，不提供回退选项
          rcu_rtc_clock_config(RCU_RTCSRC_LXTAL);
          prescaler_s = 0xFF;  // 32768Hz外部晶振分频设置
          prescaler_a = 0x7F;
    #else
    #error RTC clock source must be LXTAL.
    #endif

    rcu_periph_clock_enable(RCU_RTC);
    rtc_register_sync_wait();
}

/*!
    \brief      setup RTC with default time (silent initialization)
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_setup_default(void)
{
    // 确保备份域写访问已启用
    rcu_periph_clock_enable(RCU_PMU);
    pmu_backup_write_enable();

    // 设置默认时间：2025-01-01 00:00:00
    rtc_initpara.factor_asyn = prescaler_a;
    rtc_initpara.factor_syn = prescaler_s;
    rtc_initpara.year = 0x25;        // 2025年
    rtc_initpara.month = 0x01;       // 1月
    rtc_initpara.date = 0x01;        // 1日
    rtc_initpara.hour = 0x00;        // 0时
    rtc_initpara.minute = 0x00;      // 0分
    rtc_initpara.second = 0x00;      // 0秒
    rtc_initpara.day_of_week = RTC_WEDSDAY; // 2025-01-01是周三
    rtc_initpara.display_format = RTC_24HOUR;
    rtc_initpara.am_pm = RTC_AM;

    /* RTC current time configuration */
    if(ERROR == rtc_init(&rtc_initpara)) {
        // 配置失败，静默处理
    } else {
        // 配置成功，保存标志
        RTC_BKP0 = BKP_VALUE;
    }
}

/*!
    \brief      use hyperterminal to setup RTC time and alarm
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_setup(void)
{
    /* setup RTC time value */
		uint32_t tmp_year = 0xFF, tmp_month = 0xFF, tmp_day = 0xFF;
    uint32_t tmp_hh = 0xFF, tmp_mm = 0xFF, tmp_ss = 0xFF;

    // 临时禁用串口中断，避免与RTC输入冲突
    usart_interrupt_disable(USART0, USART_INT_RBNE);

    rtc_initpara.factor_asyn = prescaler_a;
    rtc_initpara.factor_syn = prescaler_s;
    rtc_initpara.year = 0x16;
    rtc_initpara.day_of_week = RTC_SATURDAY;
    rtc_initpara.month = RTC_APR;
    rtc_initpara.date = 0x30;
    rtc_initpara.display_format = RTC_24HOUR;
    rtc_initpara.am_pm = RTC_AM;

    /* current time input */
    printf("=======Configure RTC Time========\n\r");
	 printf("  please set the last two digits of current year:\n\r");
    while(tmp_year == 0xFF)
    {
        tmp_year = usart_input_threshold(99);
        rtc_initpara.year = tmp_year;
    }
    printf("  20%0.2x\n\r", tmp_year);

    printf("  please input month:\n\r");
    while(tmp_month == 0xFF)
    {
        tmp_month = usart_input_threshold(12);
        rtc_initpara.month = tmp_month;
    }
    printf("  %0.2x\n\r", tmp_month);

    printf("  please input day:\n\r");
    while(tmp_day == 0xFF)
    {
        tmp_day = usart_input_threshold(31);
        rtc_initpara.date = tmp_day;
    }
    printf("  %0.2x\n\r", tmp_day);
		
	
    printf("  please input hour:\n\r");
    while (0xFF == tmp_hh)
    {
        tmp_hh = usart_input_threshold(23);
        rtc_initpara.hour = tmp_hh;
    }
    printf("  %0.2x\n\r", tmp_hh);

    printf("  please input minute:\n\r");
    while (0xFF == tmp_mm)
    {
        tmp_mm = usart_input_threshold(59);
        rtc_initpara.minute = tmp_mm;
    }
    printf("  %0.2x\n\r", tmp_mm);

    printf("  please input second:\n\r");
    while (0xFF == tmp_ss)
    {
        tmp_ss = usart_input_threshold(59);
        rtc_initpara.second = tmp_ss;
    }
    printf("  %0.2x\n\r", tmp_ss);

    /* RTC current time configuration */
    if(ERROR == rtc_init(&rtc_initpara))
    {
        printf("\n\r** RTC time configuration failed! **\n\r");
    }
    else
    {
        printf("\n\r** RTC time configuration success! **\n\r");
        rtc_show_time();
        RTC_BKP0 = BKP_VALUE;
    }

    // 重新启用串口中断
    usart_interrupt_enable(USART0, USART_INT_RBNE);

//    /* setup RTC alarm */
//    tmp_hh = 0xFF;
//    tmp_mm = 0xFF;
//    tmp_ss = 0xFF;

//    rtc_alarm_disable(RTC_ALARM0);
//    printf("=======Input Alarm Value=======\n\r");
//    rtc_alarm.alarm_mask = RTC_ALARM_DATE_MASK|RTC_ALARM_HOUR_MASK|RTC_ALARM_MINUTE_MASK;
//    rtc_alarm.weekday_or_date = RTC_ALARM_DATE_SELECTED;
//    rtc_alarm.alarm_day = 0x31;
//    rtc_alarm.am_pm = RTC_AM;

//    /* RTC alarm input */
//    printf("  please input Alarm Hour:\n\r");
//    while (0xFF == tmp_hh){
//        tmp_hh = usart_input_threshold(23);
//        rtc_alarm.alarm_hour = tmp_hh;
//    }
//    printf("  %0.2x\n\r", tmp_hh);

//    printf("  Please Input Alarm Minute:\n\r");
//    while (0xFF == tmp_mm){
//        tmp_mm = usart_input_threshold(59);
//        rtc_alarm.alarm_minute = tmp_mm;
//    }
//    printf("  %0.2x\n\r", tmp_mm);

//    printf("  Please Input Alarm Second:\n\r");
//    while (0xFF == tmp_ss){
//        tmp_ss = usart_input_threshold(59);
//        rtc_alarm.alarm_second = tmp_ss;
//    }
//    printf("  %0.2x", tmp_ss);

//    /* RTC alarm configuration */
//    rtc_alarm_config(RTC_ALARM0,&rtc_alarm);
//    printf("\n\r** RTC Set Alarm Success!  **\n\r");
//    rtc_show_alarm();

//    rtc_interrupt_enable(RTC_INT_ALARM0);
//    rtc_alarm_enable(RTC_ALARM0);
}

/*!
    \brief      display the current time
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_show_time(void)
{
//    uint32_t time_subsecond = 0;
//    uint8_t subsecond_ss = 0,subsecond_ts = 0,subsecond_hs = 0;

    rtc_current_time_get(&rtc_initpara);

    /* get the subsecond value of current time, and convert it into fractional format */
//    time_subsecond = rtc_subsecond_get();
//    subsecond_ss=(1000-(time_subsecond*1000+1000)/400)/100;
//    subsecond_ts=(1000-(time_subsecond*1000+1000)/400)%100/10;
//    subsecond_hs=(1000-(time_subsecond*1000+1000)/400)%10;

    printf("Time:20%02x-%02x-%02x %02x:%02x:%02x\r\n", 
           rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
           rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
}

/*!
    \brief      display the alram value
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_show_alarm(void)
{
    rtc_alarm_get(RTC_ALARM0,&rtc_alarm);
    printf("The alarm: %0.2x:%0.2x:%0.2x \n\r", rtc_alarm.alarm_hour, rtc_alarm.alarm_minute,\
           rtc_alarm.alarm_second);
}

/*!
    \brief      get the input character string and check if it is valid
    \param[in]  none
    \param[out] none
    \retval     input value in BCD mode
*/
uint8_t usart_input_threshold(uint32_t value)
{
    uint32_t index = 0;
    uint32_t tmp[2] = {0, 0};

    // 清除可能残留的接收标志
    while(SET == usart_flag_get(USART0, USART_FLAG_RBNE))
    {
        usart_data_receive(USART0);  // 清空接收缓冲区
    }

    while (index < 2)
    {
        while (RESET == usart_flag_get(USART0, USART_FLAG_RBNE));
        tmp[index++] = usart_data_receive(USART0);
        if ((tmp[index - 1] < 0x30) || (tmp[index - 1] > 0x39))
        {
            printf("\n\r please input a valid number between 0 and 9 \n\r");
            index--;
        }
    }

    index = (tmp[1] - 0x30) + ((tmp[0] - 0x30) * 10);
    if (index > value)
    {
        printf("\n\r please input a valid number between 0 and %d \n\r", value);
        return 0xFF;
    }

    index = (tmp[1] - 0x30) + ((tmp[0] - 0x30) <<4);
    return index;
}


